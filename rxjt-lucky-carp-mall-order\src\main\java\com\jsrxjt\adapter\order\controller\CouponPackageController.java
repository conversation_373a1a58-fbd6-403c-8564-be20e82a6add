package com.jsrxjt.adapter.order.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseParam;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageDelRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPackageListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.CouponPayDetailPageRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackageListResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.CouponPackagePageDTO;
import com.jsrxjt.mobile.api.order.dto.response.SelCouponPayDetailResponseDTO;
import com.jsrxjt.mobile.biz.order.coupon.CouponPackageCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 卡包接口
 * <AUTHOR>
 * @date 2025/07/22
 */
@RestController
@RequestMapping("/v1/couponPackage")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "卡包接口", description = "卡包相关接口")
public class CouponPackageController {

    private final CouponPackageCaseService couponPackageCaseService;

    /**
     * 卡包列表接口
     */
    @GetMapping("/couponPackageList")
    @Operation(summary = "卡包列表", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<CouponPackagePageDTO<CouponPackageListResponseDTO>> couponPackageList(@Valid CouponPackageListRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        return BaseResponse.succeed(couponPackageCaseService.getCouponPackageList(customerId, request));
    }

    /**
     * 卡包列表接口
     */
    @GetMapping("/recycleBinList")
    @Operation(summary = "回收站列表", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<CouponPackageListResponseDTO>> recycleBinList(@Valid BaseParam request) {
        return BaseResponse.succeed(couponPackageCaseService.recycleBinList(request));
    }


    @GetMapping("/getCouponPackageDetail")
    @Operation(summary = "卡包详情", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<CouponPackageDetailResponseDTO> getCouponPackageDetail(@RequestParam Long couponPackageId){
        return BaseResponse.succeed(couponPackageCaseService.getCouponPackageDetail(couponPackageId));
    }

    /**
     * 消费明细
     */
    @PostMapping("/consumeList")
    @Operation(summary = "消费明细", description = "消费明细接口")
    @VerifySign(hasToken = true)
    public PageDTO<SelCouponPayDetailResponseDTO> consumeList(@RequestBody @Valid CouponPayDetailPageRequestDTO request){
        return couponPackageCaseService.consumeList(request);
    }

    @PostMapping("/putInRecycleBin")
    @Operation(summary = "卡包放入回收站", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<?> putInRecycleBin(@RequestBody @Valid CouponPackageDelRequestDTO request){
        couponPackageCaseService.putInRecycleBin(request);
        return BaseResponse.succeed();
    }

    @PostMapping("/restoreRecycleBin")
    @Operation(summary = "回收站恢复", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<?> restoreRecycleBin(@RequestBody @Valid CouponPackageDelRequestDTO request){
        couponPackageCaseService.restoreRecycleBin(request);
        return BaseResponse.succeed();
    }

    @PostMapping("/deleteCouponPackage")
    @Operation(summary = "回收站删除", description = "卡包列表接口")
    @VerifySign(hasToken = true)
    public BaseResponse<?> deleteCouponPackage(@RequestBody @Valid CouponPackageDelRequestDTO request){
        couponPackageCaseService.deleteCouponPackage(request);
        return BaseResponse.succeed();
    }
}
