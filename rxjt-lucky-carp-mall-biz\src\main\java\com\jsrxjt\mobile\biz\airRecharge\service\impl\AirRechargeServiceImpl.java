package com.jsrxjt.mobile.biz.airRecharge.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.mobile.api.airRecharge.AirRechargeRequest;
import com.jsrxjt.mobile.api.customer.response.AirRechargeInfoResponse;
import com.jsrxjt.mobile.biz.airRecharge.service.AirRechargeService;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.domain.airRecharge.entity.AirRechargeEntity;
import com.jsrxjt.mobile.domain.airRecharge.repository.AirRechargeRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 空中充值
 * @Author: ywt
 * @Date: 2025-08-26 15:07
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class AirRechargeServiceImpl implements AirRechargeService {
    private final AirRechargeRepository airRechargeRepository;
    private final CustomerService customerService;

    @Override
    public AirRechargeInfoResponse getAirRechargeInfo() {
        List<AirRechargeEntity> list = null;
        Long customId = StpUtil.getLoginIdAsLong();
        CustomerEntity entity = customerService.getCustomerById(customId);
        if (Objects.nonNull(entity) && StringUtils.isNotEmpty(entity.getPhone())) {
//            list = airRechargeRepository.getAirRechargeInfo("13028439858");
            list = airRechargeRepository.getAirRechargeInfo(entity.getPhone());
        }
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        List<AirRechargeEntity> sortedList = list.stream()
                .sorted(Comparator.comparing(
                        AirRechargeEntity::getTruePrice,
                        Comparator.nullsLast(Comparator.reverseOrder())
                ))
                .collect(Collectors.toList());
        AirRechargeEntity bigOne = sortedList.get(0);//取金额最大的一个
        return BeanUtil.copyProperties(bigOne, AirRechargeInfoResponse.class);
    }

    @Override
    public void updateAirRechargeStatus(AirRechargeRequest request) {
        airRechargeRepository.updateAirRechargeStatus(request.getId());
    }
}
