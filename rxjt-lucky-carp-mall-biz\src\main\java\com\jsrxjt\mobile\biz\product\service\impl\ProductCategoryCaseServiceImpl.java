package com.jsrxjt.mobile.biz.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.advertisement.types.AdvertisementTypeEnum;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.product.dto.CategoryProductDTO;
import com.jsrxjt.mobile.api.product.dto.ProductBaseInfoDTO;
import com.jsrxjt.mobile.api.product.dto.ProductCategoryDTO;
import com.jsrxjt.mobile.api.product.dto.request.CategoryDetailRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.MultiCategoryRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.CategorySearchResponseDTO;
import com.jsrxjt.mobile.biz.product.service.ProductCategoryCaseService;
import com.jsrxjt.mobile.domain.advertisement.entity.AdvertisementEntity;
import com.jsrxjt.mobile.domain.advertisement.repository.AdvertisementRepository;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import com.jsrxjt.mobile.domain.product.entity.ProductCategory;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import com.jsrxjt.mobile.domain.product.repository.ProductBaseInfoRepository;
import com.jsrxjt.mobile.domain.product.repository.ProductCategoryRepository;
import com.jsrxjt.mobile.domain.product.service.ProductSpuBaseInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品分类用例服务实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductCategoryCaseServiceImpl implements ProductCategoryCaseService {

    private final ProductCategoryRepository productCategoryRepository;
    private final AdvertisementRepository advertisementRepository;
    private final ProductBaseInfoRepository productBaseInfoRepository;

    private final ContentRegionService contentRegionService;

    private final ProductSpuBaseInfoService productSpuBaseInfoService;

    // 每个二级分类展示的产品数量
    private static final Integer PRODUCT_LIMIT_PER_CATEGORY = 20;
    // 多个二级类目组合查询展示的数量
    private static final Integer PRODUCT_LIMIT_PER_CATEGORIES = 200;

    @Override
    public List<ProductCategoryDTO> getFirstLevelCategories() {
        List<ProductCategory> categories = productCategoryRepository.findFirstLevelCategories();
        return convertToCategoryDTOList(categories);
    }

    @Override
    public CategorySearchResponseDTO getSecondLevelCategoryDetails(CategoryDetailRequestDTO requestDTO) {
        Long parentCategoryId = requestDTO.getParentCategoryId();
        log.info("开始获取二级分类详情，一级分类ID: {}", parentCategoryId);
        CategorySearchResponseDTO response = new CategorySearchResponseDTO();

        // 1. 获取二级分类列表
        List<ProductCategory> secondLevelCategories = productCategoryRepository
                .findSecondLevelCategoriesByParentId(parentCategoryId);
        if (secondLevelCategories.isEmpty()) {
            log.info("一级分类ID: {} 下没有二级分类", parentCategoryId);
            return new CategorySearchResponseDTO();
        }

        // 2. 获取广告位列表（一级分类对应的广告位）
        List<AdvertisementEntity> adList = advertisementRepository
                .getAdvertisementByTypeAndId(AdvertisementTypeEnum.ADV_CATEGORY.getCode(),
                parentCategoryId);

        if (CollUtil.isNotEmpty(adList)) {
            List<AdvertisementInfoDTO> advertisementInfoDTOList = new ArrayList<>();
            adList.forEach(item -> {
                if (item.getIsNationwide() == 1
                        || contentRegionService.isOnlineInRegion(item.getId(),
                        ContentcenterTypeEnum.CONTENT_ADV.getCode(),
                        requestDTO.getRegionId())) {
                    AdvertisementInfoDTO advertisementInfoDTO = new AdvertisementInfoDTO();
                    BeanUtils.copyProperties(item, advertisementInfoDTO);
                    advertisementInfoDTOList.add(advertisementInfoDTO);
                }
            });
            response.setAdvertisements(advertisementInfoDTOList);
        }



        // 3. 构建结果
        List<CategoryProductDTO> details = new ArrayList<>(secondLevelCategories.size());

        for (ProductCategory category : secondLevelCategories) {
            CategoryProductDTO detailDTO = new CategoryProductDTO();
            detailDTO.setCategoryId(category.getId());
            detailDTO.setCategoryName(category.getCategoryName());

            // 查询该分类下的产品
            List<ProductSpuBaseInfo> products = productBaseInfoRepository.findProductsByCategoryId(
                    String.valueOf(category.getId()), PRODUCT_LIMIT_PER_CATEGORY);
            List<ProductSpuBaseInfo> list = productSpuBaseInfoService.filterByRegion(requestDTO.getRegionId(), products);
            productSpuBaseInfoService.setPromotionLabels(list);

            // 转换产品列表
            detailDTO.setProducts(convertToProductInfoDTOList(list));

            details.add(detailDTO);
        }
        response.setCategoryDetails(details);

        log.info("获取二级分类详情完成，一级分类ID: {}，二级分类数量: {}", parentCategoryId, details.size());
        return response;
    }

    @Override
    public List<ProductBaseInfoDTO> findProductsByCategoryIds(MultiCategoryRequestDTO requestDTO) {
        // 查询该分类下的产品
        List<ProductSpuBaseInfo> products = productBaseInfoRepository.findProductsByCategoryIds(
                requestDTO.getCategoryIds(), PRODUCT_LIMIT_PER_CATEGORIES);
        // 区域过滤
        List<ProductSpuBaseInfo> list = productSpuBaseInfoService.filterByRegion(requestDTO.getRegionId(), products);
        productSpuBaseInfoService.setPromotionLabels(list);

        // 转换产品列表
        return convertToProductInfoDTOList(list);
    }

    private List<ProductCategoryDTO> convertToCategoryDTOList(List<ProductCategory> categories) {
        if (categories == null || categories.isEmpty()) {
            return new ArrayList<>();
        }

        return categories.stream()
                .map(this::convertToCategoryDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将领域实体转换为CategoryDTO
     */
    private ProductCategoryDTO convertToCategoryDTO(ProductCategory category) {
        ProductCategoryDTO dto = new ProductCategoryDTO();
        BeanUtil.copyProperties(category, dto);
        return dto;
    }


    /**
     * 将产品实体列表转换为DTO列表
     */
    private List<ProductBaseInfoDTO> convertToProductInfoDTOList(List<ProductSpuBaseInfo> products) {
        if (products == null || products.isEmpty()) {
            return new ArrayList<>();
        }

        return products.stream()
                .map(this::convertToProductInfoDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将产品实体转换为DTO
     */
    private ProductBaseInfoDTO convertToProductInfoDTO(ProductSpuBaseInfo entity) {
        ProductBaseInfoDTO dto = new ProductBaseInfoDTO();
        BeanUtil.copyProperties(entity, dto);
        return dto;
    }
}