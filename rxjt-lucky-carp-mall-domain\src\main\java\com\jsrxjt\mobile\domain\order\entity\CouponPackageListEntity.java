package com.jsrxjt.mobile.domain.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CouponPackageListEntity {

    /**
     * 卡包id(t_order_delivery表的id)
     */
    private Long couponPackageId;

    /**
     * 卡券名称
     */
    private String couponName;

    /**
     * 面额
     */
    private BigDecimal amount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 福鲤圈的核销类型，1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码  4面值+卡号卡密 5面值+卡号卡密+兑换码 6面值+卡号卡密+一维二维码 7面值+卡密+一维二维码 8面值+链接 9自发券(面值+提货券形式) 10跳转品牌方h5核销页"
     */
    private Integer flqType;

    /**
     * 卡券卡号
     */
    private String couponCode;

    /**
     * 卡券卡密
     */
    private String couponPin;

    /**
     * 卡券crc
     */
    private String couponCrc;

    /**
     * 卡券兑换链接
     */
    private String couponUrl;

    /**
     * 卡券兑换短链接密码
     */
    private String couponUrlPass;
}
