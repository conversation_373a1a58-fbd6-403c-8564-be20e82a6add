package com.jsrxjt.mobile.biz.app.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.app.request.AlipayAppOrderRequest;
import com.jsrxjt.mobile.api.app.request.AlipayAppPreOrderRequest;
import com.jsrxjt.mobile.api.app.request.AlipayAppRequest;
import com.jsrxjt.mobile.api.app.response.*;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.app.AlipayAppService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.app.entity.AlipayTabCategoryEntity;
import com.jsrxjt.mobile.domain.app.entity.AppCouponExplainEntity;
import com.jsrxjt.mobile.domain.app.entity.AppCouponGoodsEntity;
import com.jsrxjt.mobile.domain.app.entity.AppCouponGoodsSkuEntity;
import com.jsrxjt.mobile.domain.app.repository.AlipayTabCategoryRepository;
import com.jsrxjt.mobile.domain.app.repository.AppCouponGoodsRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.product.entity.SpuLimitStrategyEntity;
import com.jsrxjt.mobile.domain.product.entity.SpuLimitStrategyRegionEntity;
import com.jsrxjt.mobile.domain.product.repository.SpuLimitStrategyRegionRepository;
import com.jsrxjt.mobile.domain.product.repository.SpuLimitStrategyRepository;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class AlipayAppServiceImpl implements AlipayAppService {

    private final AlipayTabCategoryRepository alipayTabCategoryRepository;

    private final ProductSkuSellRegionService productSkuSellRegionService;

    private final AppCouponGoodsRepository appCouponGoodsRepository;

    private final SpuLimitStrategyRepository spuLimitStrategyRepository;

    private final SpuLimitStrategyRegionRepository spuLimitStrategyRegionRepository;

    private final CustomerRepository customerRepository;

    private final OrderCaseService orderCaseService;

    private final OrderRepository orderRepository;

    private final RegionRepository regionRepository;

    /**
     * 获取支付宝红包分类
     *
     * @return
     */
    @Override
    public List<AlipayAppCatResponse> getAlipayAppCats() {
        List<AlipayTabCategoryEntity> usedTabCategory = alipayTabCategoryRepository.getUsedTabCategory();
        return BeanUtil.copyToList(usedTabCategory, AlipayAppCatResponse.class);
    }

    /**
     * 获取支付宝红包详情信息
     *
     * @param request
     * @return
     */
    @Override
    public AlipayAppResponse getAlipayAppInfo(AlipayAppRequest request) {
        AlipayAppResponse response = new AlipayAppResponse();
        Byte isSelect = request.getIsSelect();
        Integer regionId = request.getRegionId();
        Long alipayTabCatId = request.getAlipayTabCatId();
        Long appSpuId = request.getAppSpuId();
        if(isSelect == 0  && alipayTabCatId == null){
            log.error("alipayTabCatId is null");
            return null;
        }
        if(isSelect == 1 && appSpuId != null) {
            AppCouponGoodsEntity appCouponGoodsEntity = appCouponGoodsRepository.getAppCouponGoodsInfo(request.getAppSpuId());
            alipayTabCatId = appCouponGoodsEntity.getAlipayTabCatId();
        }
        AlipayTabCategoryEntity alipayTabCategoryEntity = alipayTabCategoryRepository.getTabCategoryDetail(alipayTabCatId);
        response.setAlipayTabCatId(alipayTabCatId);
        response.setAttribute(alipayTabCategoryEntity.getAttribute());
        List<AppCouponGoodsEntity> appCouponGoodsListByType = appCouponGoodsRepository.getAppCouponGoodsListByTypeAndCatId(2,alipayTabCatId);
        if(CollectionUtil.isEmpty(appCouponGoodsListByType)){
            log.error("appCouponGoodsListByType is empty");
            return null;
        }
        List<AppCouponGoodsEntity> newAppCouponGoodsList = new ArrayList<>();
        for (AppCouponGoodsEntity entity : appCouponGoodsListByType) {
            boolean sellableInRegion = productSkuSellRegionService.isSellableInRegion(entity.getAppSpuId(), null, ProductTypeEnum.COUPON_APP, regionId);
            if(sellableInRegion){
                newAppCouponGoodsList.add(entity);
            }
        }
        if(CollectionUtil.isEmpty(newAppCouponGoodsList)){
            log.error("newAppCouponGoodsList is empty");
            return null;
        }
        //初始化 未选中
        if(isSelect == 0){
            appSpuId = newAppCouponGoodsList.get(0).getAppSpuId();
        }
        List<AlipayAppSpuResponse> alipayAppSpuResponseList =BeanUtil.copyToList(newAppCouponGoodsList, AlipayAppSpuResponse.class);
        for (AlipayAppSpuResponse spuNameResponse : alipayAppSpuResponseList) {
            if(spuNameResponse.getAppSpuId().equals(appSpuId)){
                //如果选中
                if(isSelect == 1){
                    spuNameResponse.setIsSpuSelect((byte)1);
                }
            }
            //只放入选中的说明
            AppCouponGoodsEntity appCouponGoodsEntity = appCouponGoodsRepository.getAppCouponGoodsInfo(spuNameResponse.getAppSpuId());
            List<AppCouponExplainEntity> explainEntityList = appCouponGoodsEntity.getExplainEntityList();
            spuNameResponse.setExplainEntityList(BeanUtil.copyToList(explainEntityList, AlipayAppExplainResponse.class));
            spuNameResponse.setSubscriptUrl(appCouponGoodsEntity.getSubscriptUrl());
        }

        //获取sku信息
        List<AppCouponGoodsSkuEntity> appCouponGoodsSkuListBySpuId = appCouponGoodsRepository.getAppCouponGoodsSkuListBySpuId(appSpuId);
        List<AlipayAppSkuResponse> alipayAppInfoResponseList = BeanUtil.copyToList(appCouponGoodsSkuListBySpuId, AlipayAppSkuResponse.class);
        for (AlipayAppSkuResponse alipayAppSkuResponse : alipayAppInfoResponseList) {
            if(request.getAppSkuId() !=null && alipayAppSkuResponse.getAppSkuId().equals(request.getAppSkuId())){
                //如果选中
                if(isSelect == 1 ){
                    alipayAppSkuResponse.setIsSkuSelect((byte)1);
                }
            }
        }
        //获取多元使用场景
        SpuLimitStrategyRegionEntity byProductAndRegion = spuLimitStrategyRegionRepository.findByProductAndRegion(appSpuId, ProductTypeEnum.COUPON_APP.getType(), regionId);
        if(byProductAndRegion != null){
            SpuLimitStrategyEntity limitStrategy = spuLimitStrategyRepository
                    .findByStrategyId(byProductAndRegion.getLimitStrategyId(),
                            ProductTypeEnum.COUPON_APP.getType(), appSpuId);
            if(limitStrategy != null){
                response.setDiverseUsageScene(limitStrategy.getDiverseUsageScene());
            }
        }
        response.setAlipayAppSkuResponseList(alipayAppInfoResponseList);
        response.setAlipayAppSpuResponseList(alipayAppSpuResponseList);
        return response;
    }

    /**
     * 支付宝红包预充值是否满足条件
     *
     * @param request
     * @return
     */
    @Override
    public Boolean checkImmediatePayment(AlipayAppPreOrderRequest request) {
        return checkAppPreOrder( request, null, null);
    }

    /**
     * 支付宝红包充值确认
     *
     * @param request
     * @return
     */
    @Override
    public OrderInfoEntity payAppOrder(AlipayAppOrderRequest request) {
        AppCouponGoodsEntity appCouponGoodsEntity = appCouponGoodsRepository.getAppCouponGoodsInfo(request.getAppSpuId());
        if(appCouponGoodsEntity == null ){
            log.error("appCouponGoodsEntity is null");
            throw new BizException("红包SPU信息不存在");
        }
        AppCouponGoodsSkuEntity appCouponGoodsSkuEntity = appCouponGoodsRepository.findSkuBySkuId(request.getAppSkuId());
        if (appCouponGoodsSkuEntity == null ) {
            log.error("appCouponGoodsSkuEntity is null ");
            throw new BizException("红包SKU信息不存在");
        }
        //数据验证
        AlipayAppPreOrderRequest alipayAppPreOrderRequest = BeanUtil.toBean(request, AlipayAppPreOrderRequest.class);
        checkAppPreOrder(alipayAppPreOrderRequest, appCouponGoodsEntity, appCouponGoodsSkuEntity);
        //创建订单
        CreateOrderDTO createOrderDTO = buildOrderDTO(request, appCouponGoodsEntity, appCouponGoodsSkuEntity);
        OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO);
        return orderInfoEntity;
    }

    /*@Override
    public PageDTO<AlipayVoucherRechargeRecordResponse> getRechargeRecords(AlipayVoucherRechargeRecordRequest request) {
        // 当月第一天00:00:00 - 最后一天23:59:59
        LocalDateTime startTime = request.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime endTime = request.getYearMonth().atEndOfMonth().atTime(23, 59, 59);
        OrderListQuery query = OrderListQuery.builder()
                .customerId(request.getCustomerId())
                .createTimeStart(startTime)
                .createTimeEnd(endTime)
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();
        PageDTO<OrderInfoEntity> orderPage = orderRepository.findByFlatProductTypeAndDateRange(query, FlatProductTypeEnum.ALIPAY_RED_PACKET, null);

        List<AlipayVoucherRechargeRecordResponse> recordResponseList = orderPage.getRecords().stream()
                .map(this::convertToAlipayVoucherRechargeRecordResponse)
                .collect(Collectors.toList());

        return PageDTO.<AlipayVoucherRechargeRecordResponse>builder()
                .records(recordResponseList)
                .total(orderPage.getTotal())
                .current(orderPage.getCurrent())
                .size(orderPage.getSize())
                .build();
    }*/


    private AlipayVoucherRechargeRecordResponse convertToAlipayVoucherRechargeRecordResponse(OrderInfoEntity order) {
        AlipayVoucherRechargeRecordResponse response = new AlipayVoucherRechargeRecordResponse();
        response.setOrderNo(order.getOrderNo());
        response.setPaymentAmount(order.getPaymentAmount());
        response.setRechargeAccount(order.getRechargeAccount());
        response.setOrderStatus(order.getOrderStatus());
        response.setDeliveryStatus(order.getDeliveryStatus());
        response.setCreateTime(order.getCreateTime());
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            OrderItemEntity orderItem = order.getOrderItems().get(0);
            response.setProductName(orderItem.getProductName());
            response.setProductSpuId(orderItem.getProductId());
            response.setProductSkuId(orderItem.getProductSkuId());
            response.setProductLogo(orderItem.getProductLogo());
        }
        return response;
    }

    @Override
    public String getLastRechargeAccount(Long customerId) {
        return orderRepository.getLastRechargeAccount(customerId, OrderChannelEnum.ALIPAY_RED_ENVELOPE);
    }

    private  CreateOrderDTO buildOrderDTO(AlipayAppOrderRequest request,
                                           AppCouponGoodsEntity spuEntity,
                                           AppCouponGoodsSkuEntity skuEntity
                                           ){
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setCustomerId(request.getCustomerId());
        createOrderDTO.setProductType(ProductTypeEnum.COUPON_APP.getType());
        createOrderDTO.setProductSpuId(spuEntity.getAppSpuId());
        createOrderDTO.setProductSkuId(skuEntity.getAppSkuId());
        createOrderDTO.setLongitude(request.getLongitude());
        createOrderDTO.setLatitude(request.getLatitude());
        createOrderDTO.setRegionId(request.getRegionId());
        createOrderDTO.setExternalOrderNo("");
        createOrderDTO.setThirdId("");
        createOrderDTO.setRechargeAccount(request.getRechargeAccount());
        createOrderDTO.setExternalAppProductPrice(skuEntity.getPlatformPrice());
        return createOrderDTO;
    }

    private Boolean checkAppPreOrder(AlipayAppPreOrderRequest request,
                                     AppCouponGoodsEntity spuEntity,
                                     AppCouponGoodsSkuEntity skuEntity
                                     ){
        Long customerId = StpUtil.getLoginIdAsLong();
        //获取客户信息
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if(customerEntity == null || customerEntity.getStatus() != 0){
            log.error("customerEntity is null or status != 0");
            throw new BizException("会员不存在或禁用");
        }
        if(StrUtil.isBlank(customerEntity.getPhone())){
            log.error("手机号为空");
            throw new BizException("手机号为空");
        }
        //是否勾选充值说明
        Boolean isCheckedRecharge = request.getIsCheckedRecharge();
        if(isCheckedRecharge == null){
            log.error("请勾选-充值使用说明");
            throw new BizException("请先勾选充值使用说明");
        }
        //获取分类信息判断
        Long alipayTabCatId = request.getAlipayTabCatId();
        AlipayTabCategoryEntity tabCategoryDetail = alipayTabCategoryRepository.getTabCategoryDetail(alipayTabCatId);
        if(tabCategoryDetail == null){
            log.error("tabCategoryDetail is null");
            throw new BizException("分类信息获取失败");
        }
        String categoryName = tabCategoryDetail.getCategoryName();
        //todo 中文判断？银行专项红包说明是否勾选
        if(categoryName.equals("银行专项立减金")){
            if(!request.getIsCheckedBank()){
                throw new BizException("请先勾选银行专项红包说明");
            }
        }
        //判断区域是否可买
        RegionEntity region = regionRepository.getCurrentRegion(StpUtil.getLoginIdAsLong());
        Integer regionId = region.getId();
        if(!productSkuSellRegionService.isSellableInRegion(request.getAppSpuId(), request.getAppSkuId(), ProductTypeEnum.COUPON_APP, regionId)){
            log.error("区域不可用");
            throw new BizException("区域不可用");
        }
        //判断spu是否禁用
        if(spuEntity == null ){
            AppCouponGoodsEntity appCouponGoodsEntity = appCouponGoodsRepository.getAppCouponGoodsInfo(request.getAppSpuId());
            if(appCouponGoodsEntity == null || appCouponGoodsEntity.getStatus() == 0){
                log.error("appCouponGoodsEntity is null or status != 0");
                throw new BizException("红包SPU信息不存在或禁用");
            }
        }else{
            if(spuEntity.getStatus() == 0){
                log.error("spuEntity is null or status != 0");
                throw new BizException("红包SPU信息不存在或禁用");
            }
        }

        //判断sku是否禁用
        if(skuEntity == null) {
            AppCouponGoodsSkuEntity appCouponGoodsSkuEntity = appCouponGoodsRepository.findSkuBySkuId(request.getAppSkuId());
            if (appCouponGoodsSkuEntity == null || appCouponGoodsSkuEntity.getStatus() == 0) {
                log.error("appCouponGoodsSkuEntity is null or status != 0");
                throw new BizException("红包SKU信息不存在或禁用");
            }
        }else{
            if(skuEntity.getStatus() == 0){
                log.error("skuEntity is null or status != 0");
                throw new BizException("红包SKU信息不存在或禁用");
            }
        }
        return true;
    }
}
