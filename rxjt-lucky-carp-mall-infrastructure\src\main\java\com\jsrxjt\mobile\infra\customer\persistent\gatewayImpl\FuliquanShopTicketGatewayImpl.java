package com.jsrxjt.mobile.infra.customer.persistent.gatewayImpl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.customer.gateway.FuliquanShopTicketGateway;
import com.jsrxjt.mobile.domain.customer.gateway.request.SendTicketRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.request.ShopTicketListRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.SendTicketResponseDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.ShopTicketListResponseDTO;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.infra.pickplatform.util.RxMemberSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 福鲤圈门店优惠券接口
 * @Author: zy
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FuliquanShopTicketGatewayImpl implements FuliquanShopTicketGateway {

    protected final HttpClientGateway httpClientGateway;

    @Value("${flq.shop.ticket.key}")
    private String key;

    @Value("${flq.shop.ticket.url:https://minirx.tongkask.com}")
    private String url;

    @Value("${flq.shop.ticket.sendCoupon:/FuliquanV2/Vip/sendCoupon}")
    private String sendCoupon;

    @Value("${flq.shop.ticket.getCouponList:/FuliquanV2/Vip/getCouponList}")
    private String getCouponList;

    private int connectTimeout = 3000;

    private int readTimeout = 5000;

    private static final Integer SUCCESS_CODE = 0;

    /**
     * 发券(生日券)
     *
     * @param requestDTO
     * @return
     */
    @Override
    public SendTicketResponseDTO sendCoupon(SendTicketRequestDTO requestDTO) {
        SendTicketResponseDTO dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();

        requestMap.put("nonce", requestDTO.getNonce());
        requestMap.put("timestamp", requestDTO.getTimestamp());
        requestMap.put("vipid", requestDTO.getVipId());

        String requestSign = RxMemberSignUtil.getSign(requestMap, key);
        requestMap.put("sign", requestSign);
        try {
            String resultStr = httpClientGateway.doPost(url + sendCoupon, requestMap, connectTimeout, readTimeout);
            JSONObject result = JSON.parseObject(resultStr);
            if (Objects.equals(result.getInteger("code"), SUCCESS_CODE) && Objects.nonNull(result.get("data"))) {
                dataResponse = JSON.parseObject(JSON.toJSONString(result.get("data")), SendTicketResponseDTO.class);
            }
        } catch (Exception e) {
            log.error("福鲤圈领取生日券接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }

    /**
     * 获取门店优惠券列表
     *
     * @param requestDTO
     * @return
     */
    @Override
    public PageDTO<ShopTicketListResponseDTO> getShopTicketList(ShopTicketListRequestDTO requestDTO) {
        List<ShopTicketListResponseDTO> dataResponseList = null;
        Long total = 0L;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("vipid", requestDTO.getVipid());
        requestMap.put("page", requestDTO.getPage());
        requestMap.put("size", requestDTO.getSize());
        requestMap.put("timestamp", requestDTO.getTimestamp());
        requestMap.put("nonce", requestDTO.getNonce());
        String requestSign = RxMemberSignUtil.getSign(requestMap, key);
        requestMap.put("sign", requestSign);
        try {
            String resultStr = httpClientGateway.doPost(url + getCouponList, requestMap, connectTimeout, readTimeout);
            JSONObject result = JSON.parseObject(resultStr);
            if (Objects.equals(result.getInteger("code"), SUCCESS_CODE) && Objects.nonNull(result.get("data"))) {
                String jsonString = JSON.toJSONString(result.get("data"));
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                total = Long.valueOf(jsonObject.get("total").toString());
                Object object = jsonObject.get("list");
                //转成list
                dataResponseList = JSONArray.parseArray(object.toString(), ShopTicketListResponseDTO.class);

            }
        } catch (Exception e) {
            log.error("门店券接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return PageDTO.build(dataResponseList, total, requestDTO.getSize(), requestDTO.getPage());
    }

}
