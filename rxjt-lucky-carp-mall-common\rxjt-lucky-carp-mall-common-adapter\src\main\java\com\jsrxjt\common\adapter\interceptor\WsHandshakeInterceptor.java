package com.jsrxjt.common.adapter.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;

@Slf4j
@Component
public class WsHandshakeInterceptor implements HandshakeInterceptor {

    @Value("${sa-token.token-name}")
    private String AUTH_HEADER;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request,
                                   ServerHttpResponse response,
                                   WebSocketHandler wsHandler,
                                   Map<String, Object> attributes) throws Exception {

        // 从请求中提取token
        String token = extractToken(request);
        if (token == null) {
            log.warn("WebSocket连接拒绝: 未提供Token");
            response.setStatusCode(HttpStatus.UNAUTHORIZED);
            return false;
        }
        URI uri = request.getURI();
        MultiValueMap<String, String> params =
                UriComponentsBuilder.fromUri(uri).build().getQueryParams();
        String payCode = params.getFirst("payCode");
        // 从token中获取用户信息
        Object customerId = StpUtil.getLoginIdByToken(token);
        if (customerId == null || StringUtils.isBlank(payCode)) {
            log.warn("WebSocket连接拒绝: 参数缺失 customerId {}, payCode {}", customerId, payCode);
            response.setStatusCode(HttpStatus.BAD_REQUEST);
            return false;
        }
        attributes.put("customerId", customerId);
        attributes.put("token", token);
        attributes.put("payCode", payCode);

        log.info("WebSocket握手成功, 用户ID: {}", customerId);
        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request,
                               ServerHttpResponse response,
                               WebSocketHandler wsHandler,
                               Exception exception) {
        // 握手完成后执行，可以用于清理工作
    }

    private String extractToken(ServerHttpRequest request) {
        if (request instanceof ServletServerHttpRequest) {
            ServletServerHttpRequest servletRequest = (ServletServerHttpRequest) request;
            // 从Header获取
            String authHeader = servletRequest.getServletRequest().getHeader(AUTH_HEADER);
            if (authHeader != null && !authHeader.trim().isEmpty()) {
                return authHeader.trim();
            }
        }
        return null;
    }
}
