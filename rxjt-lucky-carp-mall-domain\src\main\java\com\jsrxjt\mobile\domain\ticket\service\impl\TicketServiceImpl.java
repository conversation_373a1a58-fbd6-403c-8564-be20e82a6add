package com.jsrxjt.mobile.domain.ticket.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketRegionEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketBrandRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketProductRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRegionRepository;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRepository;
import com.jsrxjt.mobile.domain.ticket.service.TicketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券领域服务
 * @Author: ywt
 * @Date: 2025-08-15 15:53
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TicketServiceImpl implements TicketService {
    private final TicketProductRepository ticketProductRepository;
    private final TicketRepository ticketRepository;
    private final TicketRegionRepository ticketRegionRepository;
    private final TicketBrandRepository ticketBrandRepository;
    private final RedisUtil redisUtil;

    @Override
//    @Cacheable(cacheNames = "product:sku:ticket", key = "#productType + ':' + #productSkUID", unless = "#result == null")
    public List<TicketEntity> getTicketsBySkuIdAndPrdType(Long productSkUID, Integer productType) {
        List<TicketEntity> resultList = null;
        String key = String.format(RedisKeyConstants.PRODUCT_SKU_TICKET, productType, productSkUID);
        String content = redisUtil.get(key);
        if (StringUtils.isNotEmpty(content)) {
            resultList = JSONArray.parseArray(content, TicketEntity.class);
        } else {
            List<Long> ticketIds = ticketProductRepository.getTicketsIdBySkuIdAndPrdType(productSkUID, productType);
            if (CollectionUtil.isEmpty(ticketIds)) {
                return null;
            }
            resultList = ticketRepository.getTicketsByIds(ticketIds);
            redisUtil.set(key, JSONArray.toJSONString(resultList), true);
        }
        return resultList;
    }

    @Override
    public boolean isOnlineInRegion(Long ticketId, Integer regionId) {
        List<TicketRegionEntity> regionEntityList = ticketRegionRepository.getRegionById(ticketId);
        if (CollectionUtil.isEmpty(regionEntityList)) {
            return false;
        }
        Set<Integer> regionSet = regionEntityList.stream()
                .map(TicketRegionEntity::getRegionId)
                .collect(Collectors.toSet());
        if (regionSet.contains(regionId)) {
            return true;
        }
        return false;
    }

    @Override
    public List<TicketBrandEntity> getTicketBrandsByIds(List<Long> brandIds) {
        return ticketBrandRepository.getTicketBrandsByIds(brandIds);
    }

    @Override
    public List<TicketEntity> getTicketsBySkuIdsAndTicketIds(Long productSkUID, Integer productType, List<Long> ticketIds) {
        if (CollectionUtil.isEmpty(ticketIds)) {
            return null;
        }
        List<TicketEntity> resultList = new ArrayList<>();
        List<TicketEntity> list = getTicketsBySkuIdAndPrdType(productSkUID, productType);
        if (CollectionUtil.isEmpty(list)) {
            log.error("此sku没有优惠券:{},{}", productSkUID, productType);
            throw new BizException("此sku没有优惠券");
        }
        Map<Long, TicketEntity> ticketMap = list.stream().collect(Collectors.toMap(TicketEntity::getTicketId, ticket->ticket));
        ticketIds.forEach(item -> {
            TicketEntity entity = ticketMap.get(item);
            if (Objects.isNull(entity)) {
                log.error("没有查到指定的优惠券:{}", item);
                throw new BizException("没有查到指定的优惠券信息");
            }
            resultList.add(entity);
        });
        return resultList;
    }
}
