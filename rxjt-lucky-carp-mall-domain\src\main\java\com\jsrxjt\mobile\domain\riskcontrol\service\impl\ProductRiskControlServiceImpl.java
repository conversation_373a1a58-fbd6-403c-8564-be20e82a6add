package com.jsrxjt.mobile.domain.riskcontrol.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.riskcontrol.dto.request.QueryProductRiskControlRequest;
import com.jsrxjt.mobile.api.riskcontrol.dto.response.ProductRiskControlResponse;
import com.jsrxjt.mobile.domain.riskcontrol.service.ProductRiskControlService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.riskcontrol.entity.ProductRiskControlAccountEntity;
import com.jsrxjt.mobile.domain.riskcontrol.entity.SkuRiskControlEntity;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlAccountRepository;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlRepository;
import com.jsrxjt.mobile.domain.rxmember.gateway.RxMemberGateway;
import com.jsrxjt.mobile.domain.rxmember.request.RxMemberRequest;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberResponse;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberRiskLevelResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/25 15:13
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProductRiskControlServiceImpl implements ProductRiskControlService {

    private final ProductRiskControlAccountRepository productRiskControlAccountRepository;

    private final ProductRiskControlRepository productRiskControlRepository;

    private final CustomerRepository customerRepository;

    private final RxMemberGateway rxMemberGateway;

    @Override
    public ProductRiskControlResponse getProductRiskControl(QueryProductRiskControlRequest request) {
        ProductRiskControlResponse response = new ProductRiskControlResponse();
        response.setCustomerId(request.getUserId());
        response.setCanPlaceOrderFlag(1);
        List<SkuRiskControlEntity> skuRiskControls = productRiskControlRepository.getRiskControlByProductTypeAndSkuId(request.getProductType(), request.getProductSkuId());
        // 当前商品没有风控策略
        if (CollectionUtils.isEmpty(skuRiskControls)) {
            return response;
        }
        skuRiskControls.sort(Comparator.comparing(SkuRiskControlEntity::getCommissionFee).reversed());
        SkuRiskControlEntity entity = skuRiskControls.get(0);
        // 当前用户没有风控策略
        ProductRiskControlAccountEntity riskControlAccount = productRiskControlAccountRepository.getRiskControlAccountByRiskId(entity.getId());
        if (riskControlAccount == null) {
            return response;
        }
        CustomerEntity user = customerRepository.selectCustomerById(request.getUserId());

        Integer accountType = riskControlAccount.getAccountType();
        switch (accountType) {
            // 手机号
            case 0:
                String accountName = riskControlAccount.getAccountName();
                List<String> phoneList = Arrays.asList(accountName.trim().split(","));
                if (phoneList.contains(user.getPhone())) {
                    response.setCanPlaceOrderFlag(2);
                } else {
                    BeanUtil.copyProperties(entity, response);
                    response.setCanPlaceOrderFlag(3);
                    response.setPayType(riskControlAccount.getPayTpe());
                }
                break;
            // 企业
            case 1:
                if (Objects.equals(riskControlAccount.getCompanyId(),user.getCompanyId())) {
                    response.setCanPlaceOrderFlag(2);
                } else {
                    BeanUtil.copyProperties(entity, response);
                    response.setCanPlaceOrderFlag(3);
                    response.setPayType(riskControlAccount.getPayTpe());
                }
                break;
            // 易盾
            case 2:
                RxMemberRequest rxMemberRequest = new RxMemberRequest();
                RxMemberResponse rxMemberResponse = rxMemberGateway.checkUserRiskLevel(rxMemberRequest);
                if (rxMemberResponse.isSuccess()) {
                    RxMemberRiskLevelResponse rxMemberRiskLevelResponse = (RxMemberRiskLevelResponse) rxMemberResponse.getData();
                    Integer riskLevel = rxMemberRiskLevelResponse.getRiskLevel();
                    // 易盾黑名单
                    if (riskLevel == 2 || riskLevel == 3 || riskLevel == 5) {
                        response.setCanPlaceOrderFlag(2);
                    } else {
                        BeanUtil.copyProperties(entity, response);
                        response.setCanPlaceOrderFlag(3);
                        response.setPayType(riskControlAccount.getPayTpe());
                    }
                } else {
                    // 请求异常或无结果时，默认用户不在易盾黑名单
                    BeanUtil.copyProperties(entity, response);
                    response.setCanPlaceOrderFlag(3);
                    response.setPayType(riskControlAccount.getPayTpe());
                }
                break;
        }

        return response;
    }
}
