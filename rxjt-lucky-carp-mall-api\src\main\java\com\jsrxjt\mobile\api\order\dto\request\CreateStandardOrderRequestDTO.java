package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建系统标准订单的请求参数DTO
 * 
 * <AUTHOR>
 * @since 2025/6/11
 **/
@Getter
@Setter
@Schema(description = "创建标准订单请求参数")
public class CreateStandardOrderRequestDTO extends BaseParam {

    @Schema(description = "产品类型")
    @Min(value = 1, message = "产品类型必须大于0" )
    private Integer productType;
    @Schema(description = "产品spuid")
    @Min(value = 1, message = "产品spuid必须大于0" )
    private Long productSpuId;

    @Schema(description = "产品skuid,不存在时传0")
    @Min(value = 0, message = "产品skuid必须大于等于0")
    private Long productSkuId;

    @Schema(description = "数量，默认是1")
    @Min(value = 1, message = "数量必须大于0")
    private Integer quantity = 1;

    @Schema(description = "充值账号")
    private String rechargeAccount;

    @Schema(description = "充值账号类型 0 其他 1 手机号 2 qq号 支付宝充值传0 视频直充传1或2")
    @Min(value = 0, message = "充值账号类型必须大于等于0")
    @Max(value = 2, message = "充值账号类型必须小于等于2")
    private Integer rechargeAccountType = 0;

    @Schema(description = "应用商品价格，应用商品时必传")
    private BigDecimal appProductPrice;

    /**
     * 赠送券列表
     */
    @Schema(description = "赠送券列表")
    private List<GiftTicketRequestDTO> giftTickets;

    @Schema(description = "经度")
    private BigDecimal longitude;

}
