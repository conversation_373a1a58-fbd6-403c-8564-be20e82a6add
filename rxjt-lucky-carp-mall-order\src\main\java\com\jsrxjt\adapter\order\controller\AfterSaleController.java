package com.jsrxjt.adapter.order.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.AfterSaleDetailRequestDTO;
import com.jsrxjt.mobile.api.order.dto.request.AfterSaleListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleListResponseDTO;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 售后控制器
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/v1/after-sale")
@RequiredArgsConstructor
@Tag(name = "售后接口", description = "售后相关接口")
public class AfterSaleController {
    
    private final AfterSaleCaseService afterSaleCaseService;
    
    /**
     * 售后详情查询
     */
    @PostMapping("/detail")
    @Operation(summary = "售后详情", description = "根据售后单号查询售后详情")
    @VerifySign(hasToken = true)
    public BaseResponse<AfterSaleDetailResponseDTO> getAfterSaleDetail(@RequestBody @Valid AfterSaleDetailRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        log.info("接收到售后详情查询请求，客户ID：{}，售后单号：{}", customerId, request.getAfterSaleNo());
        
        AfterSaleDetailResponseDTO result = afterSaleCaseService.getAfterSaleDetail(request.getAfterSaleNo(), customerId);
        
        return BaseResponse.succeed(result);
    }

    /**
     * 售后单列表分页查询
     */
    @PostMapping("/page")
    @Operation(summary = "售后单列表", description = "分页查询用户售后单列表")
    @VerifySign(hasToken = true)
    public BaseResponse<PageDTO<AfterSaleListResponseDTO>> pageAfterSaleList(@RequestBody @Valid AfterSaleListRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        log.info("接收到售后单列表查询请求，客户ID：{}，售后状态：{}", customerId, request.getAfterSaleStatus());
        
        PageDTO<AfterSaleListResponseDTO> result = afterSaleCaseService.pageAfterSaleList(customerId, request);
        
        return BaseResponse.succeed(result);
    }
}
