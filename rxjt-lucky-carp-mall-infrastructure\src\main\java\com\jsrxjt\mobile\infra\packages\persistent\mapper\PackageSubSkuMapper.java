package com.jsrxjt.mobile.infra.packages.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSkuSubJobEntity;
import com.jsrxjt.mobile.infra.packages.persistent.po.PackageSubSkuPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 套餐子sku的mapper
 * @Author: ywt
 * @Date: 2025-06-19 17:42
 * @Version: 1.0
 */
@Mapper
public interface PackageSubSkuMapper extends CommonBaseMapper<PackageSubSkuPO> {
    void updatePackageSubSkuStatus(@Param("list") List<PackageSkuSubJobEntity> list);

    /**
     * 原子性增加套餐子SKU销量
     *
     * @param packageSubSkuId 套餐子SKU ID
     * @param quantity        增加的数量
     * @return 更新的行数
     */
    int increaseSoldNum(@Param("packageSubSkuId") Long packageSubSkuId, @Param("quantity") Integer quantity);
}
