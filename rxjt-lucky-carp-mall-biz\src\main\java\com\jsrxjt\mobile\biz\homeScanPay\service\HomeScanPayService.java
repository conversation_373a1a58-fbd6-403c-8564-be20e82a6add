package com.jsrxjt.mobile.biz.homeScanPay.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.PickChannelAppRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.PickCodeRequestDTO;
import com.jsrxjt.mobile.api.scanPay.response.*;
import com.jsrxjt.mobile.api.scanPay.PosOrderRequestDTO;
import com.jsrxjt.mobile.api.scanPay.request.*;

import java.util.List;

/**
 * @Description:首页扫码付接口
 * 951码
 * @Author: zy
 * @Date: 20250530
 */
public interface HomeScanPayService {


    /**
     * 更具定位城市获取对应的展码付应用
     */
    BaseResponse<List<PickChannelAppResponseDTO>> getChannelApp(PickChannelAppRequestDTO requestDTO);

    /**
     * 第三方系统调用-根据code查询用户信息
     * @param requestDTO
     * @return
     */
    CustomerDetailResponse getCustomerEntityByCode(PickCodeRequestDTO requestDTO);
    /**
     * 1获取线下付款码
     * @return
     */
    HomeScanNumberResponseDTO getOfflineCode(OfflineScanCodeRequestDTO request);

    /**
     * 2-收银台返回订单信息
     * @return
     */
    ThirdOrderInfoResponseDTO getCashierVipOrder(PosOrderRequestDTO requestDTO);

    /**
     * 收银台返回支付结果
     * @param request
     */
    void getCashierPayResult(PollPayRefundRequestDTO request);

    /**
     * 3轮询预付订单展示付款界面
     * @return
     */
    PollVipOrderResponseDTO pollPreOrder(PollPrePayRequestDTO requestDTO);

    /**
     * 5-前端轮询支付结果
     */
    PollOrderRefundResultResponseDTO pollPaymentRefund(PollPayRefundRequestDTO request);

    /**
     * 验证密码是否正确
     * 密码MD5加密
     *  @return true 正确 false 错误
     */
    Boolean checkPassword(UserCheckPassRequestDTO requestDTO);


    /**
     *  验证用户是否被锁
     *   @return true 锁定 false 未锁定
     */
     Boolean checkUserIsLock(Long customerId);


    /**
     * 用户展码支付
     */
    HomePayResultResponseDTO homeScanPay(UserHomePayRequestDTO requestDTO);


}

