package com.jsrxjt.mobile.api.module.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Schema(description = "组件详情")
public class ModuleDetailResponse {

    @Schema(description = "组件详情id")
    private Integer detailId;
    
    @Schema(description = "所属组件id")
    private Integer moduleId;

    @Schema(description = "父详情id(相同的父详情在同一个位置)")
    private Integer parentDetailId;

    @Schema(description = "详情标题")
    private String detailTitle;

    @Schema(description = "详情副标题")
    private String detailSubTitle;

    @Schema(description = "详情类型(10:自定义tab栏 11:本地生活tab 12:全球购tab 100:普通卡券 200:组合套餐卡券 300:普通应用 400:支付宝红包应用/苏西话费应用 1000:普通广告位 1100:单图 1200:轮播图 1300:单资讯 1400:多资讯 1500:本地生活 1600:全球购商品 1700:装修活动页 1800:自定义链接 1900:分类页)")
    private Integer detailType;

    @Schema(description = "产品id(根据type对应各表主键,分类产品特殊,多个分类逗号分割,全部分类直接传0)")
    private String productId;

    @Schema(description = "产品sku id")
    private String productItemId;

    @Schema( description = "详情图片(卡券,应用等图片)")
    private String productImgUrl;

    @Schema( description = "logo图片(卡券,应用等图片)")
    private String productLogoUrl;

    @Schema(description = "商品类型(detailType + 对应类型 例:本地生活1510:商户 1520:代金券 1530:套餐)")
    private Integer productType;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品副标题")
    private String productSubName;

    @Schema(description = "产品价格")
    private String productPrice;

    @Schema(description = "产品原价")
    private String productOriginalPrice;

    @Schema(description = "角标图片")
    private String subscriptImg;

    @Schema(description = "背景图片链接(不包含卡券，应用类型等图片,关联对应表查询)")
    private String imgUrl;

    @Schema(description = "详情内容(包含各平台跳转链接等)")
    private Map<String, Object> content;

    @Schema(description = "排序")
    private Double sortOrder;

    @Schema(description = "折扣率")
    private String discountRate;

    @Schema(description = "可用时间")
    private String availableTimeStr;

    @Schema(description = "距离(单位米)")
    private Integer distance;

    @Schema(description = "小程序appid")
    private String miniAppId;

    @Schema(description = "小程序ghid")
    private String ghId;

    @Schema(description = "促销标签")
    private List<String> promotionLabels;

    @Schema(description = "活动标签文案 用在tab栏商品(暂时卡券，套餐)名称前面")
    private String labelCopy;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    private List<ModuleDetailResponse> children = new ArrayList<>();
}
