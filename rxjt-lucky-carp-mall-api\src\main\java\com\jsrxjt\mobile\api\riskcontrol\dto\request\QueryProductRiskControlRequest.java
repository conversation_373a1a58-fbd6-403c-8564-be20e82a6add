package com.jsrxjt.mobile.api.riskcontrol.dto.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/25 18:00
 */
@Data
public class QueryProductRiskControlRequest {

    /**
     * 产品skuId或应用ID
     */
    private Long productSkuId;

    /**
     * 产品类型 1、卡券 2、套餐 3、应用，4、支付宝红包 5、苏西话费 6、扫码付应用
     */
    private Integer productType;

    /**
     * 用户Id
     */
    private Long userId;

    private String token;
}
