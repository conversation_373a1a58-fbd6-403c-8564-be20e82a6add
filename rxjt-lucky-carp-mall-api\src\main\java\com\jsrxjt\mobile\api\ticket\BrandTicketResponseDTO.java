package com.jsrxjt.mobile.api.ticket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 品牌优惠券响应
 * @Author: ywt
 * @Date: 2025-08-20 19:09
 * @Version: 1.0
 */
@Data
@Schema(description = "品牌优惠券响应数据")
public class BrandTicketResponseDTO {
    @Schema(description = "品牌名")
    private String brandName;
    @Schema(description = "优惠券列表")
    private List<TicketResponseDTO> ticketList;
}
