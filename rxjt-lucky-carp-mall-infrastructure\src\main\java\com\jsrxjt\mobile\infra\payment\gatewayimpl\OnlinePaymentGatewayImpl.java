package com.jsrxjt.mobile.infra.payment.gatewayimpl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.CheckFindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.FindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.PaymentRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.PrePayRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.CheckFindCardBalanceResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.FindCardBalanceResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.PaymentResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.PrePayResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 线上支付Gateway实现类
 * <AUTHOR>
 * @since 2025/8/8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OnlinePaymentGatewayImpl implements OnlinePaymentGateway {

    private final HttpClientGateway httpClientGateway;
    
    @Value("${payment.online.host:https://flqcash-test.rxcrs.com}")
    private String paymentHost;
    
    @Value("${payment.online.prepay.url:/online/pay/preview}")
    private String prePayUrl;
    
    @Value("${payment.online.pay.url:/online/pay}")
    private String payUrl;

    @Value("${payment.online.find.balance.url:/online/find_balance}")
    private String findBalanceUrl;

    @Value("${payment.online.find.balance.url:/online/check_find_balance}")
    private String checkFindBalanceUrl;

    @Value("${payment.online.connect.timeout:3000}")
    private int connectTimeout;
    
    @Value("${payment.online.read.timeout:5000}")
    private int readTimeout;


    @Override
    public PrePayResponse prePayment(PrePayRequest request) {
        log.info("开始调用预支付接口，订单号：{}，来源：{}", request.getOrderNo(), request.getSource());
        
        try {
            // 构建请求JSON - 转换为下划线格式
            String requestJson = JSON.toJSONString(request);
            log.info("预支付请求参数：{}", requestJson);
            
            // 调用远程接口
            String url = paymentHost + prePayUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("预支付接口响应：{}", responseStr);
            
            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);
            
            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("预支付接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PRE_INVOKE_ERROR.getCode(),errorMsg);
            }
            
            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("预支付接口返回数据为空");
                throw new BizException("预支付接口返回数据为空");
            }


            // 解析 JSON 数据为 PrePayResponse 类
            PrePayResponse response = JSON.parseObject(data, PrePayResponse.class);
            log.info("预支付处理成功，交易流水号：{},订单号：{}", response.getTradeNo(),request.getOrderNo());
            
            return response;
            
        } catch (BizException e) {
            log.error("预支付处理业务异常，订单号：{}，错误码：{} 错误信息：{}", request.getOrderNo(), e.getCode(),e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("预支付接口调用异常，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("预支付接口调用IO异常", e);
        } catch (Exception e) {
            log.error("预支付处理失败，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("预支付处理失败", e);
        }
    }

    @Override
    public PaymentResponse pay(PaymentRequest request) {
        log.info("开始调用发起支付接口，订单号：{}，预支付订单号：{}", 
                request.getOrderNo(), request.getPreOrderNo());
        
        try {
            String requestJson = JSON.toJSONString(request);
            log.info("发起支付请求参数：{}", requestJson);
            
            // 调用远程接口
            String url = paymentHost + payUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("发起支付接口响应：{}", responseStr);
            
            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (Objects.equals(code, 599)) {
                // 支付密码错误 单独处理
                String errorMsg = responseJson.getString("message");
                log.error("支付接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PASS_ERROR);
            }
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("支付接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PAY_INVOKE_ERROR.getCode(),errorMsg);
            }
            
            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("发起支付接口返回数据为空");
                throw new BizException("预支付接口返回数据为空");
            }


            // 解析 JSON 数据为 PaymentResponse 类
            PaymentResponse response = JSON.parseObject(data, PaymentResponse.class);
            log.info("发起支付处理成功，交易流水号：{},订单号：{}", response.getTradeNo(),request.getOrderNo());

            return response;

            
        } catch (BizException e) {
            log.error("发起支付处理业务异常，订单号：{}，错误码：{} 错误信息：{}", request.getOrderNo(), e.getCode(),e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("发起支付接口调用异常，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("发起支付接口调用IO异常", e);
        } catch (Exception e) {
            log.error("支付处理失败，订单号：{}，错误信息：{}", request.getOrderNo(), e.getMessage(), e);
            throw new BizException("发起支付处理失败", e);
        }
    }

    @Override
    public List<FindCardBalanceResponse> findBalance(FindCardBalanceRequest request) {
        log.info("开始调用查询卡余额接口");
        try {
            String requestJson = JSON.toJSONString(request);
            log.info("查询卡余额请求参数：{}", requestJson);
            // 调用远程接口
            String url = paymentHost + findBalanceUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("查询卡余额接口响应：{}", responseStr);

            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("查询卡余额接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(),code + ":" +errorMsg);
            }

            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("查询卡余额接口返回数据为空");
                throw new BizException("查询卡余额接口返回数据为空");
            }
            JSONArray findResultJSONArray = JSON.parseObject(data).getJSONArray("find_result");
            if (findResultJSONArray == null) {
                log.error("查询卡余额接口返回数据格式错误");
                throw new BizException("查询卡余额接口返回数据格式错误");
            }

            List<FindCardBalanceResponse> responseList = JSON.parseArray(findResultJSONArray.toJSONString(), FindCardBalanceResponse.class);            log.info("查询卡余额接口查询结束");
            return responseList;

        } catch (BizException e) {
            log.error("查询卡余额接口调用异常，错误码：{} 错误信息：{}", e.getCode(), e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("查询卡余额接口调用异常，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "查询卡余额接口调用IO异常", e);
        } catch (Exception e) {
            log.error("查询卡余额接口调用失败，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "查询卡余额接口调用失败", e);
        }
    }

    @Override
    public CheckFindCardBalanceResponse findBalanceByCardNoAndPassword(CheckFindCardBalanceRequest request) {
        log.info("开始调用根据卡号密码查询卡余额接口");
        try {
            String requestJson = JSON.toJSONString(request);
            log.info("根据卡号密码查询卡余额请求参数：{}", requestJson);
            // 调用远程接口
            String url = paymentHost + checkFindBalanceUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("根据卡号密码查询卡余额接口响应：{}", responseStr);

            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("根据卡号密码查询卡余额接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(),code + ":" +errorMsg);
            }

            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("根据卡号密码查询卡余额接口返回数据为空");
                throw new BizException("根据卡号密码查询卡余额接口返回数据为空");
            }
            CheckFindCardBalanceResponse response = JSON.parseObject(data, CheckFindCardBalanceResponse.class);
            log.info("根据卡号密码查询卡余额成功，卡号：{},余额：{}", request.getCardNo(),request.getPassword());
            return response;
        } catch (BizException e) {
            log.error("根据卡号密码查询卡余额接口调用异常，错误码：{} 错误信息：{}", e.getCode(), e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("根据卡号密码查询卡余额接口调用异常，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "根据卡号密码查询卡余额接口调用IO异常", e);
        } catch (Exception e) {
            log.error("根据卡号密码查询卡余额接口调用失败，错误信息：{}", e.getMessage(), e);
            throw new BizException(Status.FIND_CARD_BALANCE_FAILED.getCode(), "根据卡号密码查询卡余额接口调用失败", e);
        }
    }

}
