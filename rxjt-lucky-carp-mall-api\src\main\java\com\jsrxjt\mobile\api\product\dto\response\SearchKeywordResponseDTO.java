package com.jsrxjt.mobile.api.product.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 默认搜索词响应DTO
 * 
 * <AUTHOR>
 * @since 2025/5/14
 */
@Data
@Schema(description = "默认搜索词响应")
public class SearchKeywordResponseDTO {

    @Schema(description = "搜索词ID", example = "1001")
    private Long id;

    @Schema(description = "关键词", example = "iPhone 15")
    private String keyword;

    @Schema(description = "图标广告图url", example = "https://example.com/images/iphone15.jpg")
    private String imlUrl;

    @Schema(description = "跳转类型：1-内部链接，2-外部链接", example = "1")
    private Integer jumpType;

    @Schema(description = "跳转URL", example = "https://example.com/product/12345")
    private String jumpUrl;

    @Schema(description = "排序值", example = "10")
    private Integer sort;

    @Schema(description = "搜索词类型：1-搜索关键词 2-搜索发现", example = "1")
    private Integer type;
}