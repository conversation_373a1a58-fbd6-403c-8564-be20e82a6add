package com.jsrxjt.mobile.infra.payment.gatewayimpl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.payment.gateway.OfflinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.*;
import com.jsrxjt.mobile.domain.payment.gateway.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 线下支付相关接口实现
 * <AUTHOR>
 * @since 2025/8/8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OfflinePaymentGatewayImpl implements OfflinePaymentGateway {

    private final HttpClientGateway httpClientGateway;

    @Value("${payment.online.host:https://flqcash-test.rxcrs.com}")
    private String paymentHost;

    @Value("${payment.online.prepay.url:/online/user/card/sort}")
    private String cardSortUrl;

    @Value("${payment.online.connect.timeout:3000}")
    private int connectTimeout;

    @Value("${payment.online.read.timeout:5000}")
    private int readTimeout;


    @Override
    public OfflineCardSortResponse findOfflineCardSort(OfflineCardSortRequest request) {
        log.info("开始调用卡顺序接口，用户id：{}", request.getUser_id());
        
        try {
            // 构建请求JSON - 转换为下划线格式
            String requestJson = JSON.toJSONString(request);
            log.info("卡顺序请求参数：{}", requestJson);
            
            // 调用远程接口
            String url = paymentHost + cardSortUrl;
            String responseStr = httpClientGateway.doPostJson(url, requestJson, connectTimeout, readTimeout);
            log.info("卡顺序接口响应：{}", responseStr);
            
            // 解析响应结果
            JSONObject responseJson = JSON.parseObject(responseStr);
            
            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (!Objects.equals(code, 0)) {
                String errorMsg = responseJson.getString("message");
                log.error("卡顺序接口调用失败，错误code：{} 错误信息：{}", code,errorMsg);
                throw new BizException(Status.ONLINE_PAY_PRE_INVOKE_ERROR.getCode(),errorMsg);
            }
            
            // 解析响应数据
            String data = responseJson.getString("data");
            if (StringUtils.isBlank(data)) {
                log.error("卡顺序接口返回数据为空");
                throw new BizException("预支付接口返回数据为空");
            }
            // 解析 JSON 数据为 PrePayResponse 类
            OfflineCardSortResponse response = JSONUtil.toBean(data, OfflineCardSortResponse.class);
            return response;
            
        } catch (BizException e) {
            log.error("卡顺序处理业务异常，用户id：{}，错误码：{} 错误信息：{}", request.getUser_id(), e.getCode(),e.getMsg());
            throw e;
        } catch (IOException e) {
            log.error("卡顺序接口调用异常，用户id：{}，错误信息：{}", request.getUser_id(), e.getMessage(), e);
            throw new BizException("预支付接口调用IO异常", e);
        } catch (Exception e) {
            log.error("卡顺序处理失败，用户id：{}，错误信息：{}", request.getUser_id(), e.getMessage(), e);
            throw new BizException("预支付处理失败", e);
        }
    }

    /**
     * 线下预支付
     *
     * @param request
     * @return
     */
    @Override
    public OfflinePrePayResponse prePayment(OfflinePrePayRequest request) {
        return null;
    }

}
