package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 结算接口返回参数
 * 
 * <AUTHOR>
 * @description
 * @since 2025/6/25
 **/
@Data
@Schema(description = "结算接口返回参数")
public class SettleResponseDTO {

    @Schema(description = "原价销售价金额")
    private BigDecimal totalSellAmount;

    @Schema(description = "加点手续费")
    private BigDecimal totalServiceFee;

    @Schema(description = "单件商品加点手续费")
    private BigDecimal singleGoodsServiceFee;

    @Schema(description = "加点手续费百分比")
    private BigDecimal serviceFeeRate;

    @Schema(description = "超额手续费")
    private BigDecimal totalExceedFee;

    @Schema(description = "订单金额（不带优惠）")
    private BigDecimal orderAmount;

    @Schema(description = "订单支付金额")
    private BigDecimal payAmount;

    @Schema(description = "超额金额")
    private BigDecimal exceedAmount;

    @Schema(description = "超额手续费")
    private BigDecimal exceedFee;

    @Schema(description = "超额百分比")
    private BigDecimal exceedPercentage;

    @Schema(description = "是否允许超额")
    private Boolean isExceedAllowed;

    @Schema(description = "限额金额(超出限额计算超额手续费)")
    private BigDecimal limitAmount;

    @Schema(description = "当月商品已购金额")
    private BigDecimal purchasedAmount;

    @Schema(description = "总优惠金额")
    private BigDecimal totalDiscountAmount;

    @Schema(description = "单件商品优惠金额")
    private BigDecimal singleGoodsDiscountAmount;

    @Schema(description = "如果是打折活动，表示折扣率 97 表示97折，没有是空")
    private Integer discount;

    @Schema(description = "单个面值的每月限购数量")
    private Integer skuLimitNumPerMonth;

    @Schema(description = "单个面值的当月已购数量")
    private Integer monthlyPurchaseQuantity;

    @Schema(description = "月购买限制数量")
    private Integer monthlyLimitQuantity;

    /**
     * 赠送券列表
     */
    @Schema(description = "赠送券列表")
    private List<GiftTicketResponseDTO> giftTickets;

    /**
     * 产品信息
     */
    @Schema(description = "产品信息")
    private ProductInfo productInfo;

    /**
     * 套餐子产品信息
     */
    @Data
    @Schema(description = "套餐子产品信息")
    public static class PackageSubProductInfo {

        @Schema(description = "面值名称")
        private String amountName;

        @Schema(description = "该套餐中的子sku数量")
        private Integer packageCouponNum;

        @Schema(description = "面值")
        private BigDecimal amount;

        @Schema(description = "套餐中的子sku图片")
        private String packageCouponImg;
    }

    /**
     * 套餐子产品信息
     */
    @Data
    @Schema(description = "产品信息")
    public static class ProductInfo {
        /**
         * spuId
         */
        @Schema(description = "spuId")
        private Long spuId;

        /**
         * skuId 没有sku的产品默认为0
         */
        @Schema(description = "skuId 没有sku的产品默认为0")
        private Long skuId;


        /**
         * 产品名称
         */
        @Schema(description = "产品名称")
        private String productName;

        /**
         * 产品logo
         */
        @Schema(description = "产品logo")
        private String productLogo;

        /**
         * 产品类型
         */
        @Schema(description = "产品类型")
        private Integer productType;

        /**
         * 产品类型（扁平化）
         */
        @Schema(description = "产品类型（扁平化）")
        private Integer flatProductType;

        /**
         * 平台售价（不含手续费）
         */
        @Schema(description = "平台售价（不含手续费）")
        private BigDecimal platformPrice;

        @Schema(description = "购买数量")
        private Integer quantity;

        /**
         * 面额
         */
        @Schema(description = "面额")
        private BigDecimal faceAmount;

        @Schema(description = "套餐子产品信息列表（仅套餐产品有值）")
        List<PackageSubProductInfo> subProductList;

    }

}
