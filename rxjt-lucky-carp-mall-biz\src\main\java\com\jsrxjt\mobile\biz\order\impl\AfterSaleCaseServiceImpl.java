package com.jsrxjt.mobile.biz.order.impl;

import com.alibaba.fastjson2.JSON;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.AfterSaleListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleListResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.RefundAccountDTO;
import com.jsrxjt.mobile.api.order.types.AfterSaleOperationTypeEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.biz.order.AfterSaleCaseService;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleLogEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.query.AfterSaleListQuery;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 售后业务服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AfterSaleCaseServiceImpl implements AfterSaleCaseService {
    
    private final AfterSaleRepository afterSaleRepository;
    private final AfterSaleLogRepository afterSaleLogRepository;
    private final OrderRepository orderRepository;
    
    @Override
    public AfterSaleDetailResponseDTO getAfterSaleDetail(String afterSaleNo, Long customerId) {
        log.info("开始查询售后详情，售后单号：{}，客户ID：{}", afterSaleNo, customerId);
        
        // 查询售后信息
        AfterSaleEntity afterSale = afterSaleRepository.findByAfterSaleNoAndCustomerId(afterSaleNo, customerId);
        if (afterSale == null) {
            log.warn("售后单不存在或无权限访问，售后单号：{}，客户ID：{}", afterSaleNo, customerId);
            throw new BizException("售后单不存在");
        }
        
        // 查询订单信息
        OrderInfoEntity order = orderRepository.findByOrderNo(afterSale.getOrderNo());
        if (order == null) {
            log.warn("关联订单不存在，订单号：{}", afterSale.getOrderNo());
            throw new BizException("关联订单不存在");
        }
        
        // 查询售后日志
        List<AfterSaleLogEntity> logs = afterSaleLogRepository.findByAfterSaleNo(afterSaleNo);
        
        // 构建响应DTO
        AfterSaleDetailResponseDTO response = buildAfterSaleDetailResponse(afterSale, order, logs);
        
        log.info("售后详情查询完成，售后单号：{}", afterSaleNo);
        return response;
    }
    
    /**
     * 构建售后详情响应
     */
    private AfterSaleDetailResponseDTO buildAfterSaleDetailResponse(AfterSaleEntity afterSale, 
                                                                   OrderInfoEntity order, 
                                                                   List<AfterSaleLogEntity> logs) {
        AfterSaleDetailResponseDTO response = new AfterSaleDetailResponseDTO();
        
        // 设置售后状态
        response.setAfterSaleStatus(afterSale.getAfterSaleStatus());

        // 设置退款状态
        response.setRefundStatus(afterSale.getRefundStatus());

        // 设置售后类型
        response.setAfterSaleType(afterSale.getAfterSaleType());
        
        // 设置退款总金额
        response.setRefundTotalAmount(afterSale.getRefundAmount());
        
        // 设置退款详情信息
        String refundDetails = afterSale.getRefundAccountDetails();
        if (StringUtils.isNotBlank(refundDetails)) {
            List<RefundAccountDTO> refundAccountList= JSON.parseArray(refundDetails, RefundAccountDTO.class);
            response.setRefundAccountList(refundAccountList);
        }

        // 构建进度列表
        response.setProgressList(buildProgressList(afterSale));
        
        // 构建进度详情
        response.setProgressDetails(buildProgressDetails(logs));
        
        // 构建退款信息
        response.setRefundInfo(buildRefundInfo(afterSale, order));
        
        return response;
    }

    
    /**
     * 构建进度列表
     */
    private List<AfterSaleDetailResponseDTO.AfterSaleProgressDTO> buildProgressList(AfterSaleEntity afterSale) {
        Integer currentStatus = afterSale.getAfterSaleStatus();
        List<AfterSaleDetailResponseDTO.AfterSaleProgressDTO> progressList = new ArrayList<>();
        
        // 提交申请
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step1 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step1.setStepName("提交申请");
        step1.setCompleted(true);
        step1.setCurrent(currentStatus.equals(AfterSaleStatusEnum.PENDING_AUDIT.getCode()));
        progressList.add(step1);
        if (Boolean.TRUE.equals(step1.getCurrent())) {
            return progressList; // 如果当前步骤是提交申请，则不需要继续构建其他步骤
        }
        if (currentStatus.equals(AfterSaleStatusEnum.AUDIT_REJECTED.getCode())
                || currentStatus.equals(AfterSaleStatusEnum.AFTER_SALE_CANCELLED.getCode())) {
            // 如果当前状态是审核驳回或撤销，则不需要继续构建其他步骤
            AfterSaleDetailResponseDTO.AfterSaleProgressDTO step2 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
            step2.setStepName(Objects.requireNonNull(AfterSaleStatusEnum.getByCode(currentStatus)).getDesc());
            step2.setCompleted(true);
            step2.setCurrent(true);
            progressList.add(step2);
            return progressList;
        }
        
        // 客服审核
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step2 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step2.setStepName("客服审核");
        step2.setCompleted(currentStatus >= AfterSaleStatusEnum.AUDIT_PASSED.getCode());
        step2.setCurrent(currentStatus.equals(AfterSaleStatusEnum.AUDIT_PASSED.getCode()));
        progressList.add(step2);
        Integer refundStatus = afterSale.getRefundStatus();
        
        // 处理中
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step3 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step3.setStepName("处理中");
        step3.setCompleted(currentStatus >= AfterSaleStatusEnum.AUDIT_PASSED.getCode());

        // 退款处理中
        step3.setCurrent(refundStatus.equals(RefundStatusEnum.REFUNDING.getCode()) ||
                refundStatus.equals(RefundStatusEnum.NOT_REFUNDED.getCode()));
        progressList.add(step3);
        
        // 已完成
        AfterSaleDetailResponseDTO.AfterSaleProgressDTO step4 = new AfterSaleDetailResponseDTO.AfterSaleProgressDTO();
        step4.setStepName("已完成");
        step4.setCompleted(currentStatus.equals(AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode()));
        step4.setCurrent(currentStatus.equals(AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode()));
        if (currentStatus > AfterSaleStatusEnum.AUDIT_PASSED.getCode()
                && currentStatus < AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode()) {
            //售后驳回的场景
            step4.setStepName("售后驳回");
            step4.setCompleted(true);
            step4.setCurrent(true);
        }
        progressList.add(step4);
        
        return progressList;
    }
    
    /**
     * 构建进度详情
     */
    private List<AfterSaleDetailResponseDTO.AfterSaleLogDTO> buildProgressDetails(List<AfterSaleLogEntity> logs) {
        List<AfterSaleDetailResponseDTO.AfterSaleLogDTO> details = new ArrayList<>();
        
        for (AfterSaleLogEntity log : logs) {
            AfterSaleDetailResponseDTO.AfterSaleLogDTO detail = new AfterSaleDetailResponseDTO.AfterSaleLogDTO();
            AfterSaleOperationTypeEnum operationType = AfterSaleOperationTypeEnum.getByCode(log.getOperationType());
            detail.setOperationDesc(operationType != null ? operationType.getDesc() : "");
            detail.setOperationTime(log.getOperationTime());
            detail.setOperationDetail(log.getOperationContent());
            details.add(detail);
        }
        
        return details;
    }

    
    /**
     * 构建退款信息
     */
    private AfterSaleDetailResponseDTO.RefundInfoDTO buildRefundInfo(AfterSaleEntity afterSale, OrderInfoEntity order) {
        AfterSaleDetailResponseDTO.RefundInfoDTO refundInfo = new AfterSaleDetailResponseDTO.RefundInfoDTO();
        
        // 构建商品信息
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            OrderItemEntity orderItem = order.getOrderItems().get(0);
            AfterSaleDetailResponseDTO.RefundInfoDTO.ProductInfoDTO productInfo = 
                new AfterSaleDetailResponseDTO.RefundInfoDTO.ProductInfoDTO();
            productInfo.setProductName(orderItem.getProductName());
            productInfo.setBrandName(orderItem.getBrandName());
            productInfo.setProductLogo(orderItem.getProductLogo());
            productInfo.setFaceAmount(orderItem.getFaceAmount());
            productInfo.setSellPrice(orderItem.getSellPrice());
            productInfo.setQuantity(orderItem.getQuantity());
            refundInfo.setProductInfo(productInfo);
        }
        
        // 设置退款信息
        refundInfo.setRefundAmount(afterSale.getRefundAmount());
        refundInfo.setApplyQuantity(afterSale.getAfterSaleQuantity());
        refundInfo.setApplyTime(afterSale.getApplyTime());
        refundInfo.setAfterSaleNo(afterSale.getAfterSaleNo());
        refundInfo.setOrderNo(afterSale.getOrderNo());
        refundInfo.setRefundReason(afterSale.getAfterSaleReason());
        
        return refundInfo;
    }
    
    @Override
    public PageDTO<AfterSaleListResponseDTO> pageAfterSaleList(Long customerId, AfterSaleListRequestDTO request) {
        log.info("开始查询用户售后单列表，客户ID：{}，售后状态：{}，售后类型“{}",
                 customerId, request.getAfterSaleStatus(), request.getAfterSaleType());

        
        // 构建查询条件
        AfterSaleListQuery query = AfterSaleListQuery.builder()
                .customerId(customerId)
                .afterSaleStatus(request.getAfterSaleStatus() < 0 ? null : request.getAfterSaleStatus())
                .afterSaleType(request.getAfterSaleType())
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();
        
        // 查询售后单列表
        PageDTO<AfterSaleEntity> afterSalePage = afterSaleRepository.findAfterSaleListByPage(query);
        
        // 转换为响应DTO
        List<AfterSaleListResponseDTO> afterSaleList = afterSalePage.getRecords().stream()
                .map(this::convertToAfterSaleListResponse)
                .toList();
        
        return PageDTO.<AfterSaleListResponseDTO>builder()
                .records(afterSaleList)
                .total(afterSalePage.getTotal())
                .current(afterSalePage.getCurrent())
                .size(afterSalePage.getSize())
                .build();
    }
    
    /**
     * 转换为售后单列表响应DTO
     */
    private AfterSaleListResponseDTO convertToAfterSaleListResponse(AfterSaleEntity entity) {
        AfterSaleListResponseDTO response = new AfterSaleListResponseDTO();
        
        response.setAfterSaleNo(entity.getAfterSaleNo());
        response.setOrderNo(entity.getOrderNo());
        response.setAfterSaleType(entity.getAfterSaleType());
        response.setAfterSaleStatus(entity.getAfterSaleStatus());
        response.setRefundStatus(entity.getRefundStatus());
        response.setRefundAmount(entity.getRefundAmount());
        response.setApplyQuantity(entity.getAfterSaleQuantity());
        if (entity.getOrderItem() != null) {
            response.setProductName(entity.getOrderItem().getProductName());
            response.setBrandName(entity.getOrderItem().getBrandName());
            response.setProductLogo(entity.getOrderItem().getProductLogo());
            response.setFaceAmount(entity.getOrderItem().getFaceAmount());
            response.setSellPrice(entity.getOrderItem().getSellPrice());
            response.setQuantity(entity.getOrderItem().getQuantity());
        }
        response.setCreateTime(entity.getCreateTime());
        
        return response;
    }
}

