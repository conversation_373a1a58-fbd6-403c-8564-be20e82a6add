package com.jsrxjt.mobile.api.product.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 历史充值账号请求参数
 * @Author: ywt
 * @Date: 2025-08-06 09:41
 * @Version: 1.0
 */
@Data
public class RechargeAccountHistoryRequestDTO {
    @Schema(description = "产品spuId")
    @NotNull(message = "产品spuId为空错误")
    private Long productSpuId;
    @Schema(description = "产品类型 1卡券 4支付宝红包")
    @NotNull(message = "产品类型为空错误")
    private Integer productType;
    @Schema(description = "账户类型 0-其他 1-手机号 2-QQ号")
    @NotNull(message = "账户类型为空错误")
    private Integer accountType;
}
