package com.jsrxjt.mobile.infra.packages.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 套餐子SKU表
 * @Author: ywt
 * @Date: 2025-06-19 17:44
 * @Version: 1.0
 */
@Data
@TableName("package_sub_sku")
public class PackageSubSkuPO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long packageSpuId;
    private Long brandId;
    private Long packageSkuId;
    private Long outerId;
    private Integer couponType;//卡券类型 1卡券 3品诺
    private String amountName;//卡管面值名称
    private BigDecimal amount;//面值
    private BigDecimal price;//卡管的售价
    private BigDecimal costPrice;//卡管的成本价
    private Integer soldNum;//已售数量
    private Integer subSkuStatus;//子sku状态 0:下架 1:出售中
    private Integer inventory;//卡券库存
    private Integer  isMoreOffset;//是否多次核销 0否 1是
    private Integer packageCouponNum;//该套餐中的子sku数量
    private Integer onSaleNum;//起售数量
    private Integer rationSaleNum;//单次限售数量
    // private Date couponValidTime;//卡券有效期时间
    private Integer centerStatus;//卡管平台状态 0:下架 1:出售中
    private Integer accountType;//充值类型
    private Integer type;//核销类型
    private Integer pnType;//品诺类型 直充类商品2卡券类商品
    private String backgroundColor;//核销页背景色
    private String scenarios; //使用说明
   //自定义核销类型 1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码  4面值+卡号卡密 5面值+卡号卡密+兑换码 6面值+卡号卡密+一维二维码 7面值+卡密+一维二维码 8面值+链接 9自发券(面值+提货券形式)',
    private Integer flqType;

    private String imgUrl;
    private Date createTime;

    private Long createId;

    private Long modId;

    private Date modTime;
    private Integer delFlag;
}
