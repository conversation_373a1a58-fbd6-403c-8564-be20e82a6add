<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.product.persistent.mapper.ProductOffsetPageMapper">


    <select id="findBySpuId" resultType="com.jsrxjt.mobile.infra.product.persistent.po.ProductOffsetPagePO">
        select
            id,
            product_spu_id,
            product_type,
            scenarios,
            background_color,
            validity_date,
            remark,
            create_time,
            create_id,
            del_flag,
            mod_id,
            mod_time
        from product_offset_page
        where product_spu_id = #{spuId}
        and product_type = #{productType}
        and del_flag = 0
    </select>
</mapper>