package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantRxShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopDetailResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopResponseDTO;

/**
 * @Description: 商户服务接口
 * @Author: ywt
 * @Date: 2025-05-27 14:26
 * @Version: 1.0
 */
public interface MerchantCaseService {
    PageDTO<MerchantShopResponseDTO> getNearShopList(MerchantShopRequestDTO requestDTO);
    PageDTO<MerchantShopResponseDTO> getRxShopList(MerchantRxShopRequestDTO requestDTO);

    MerchantShopDetailResponseDTO getShopDetail(String shopId);

}
