package com.jsrxjt.mobile.biz.product.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.dto.request.AccountRechargeRecordRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.RechargeAccountHistoryRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.RechargeAccountHistoryResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.RechargeRecordResponse;
import com.jsrxjt.mobile.biz.product.service.ProductRechargeCaseService;
import com.jsrxjt.mobile.domain.customer.entity.RechargeAccountHistoryEntity;
import com.jsrxjt.mobile.domain.customer.repository.RechargeAccountHistoryRepository;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.query.OrderListQuery;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 产品充值服务
 * @Author: ywt
 * @Date: 2025-08-07 11:35
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductRechargeCaseServiceImpl implements ProductRechargeCaseService {
    private final RechargeAccountHistoryRepository rechargeAccountHistoryRepository;
    private final OrderRepository orderRepository;

    @Override
    public List<RechargeAccountHistoryResponseDTO> getHisRechargeAccounts(RechargeAccountHistoryRequestDTO requestDTO) {
        List<RechargeAccountHistoryEntity> list = rechargeAccountHistoryRepository.findByCustomerAndProduct(StpUtil.getLoginIdAsLong(), requestDTO.getProductSpuId(), requestDTO.getProductType(), requestDTO.getAccountType());
        return BeanUtil.copyToList(list, RechargeAccountHistoryResponseDTO.class);
    }

    @Override
    public PageDTO<RechargeRecordResponse> getRechargeRecords(AccountRechargeRecordRequestDTO request) {
        // 当月第一天00:00:00 - 最后一天23:59:59
        LocalDateTime startTime = request.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime endTime = request.getYearMonth().atEndOfMonth().atTime(23, 59, 59);
        OrderListQuery query = OrderListQuery.builder()
                .customerId(StpUtil.getLoginIdAsLong())
                .createTimeStart(startTime)
                .createTimeEnd(endTime)
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();
        PageDTO<OrderInfoEntity> orderPage = orderRepository.findByFlatProductTypeAndDateRange(query, request.getProductType(), request.getProductSpuId());

        List<RechargeRecordResponse> recordResponseList = orderPage.getRecords().stream()
                .map(this::convertToRechargeRecordResponse)
                .collect(Collectors.toList());

        return PageDTO.<RechargeRecordResponse>builder()
                .records(recordResponseList)
                .total(orderPage.getTotal())
                .current(orderPage.getCurrent())
                .size(orderPage.getSize())
                .build();
    }

    private RechargeRecordResponse convertToRechargeRecordResponse(OrderInfoEntity order) {
        RechargeRecordResponse response = new RechargeRecordResponse();
        response.setOrderNo(order.getOrderNo());
        response.setPaymentAmount(order.getPaymentAmount());
        response.setRechargeAccount(order.getRechargeAccount());
        response.setOrderStatus(order.getOrderStatus());
        response.setDeliveryStatus(order.getDeliveryStatus());
        response.setCreateTime(order.getCreateTime());
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            OrderItemEntity orderItem = order.getOrderItems().get(0);
            response.setProductName(orderItem.getProductName());
            response.setProductSpuId(orderItem.getProductId());
            response.setProductSkuId(orderItem.getProductSkuId());
            response.setProductLogo(orderItem.getProductLogo());
        }
        return response;
    }
}
