package com.jsrxjt.mobile.biz.homeScanPay.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.BaseRegion;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayRefundRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayResultRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayResultResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayStatusResultResponse;

import java.util.List;

/**
 * @Description: 扫码提货服务接口
 * @Author: ywt
 * @Date: 2025-05-19 10:23
 * @Version: 1.0
 */
public interface PickScanCaseService {
    PickPaymentCodeResponseDTO getPaymentCode(PickPaymentCodeRequestDTO requestDTO);

    /**
     * 创建订单(外部系统调用)
     *
     * @param requestDTO
     * @return
     */
    PickChannelOrderResponseDTO createChannelOrder(PickChannelOrderRequestDTO requestDTO);

    /**
     * 订单变更
     */
    BaseResponse updateChannelOrder(PickChannelOrderUpdateRequestDTO requestDTO);

    /**
     * 支付状态查询 (外部系统调用)
     */
    PickPlatformPayStatusResultResponse getChannelPayStatus(PickPaymentRefundRequestDTO requestDTO);

    /**
     * 订单退款(外部系统调用)
     */
    PickChannelOrderRefundResponseDTO refundChannelOrder(PickPaymentRefundRequestDTO requestDTO);

    /**
     * 退款状态查询(外部系统调用)
     */
    PickOrderRefundStatusResponseDTO getRefundStatus(PickOrderRefundStatusRequestDTO requestDTO);

    /**
     * 订单退款结果回告[幂等性]
     */
    PickOrderRefundResultResponseDTO getPaymentOrderRefund(PickPlatformPayRefundRequest request);

    /**
     * 订单支付结果回告
     */
    PickPlatformPayResultResponse getPaymentOrderResult(PickPlatformPayResultRequestDTO requestDTO);
}
