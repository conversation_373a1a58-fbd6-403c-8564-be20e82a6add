package com.jsrxjt.mobile.infra.product.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductOffsetPagePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 核销产品说明mapper
 * @Author: ywt
 * @Date: 2025-04-28 11:31
 * @Version: 1.0
 */
@Mapper
public interface ProductOffsetPageMapper extends CommonBaseMapper<ProductOffsetPagePO> {


    List<ProductOffsetPagePO> findBySpuId(@Param("spuId") Long spuId, @Param("productType")Byte productType);
}
