package com.jsrxjt.mobile.biz.module.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.util.geo.GeoUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.config.types.SystemConfigTypeEnum;
import com.jsrxjt.mobile.api.module.dto.request.TabPageRequest;
import com.jsrxjt.mobile.api.module.dto.response.ModuleDetailResponse;
import com.jsrxjt.mobile.api.module.dto.response.PageResponse;
import com.jsrxjt.mobile.api.module.types.ModuleCodeEnum;
import com.jsrxjt.mobile.api.module.types.ModuleDetailType;
import com.jsrxjt.mobile.biz.module.ModuleCaseService;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.global.gateway.GlobalGateWay;
import com.jsrxjt.mobile.domain.global.request.GlobalGoodsListRequest;
import com.jsrxjt.mobile.domain.global.response.GlobalGoodsListResponse;
import com.jsrxjt.mobile.domain.locallife.gateway.LocalLifeGateWay;
import com.jsrxjt.mobile.domain.locallife.request.ActivityListRequest;
import com.jsrxjt.mobile.domain.locallife.response.LocalLifeGoodsResponse;
import com.jsrxjt.mobile.domain.locallife.response.LocalLifeProductGoodsDetailVO;
import com.jsrxjt.mobile.domain.locallife.response.LocalLifeProductInfoVO;
import com.jsrxjt.mobile.domain.locallife.response.SubstationResponse;
import com.jsrxjt.mobile.domain.locallife.types.LocalLifeGoodsEnum;
import com.jsrxjt.mobile.domain.module.entity.*;
import com.jsrxjt.mobile.domain.module.repository.*;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailFactory;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailStrategy;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.swing.plaf.synth.Region;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ModuleCaseServiceImpl implements ModuleCaseService {

    private final RedisUtil redisUtil;

    private final PageRegionRepository pageRegionRepository;

    private final PageInfoRepository pageInfoRepository;

    private final ModuleRegionRelationalRepository moduleRegionRelationalRepository;

    private final ModuleInfoRepository moduleInfoRepository;

    private final ModuleDetailRepository moduleDetailRepository;

    private final GlobalGateWay globalGateWay;

    private final LocalLifeGateWay localLifeGateWay;

    private final ModuleDetailFactory moduleDetailFactory;

    private final RegionRepository regionRepository;

    private final ConfigRepository configRepository;

    @Override
    public List<PageResponse> page(Integer activityId, Integer regionId) {
        Integer pageId = 0;
        if (activityId.equals(0)){
            //首页需要获取当前发布页
            PageInfoEntity  pageInfoEntity = pageInfoRepository.getCurrentHomePage();
            pageId = pageInfoEntity.getPageId();
        }
        Integer cityId = 0;
        Integer districtId = 0;
        RegionEntity selectRegionEntity = regionRepository.getRegionById(regionId);
        if (selectRegionEntity == null){
            log.error("区域不存在");
            return Collections.emptyList();
        }
        if (selectRegionEntity.getRegionType().equals(2)){
            cityId = selectRegionEntity.getId();
        }else if (selectRegionEntity.getRegionType().equals(3)) {
            cityId = selectRegionEntity.getParentId();
            districtId = selectRegionEntity.getId();
        }else {
            log.error("区域类型错误,使用全国页面 regionId = {}", regionId);
        }
        //根据区域获取page_region
        PageRegionEntity pageRegionEntity = pageRegionRepository.getPageRegion(activityId, pageId, cityId, districtId);
        if (pageRegionEntity != null && pageRegionEntity.getPageRegionId() != null){
            Integer pageRegionId = pageRegionEntity.getPageRegionId();
            String key = RedisKeyConstants.PAGE_INFO + pageRegionId;
            String pageInfo = redisUtil.get(key);
            List<PageResponse> pageResponseList = new ArrayList<>();
            //不包含全球购和本地生活tab的组件
            if (StrUtil.isNotBlank(pageInfo)){
                pageResponseList.addAll(JSONUtil.toList(pageInfo, PageResponse.class));
            }else {
                List<PageResponse> basePageResponseList = this.getPageInfo(pageRegionId);
                if (CollectionUtil.isNotEmpty(basePageResponseList)){
                    redisUtil.set(key,JSONUtil.toJsonStr(basePageResponseList), 24* 60 * 60);
                }
                pageResponseList.addAll(basePageResponseList);
            }

            //获取全球购和本地生活tab是否显示
            Integer outGoodsMinNum = 10;
            try {
                String outGoodsMinNumStr = configRepository.getValueByType(SystemConfigTypeEnum.OUT_GOODS_MIN_NUM);
                outGoodsMinNum = Integer.parseInt(outGoodsMinNumStr);
            }catch (Exception e){
                log.error("获取全局购和本地生活是否显示的商品数阈值失败:{}", e.getMessage());
            }
            List<PageResponse> tabPageResponseList = pageResponseList.stream()
                    .filter(pageResponse -> pageResponse.getModuleCode().equals(ModuleCodeEnum.TAB.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(tabPageResponseList)){
                PageResponse tabPageResponse = tabPageResponseList.get(0);
                List<ModuleDetailResponse> localGlobalTabList = new ArrayList<>();
                try {
                    //全球购和本地生活tab组件
                    if (cityId != null && !cityId.equals(0)){
                        //本地生活只使用市查询
                        RegionEntity regionEntity = regionRepository.getRegionById(cityId);
                        if (regionEntity != null){
                            String cityName = regionEntity.getRegionName().substring(0,2);
                            String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + cityName + "*";
                            Set<String> stationSet = redisUtil.scan(stationPattern, 10);
                            if (CollectionUtil.isNotEmpty(stationSet)){
                                Optional<String> firstElement = stationSet.stream().findFirst();
                                String stationValue = firstElement.get();
                                ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = JSONObject.parseObject(stationValue, ModuleLocalLifeInfoEntity.class);
                                Integer productNum = moduleLocalLifeInfoEntity.getProductNum();
                                if (productNum != null && productNum > outGoodsMinNum){
                                    ModuleDetailResponse localDetailResponse = new ModuleDetailResponse();
                                    localDetailResponse.setModuleId(tabPageResponse.getModuleId());
                                    localDetailResponse.setParentDetailId(0);
                                    localDetailResponse.setDetailType(ModuleDetailType.SON_LOCAL_LIFE_TAB.getCode());
                                    localGlobalTabList.add(localDetailResponse);
                                }
                            }
                        }
                    }

                }catch (Exception e){
                    log.error("获取本地生活tab失败:{}",e.getMessage());
                }
                try {
                    //获取全球购商品数量
                    String globalKey = RedisKeyConstants.PAGE_TAB_GLOBAL_GOODS;
                    if (redisUtil.zCard(globalKey).compareTo(Long.valueOf(outGoodsMinNum)) > 0){
                        ModuleDetailResponse globalDetailResponse = new ModuleDetailResponse();
                        globalDetailResponse.setModuleId(tabPageResponse.getModuleId());
                        globalDetailResponse.setParentDetailId(0);
                        globalDetailResponse.setDetailType(ModuleDetailType.SON_GLOBAL_TAB.getCode());
                        localGlobalTabList.add(globalDetailResponse);
                    }
                }catch (Exception e){
                    log.error("获取全球购tab失败:{}",e.getMessage());
                }
                List<ModuleDetailResponse> tabDetailResponseList = tabPageResponse.getModuleDetails();
                if(CollectionUtil.isNotEmpty(tabDetailResponseList)){
                    tabDetailResponseList.addAll(localGlobalTabList);
                }else {
                    tabPageResponse.setModuleDetails(localGlobalTabList);
                }
            }
            return pageResponseList;
        }
        return Collections.emptyList();
    }

    @Override
    public List<PageResponse> getPageInfo(Integer pageRegionId) {
        List<ModuleRegionRelationalEntity> moduleRegionRelationalEntityList = moduleRegionRelationalRepository.getModuleRegionRelational(pageRegionId);
        if (CollectionUtil.isNotEmpty(moduleRegionRelationalEntityList)){
            PageRegionEntity pageRegionEntity = pageRegionRepository.getById(pageRegionId);
            if (pageRegionEntity == null){
                log.error("获取页面区域异常");
                return Collections.emptyList();
            }
            List<Integer> moduleIds = moduleRegionRelationalEntityList.stream()
                    .map(ModuleRegionRelationalEntity::getModuleId).toList();
            List<ModuleInfoEntity> moduleInfoEntityList = moduleInfoRepository.getModuleListByIds(moduleIds);
            if (CollectionUtil.isNotEmpty(moduleInfoEntityList)){
                //筛选出不包含tab的组件
                List<Integer> filterModuleIds = moduleInfoEntityList.stream().filter(moduleInfoEntity -> !moduleInfoEntity.getModuleCode().equals(ModuleCodeEnum.TAB.getCode()))
                        .map(ModuleInfoEntity::getModuleId).collect(Collectors.toList());
                Map<Integer, String> moduleCodeMap = moduleInfoEntityList.stream().collect(Collectors.toMap(ModuleInfoEntity::getModuleId, ModuleInfoEntity::getModuleCode));
                List<ModuleDetailEntity> moduleDetailEntityList = moduleDetailRepository.getAllModuleDetailListByModuleIds(filterModuleIds);
                //赋值区域，用于判断可售性
                moduleDetailEntityList.stream().forEach(moduleDetailEntity -> {
                    moduleDetailEntity.setCityId(pageRegionEntity.getCityId());
                    moduleDetailEntity.setDistrictId(pageRegionEntity.getDistrictId());
                });
                Map<Integer, List<ModuleDetailEntity>> moduleDetailEntityMap = moduleDetailEntityList.stream().collect(Collectors.groupingBy(ModuleDetailEntity::getModuleId));
                List<PageResponse> pageResponseList = new ArrayList<>(moduleInfoEntityList.size());
                for (ModuleInfoEntity moduleInfoEntity : moduleInfoEntityList){
                    PageResponse pageResponse = new PageResponse();
                    if (moduleInfoEntity.getModuleCode().equals(ModuleCodeEnum.TAB.getCode())){
                        //基础tab栏只保存sontab类型详情,单独接口获取详情列表
                        pageResponse.setModuleId(moduleInfoEntity.getModuleId());
                        pageResponse.setModuleCode(moduleInfoEntity.getModuleCode());
                        List<ModuleDetailEntity> tabDetailEntityList = moduleDetailRepository.getTabDetailListByModuleId(moduleInfoEntity.getModuleId());
                        if (CollectionUtil.isNotEmpty(tabDetailEntityList)){
                            pageResponse.setModuleDetails(BeanUtil.copyToList(tabDetailEntityList, ModuleDetailResponse.class));
                        }
                        pageResponseList.add(pageResponse);
                        continue;
                    }
                    BeanUtil.copyProperties(moduleInfoEntity,pageResponse);
                    List<ModuleDetailEntity> onSalemoduleDetailList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                        //组装组件详情
                        List<ModuleDetailEntity> moduleDetailList = moduleDetailEntityMap.get(moduleInfoEntity.getModuleId());
                        if (CollectionUtil.isNotEmpty(moduleDetailList)){
                            //使用组件详情策略生成图片等内容(针对卡券，应用等)
                            updateModuleDetailEntityListInfo(moduleDetailList);
                            for (ModuleDetailEntity moduleDetailEntity : moduleDetailList){
                                moduleDetailEntity.setModuleCode(moduleCodeMap.get(moduleDetailEntity.getModuleId()));
                                moduleDetailEntity.setCityId(pageRegionEntity.getCityId());
                                moduleDetailEntity.setDistrictId(pageRegionEntity.getDistrictId());
                                if (moduleDetailEntity.getOnSale()){
                                    onSalemoduleDetailList.add(moduleDetailEntity);
                                }
                            }
                            if(CollectionUtil.isNotEmpty(onSalemoduleDetailList)){
                                //品牌墙产品图片筛选
                                List<ModuleDetailEntity> brandWallModuleDetailList = onSalemoduleDetailList.stream()
                                        .filter(moduleDetailEntity -> moduleDetailEntity.getModuleCode().equals(ModuleCodeEnum.BRANDS.getCode()))
                                        .collect(Collectors.toList());
                                if (CollectionUtil.isNotEmpty(brandWallModuleDetailList)){
                                    if (brandWallModuleDetailList.size() > 10){
                                        brandWallModuleDetailList.subList(0, 10).stream().forEach(moduleDetailEntity -> moduleDetailEntity.setImgUrl(moduleDetailEntity.getProductImgUrl()));
                                        brandWallModuleDetailList.subList(10, brandWallModuleDetailList.size()).stream().forEach(moduleDetailEntity -> moduleDetailEntity.setImgUrl(moduleDetailEntity.getProductLogoUrl()));
                                    }else {
                                        brandWallModuleDetailList.stream().forEach(moduleDetailEntity -> moduleDetailEntity.setImgUrl(moduleDetailEntity.getProductImgUrl()));
                                    }
                                }
                                onSalemoduleDetailList.sort(Comparator.comparing(ModuleDetailEntity::getSortOrder));
                                List<ModuleDetailResponse> moduleDetailResponseList = this.deepCopyToResponse(onSalemoduleDetailList);
                                pageResponse.setModuleDetails(moduleDetailResponseList);
                            }
                        }
                    }
                    if(!ModuleCodeEnum.BASIC_CONFIG.getCode().equals(moduleInfoEntity.getModuleCode())){
                        //非基础组件，若详情为空，则不显示
                        if (CollectionUtil.isEmpty(onSalemoduleDetailList)){
                            continue;
                        }
                    }
                    pageResponseList.add(pageResponse);
                }
                return pageResponseList;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public void updateAllPageRegionCache() {
        List<PageRegionEntity> pageRegionEntityList = pageRegionRepository.getAllPageRegion();
        String pattern = "page:region:" + "*";
        Set<String> existKeyList = redisUtil.scan(pattern, 100);
        // 如果列表为空，删除所有以page:region:*开头的缓存
        if (pageRegionEntityList == null || pageRegionEntityList.isEmpty()) {
            deleteAllPageRegionRedisKeys(existKeyList);
            return;
        }
        Set<String> dbKeys = pageRegionEntityList.stream()
                .map(entity -> RedisKeyConstants.PAGE_REGION.formatted(entity.getActivityId(), entity.getPageId(), entity.getCityId(), entity.getDistrictId()))
                .collect(Collectors.toSet());
        //遍历当前缓存，删除不存在数据库中的缓存
        if (CollectionUtil.isNotEmpty(existKeyList)){
            for (String redisKey : existKeyList) {
                if (!dbKeys.contains(redisKey)) {
                    redisUtil.delete(redisKey);
                }
            }
        }
        //遍历数据库列表，插入不在Redis中的记录
        for (PageRegionEntity entity : pageRegionEntityList) {
            String redisKey = RedisKeyConstants.PAGE_REGION.formatted(entity.getActivityId(), entity.getPageId(), entity.getCityId(), entity.getDistrictId());
            if (existKeyList == null || !existKeyList.contains(redisKey)) {
                redisUtil.set(redisKey, String.valueOf(entity.getPageRegionId()), true);
            }
        }
    }

    @Override
    public void updateAllPageCache() {
        String pattern = "page:region:" + "*";
        Set<String> existPageRegionKeyList = redisUtil.scan(pattern, 100);
        if (CollectionUtil.isNotEmpty(existPageRegionKeyList)){
            for (String pageRegionKey : existPageRegionKeyList) {
                try {
                    Integer pageRegionId = Integer.valueOf(redisUtil.get(pageRegionKey));
                    List<PageResponse> pageResponseList = this.getPageInfo(pageRegionId);
                    if (CollectionUtil.isNotEmpty(pageResponseList)){
                        String pageKey = RedisKeyConstants.PAGE_INFO + pageRegionId;
                        redisUtil.set(pageKey,JSONUtil.toJsonStr(pageResponseList), 24* 60 * 60);
                    }
                }catch (Exception e){
                    log.error("更新页面缓存异常:{}", e.getMessage());
                }

            }
        }
    }

    @Override
    public void updateAllGlobalGoodsCache() {
        GlobalGoodsListRequest request = new GlobalGoodsListRequest();
        request.setKeyword("RX");
        List<GlobalGoodsListResponse> globalGoodsList = globalGateWay.getGoodsList(request);
        if (globalGoodsList == null){
            return;
        }
        String key = RedisKeyConstants.PAGE_TAB_GLOBAL_GOODS;
        redisUtil.delete(key);
        if (CollectionUtil.isNotEmpty(globalGoodsList)){
            int i = 0;
            Map<String, Double> scoreMembers = new HashMap<>();
            for (GlobalGoodsListResponse globalGoodsListResponse : globalGoodsList){
                ModuleDetailEntity moduleDetailEntity = new ModuleDetailEntity();
                moduleDetailEntity.setProductId(globalGoodsListResponse.getProductId());
                moduleDetailEntity.setProductItemId(globalGoodsListResponse.getProductItemId());
                moduleDetailEntity.setImgUrl(globalGoodsListResponse.getImgUrl());
                moduleDetailEntity.setProductImgUrl(globalGoodsListResponse.getImgUrl());
                moduleDetailEntity.setProductName(globalGoodsListResponse.getProductName());
                moduleDetailEntity.setDetailType(ModuleDetailType.GLOBAL_GOODS.getCode());
                moduleDetailEntity.setProductType(ModuleDetailType.GLOBAL_GOODS.getCode());
                moduleDetailEntity.setPromotionLabels(globalGoodsListResponse.getPromotionLabels());
                moduleDetailEntity.setProductPrice(globalGoodsListResponse.getPrice() != null? globalGoodsListResponse.getPrice().toString() : null);
                moduleDetailEntity.setProductOriginalPrice(globalGoodsListResponse.getOriginalPrice() != null? globalGoodsListResponse.getOriginalPrice().toString() : null);
                scoreMembers.put(JSONObject.toJSONString(moduleDetailEntity), Double.valueOf(i));
                i += 1;
            }
            redisUtil.zadd(key, scoreMembers);
        }
    }

    @Override
    public void updateAllLocalLifeGoodsCache() {
        //获取所有站点
        List<SubstationResponse> substationResponseList = localLifeGateWay.getSubstationList("");
        if (substationResponseList == null){
            return;
        }
        String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + "*";
        //删除所有站点信息的缓存(后续重新生成)
        Set<String> existStationList = redisUtil.scan(stationPattern, 100);
        if (CollectionUtil.isNotEmpty(existStationList)){
            for (String redisKey : existStationList) {
                redisUtil.delete(redisKey);
            }
        }
        if(CollectionUtil.isEmpty(substationResponseList)){
            redisUtil.delete(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE);
            return;
        }
        for (SubstationResponse substationResponse : substationResponseList){
            ActivityListRequest request = new ActivityListRequest();
            request.setWebSiteId(substationResponse.getSiteId());
            request.setLabelType(10); //热门推荐
            List<ModuleDetailEntity> moduleDetailEntityList = getAllModuleLocalLifeResponse(request);
            if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                String fieldKey = substationResponse.getSiteId() + ":" + 10;   //使用站点id和标签类型作为字段名
                redisUtil.hset(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, fieldKey, JSONArray.toJSONString(moduleDetailEntityList));
            }
            request.setLabelType(50); //全部
            moduleDetailEntityList = getAllModuleLocalLifeResponse(request);
            if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                String fieldKey = substationResponse.getSiteId() + ":" + 50;
                redisUtil.hset(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, fieldKey, JSONArray.toJSONString(moduleDetailEntityList));
            }
            String stationKey = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + substationResponse.getSiteAreaName();
            ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = new ModuleLocalLifeInfoEntity();
            moduleLocalLifeInfoEntity.setSiteId(substationResponse.getSiteId());
            moduleLocalLifeInfoEntity.setSiteAreaName(substationResponse.getSiteAreaName());
            moduleLocalLifeInfoEntity.setProductNum(CollectionUtil.isNotEmpty(moduleDetailEntityList)? moduleDetailEntityList.size():0);
            redisUtil.set(stationKey, JSONObject.toJSONString(moduleLocalLifeInfoEntity), 24* 60 * 60);
        }
    }

    @Override
    public void updateAllTabCache() {
        String pattern = "page:region:" + "*";
        Set<String> existPageRegionKeyList = redisUtil.scan(pattern, 100);
        if (CollectionUtil.isNotEmpty(existPageRegionKeyList)){
            for (String pageRegionKey : existPageRegionKeyList) {
                Integer pageRegionId = Integer.valueOf(redisUtil.get(pageRegionKey));
                List<ModuleRegionRelationalEntity> moduleRegionRelationalEntityList = moduleRegionRelationalRepository.getModuleRegionRelational(pageRegionId);
                if (CollectionUtil.isNotEmpty(moduleRegionRelationalEntityList)){
                    List<Integer> moduleIds = moduleRegionRelationalEntityList.stream().map(ModuleRegionRelationalEntity::getModuleId).collect(Collectors.toList());
                    List<ModuleInfoEntity> moduleInfoEntityList = moduleInfoRepository.getModuleListByIds(moduleIds);
                    List<ModuleInfoEntity> tabModuleInfoEntityList = moduleInfoEntityList.stream().filter(moduleInfoEntity -> moduleInfoEntity.getModuleCode().equals(ModuleCodeEnum.TAB.getCode())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(tabModuleInfoEntityList)){
                        //获取tab组件下detailtype为sontab的详情列表
                        tabModuleInfoEntityList.forEach(moduleInfoEntity ->{
                            List<ModuleDetailEntity> moduleDetailEntityList = moduleDetailRepository.getTabDetailListByModuleId(moduleInfoEntity.getModuleId());
                            if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                                moduleDetailEntityList.forEach(moduleDetailEntity ->{
                                    List<ModuleDetailResponse> detailResponseList = this.getTabInfo(pageRegionId, moduleDetailEntity.getDetailId());
                                    String pageTabKey = RedisKeyConstants.PAGE_TAB_INFO.formatted(pageRegionId, moduleDetailEntity.getDetailId());
                                    this.addTabCache(detailResponseList, pageTabKey);
                                });
                            }
                        });
                    }
                }
            }
        }
    }

    @Override
    public PageDTO<ModuleDetailResponse> tabPage(TabPageRequest request) {
        Integer activityId = request.getActivityId();
        Integer regionId = request.getRegionId();
        Integer moduleDetailId = request.getModuleDetailId();
        Integer detailType = request.getDetailType();
        Integer pageRows = request.getPageRows();
        Integer toPage = request.getToPage();
        Integer pageId = 0;
        if (activityId.equals(0)){
            //首页需要获取当前发布页
            PageInfoEntity  pageInfoEntity = pageInfoRepository.getCurrentHomePage();
            pageId = pageInfoEntity.getPageId();
        }else {
            //活动页pageId=activityId
            pageId = activityId;
        }
        Integer cityId = 0;
        Integer districtId = 0;
        RegionEntity selectRegionEntity = regionRepository.getRegionById(regionId);
        if (selectRegionEntity == null){
            log.error("区域不存在");
            return PageDTO.build(null, 0l,  Long.valueOf(pageRows), Long.valueOf(toPage));
        }
        if (selectRegionEntity.getRegionType().equals(2)){
            cityId = selectRegionEntity.getId();
        }else if (selectRegionEntity.getRegionType().equals(3)) {
            cityId = selectRegionEntity.getParentId();
            districtId = selectRegionEntity.getId();
        }else {
            log.error("区域类型错误,使用全国页面 regionId = {}", regionId);
        }
        Double lat = null;
        Double lng = null;
        try {
            Long customerId = Long.parseLong((String) StpUtil.getLoginId());
            RegionEntity userRegionEntity = regionRepository.getCurrentRegion(customerId);
            lat = Double.parseDouble(userRegionEntity.getLat());
            lng = Double.parseDouble(userRegionEntity.getLng());
        }catch (Exception e){
            log.error("未获取到用户区域信息:{}",e.getMessage());
        }
        //根据区域获取page_region
        PageRegionEntity pageRegionEntity = pageRegionRepository.getPageRegion(activityId, pageId, cityId, districtId);
        if (pageRegionEntity != null && pageRegionEntity.getPageRegionId() != null) {
            Integer pageRegionId = pageRegionEntity.getPageRegionId();
            Long total = 0l;
            List<ModuleDetailResponse> detailResponseList = new ArrayList<>();
            if (moduleDetailId != null){
                String pageTabKey = RedisKeyConstants.PAGE_TAB_INFO.formatted(pageRegionId, moduleDetailId);
                if(redisUtil.exists(pageTabKey)){
                    total = redisUtil.zCard(pageTabKey);
                    Set<String> detailResponseSet = redisUtil.zrange(pageTabKey, toPage * pageRows - pageRows, toPage * pageRows - 1);
                    if (CollectionUtil.isNotEmpty(detailResponseSet)){
                        detailResponseList = detailResponseSet.stream()
                                .map(detailResponse -> JSONObject.parseObject(detailResponse, ModuleDetailResponse.class))
                                .collect(Collectors.toList());
                    }
                }else {
                    List<ModuleDetailResponse> moduleDetailEntityList = getTabInfo(pageRegionId, moduleDetailId);
                    if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
                        this.addTabCache(moduleDetailEntityList, pageTabKey);
                        total = Long.valueOf(moduleDetailEntityList.size());
                        detailResponseList = moduleDetailEntityList.stream().skip(toPage * pageRows - pageRows).limit(pageRows).collect(Collectors.toList());
                    }
                }
                if(CollectionUtil.isNotEmpty(detailResponseList)){
                  this.goodsDistance(detailResponseList, lat, lng);
                }
            }else if (detailType != null){
                //全球购or本地生活tab(全球购和本地生活缓存中存储是ModuleDetailEntity列表,需要转成ModuleDetailResponse)
                if(detailType.equals(ModuleDetailType.SON_GLOBAL_TAB.getCode())){
                    String globalKey = RedisKeyConstants.PAGE_TAB_GLOBAL_GOODS;
                    total = redisUtil.zCard(globalKey);
                    Set<String> detailResponseSet = redisUtil.zrange(globalKey, toPage * pageRows - pageRows, toPage * pageRows - 1);
                    if (CollectionUtil.isNotEmpty(detailResponseSet)){
                        List<ModuleDetailEntity> detailEntityList = detailResponseSet.stream()
                                .map(detailEntity -> JSONObject.parseObject(detailEntity, ModuleDetailEntity.class))
                                .collect(Collectors.toList());
                        detailResponseList = this.deepCopyToResponse(detailEntityList);
                    }
                }else if (detailType.equals(ModuleDetailType.SON_LOCAL_LIFE_TAB.getCode())){
                    if (cityId != null && !cityId.equals(0)){
                        //本地生活只使用城市id获取
                        RegionEntity regionEntity = regionRepository.getRegionById(cityId);
                        if (regionEntity != null){
                            String cityName = regionEntity.getRegionName().substring(0,2);
                            String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + cityName + "*";
                            Set<String> stationSet = redisUtil.scan(stationPattern, 10);
                            if (CollectionUtil.isNotEmpty(stationSet)){
                                Optional<String> firstElement = stationSet.stream().findFirst();
                                String stationKey = firstElement.get();
                                String stationValue = redisUtil.get(stationKey);
                                ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = JSONObject.parseObject(stationValue, ModuleLocalLifeInfoEntity.class);
                                Long siteId = moduleLocalLifeInfoEntity.getSiteId();
                                if (siteId != null){
                                    String field = siteId + ":" + 50; //全部
                                    String globalValue = redisUtil.hget(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, field);
                                    if (StringUtils.isNotEmpty(globalValue)){
                                        List<ModuleDetailEntity> localModuleDetailEntityList = JSONArray.parseArray(globalValue, ModuleDetailEntity.class);
                                        total = Long.valueOf(localModuleDetailEntityList.size());
                                        List<ModuleDetailEntity> detailEntityList = localModuleDetailEntityList.stream().skip(toPage * pageRows - pageRows).limit(pageRows).collect(Collectors.toList());
                                        detailResponseList = this.deepCopyToResponse(detailEntityList);
                                        if(CollectionUtil.isNotEmpty(detailResponseList)){
                                            this.goodsDistance(detailResponseList, lat, lng);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return PageDTO.build(detailResponseList, total,  Long.valueOf(pageRows) , Long.valueOf(toPage));
        }
        return PageDTO.build(null, 0l,  Long.valueOf(pageRows), Long.valueOf(toPage));
    }


    @Override
    public void handleReleasePage(Integer pageId, Integer activityId) {
        List<PageRegionEntity> pageRegionEntityList = pageRegionRepository.getAllPageRegionByPageId(pageId, activityId);
        if (CollectionUtil.isEmpty(pageRegionEntityList)){
            log.error("未获取到页面区域 pageId = {}", pageId);
        }
        pageRegionEntityList.forEach(pageRegionEntity -> {
            Integer pageRegionId = pageRegionEntity.getPageRegionId();
            String key = RedisKeyConstants.PAGE_INFO + pageRegionId;
            redisUtil.delete(key);
            List<PageResponse> basePageResponseList = this.getPageInfo(pageRegionId);
            if (CollectionUtil.isNotEmpty(basePageResponseList)){
                redisUtil.set(key,JSONUtil.toJsonStr(basePageResponseList), 24* 60 * 60);
                basePageResponseList.stream().filter(pageResponse -> pageResponse.getModuleCode().equals(ModuleCodeEnum.TAB.getCode()))
                        .forEach(pageResponse -> {
                            pageResponse.getModuleDetails().stream().filter(moduleDetailResponse -> moduleDetailResponse.getDetailType().equals(ModuleDetailType.SON_TAB.getCode()))
                                    .forEach(moduleDetailResponse -> {
                                        Integer detailId = moduleDetailResponse.getDetailId();
                                        List<ModuleDetailResponse> detailResponseList = this.getTabInfo(pageRegionId, detailId);
                                        String pageTabKey = RedisKeyConstants.PAGE_TAB_INFO.formatted(pageRegionId, detailId);
                                        this.addTabCache(detailResponseList, pageTabKey);
                                    });
                        });
            }
        });
    }

    /**
     * 普通son tab(区分全球购和本地生活tab)内容
     * @param pageRegionId
     * @param moduleDetailId
     * @return {@link List}<{@link ModuleDetailResponse}>
     */
    public List<ModuleDetailResponse> getTabInfo(Integer pageRegionId, Integer moduleDetailId) {
        PageRegionEntity pageRegionEntity = pageRegionRepository.getById(pageRegionId);
        if (pageRegionEntity == null){
            log.error("获取页面区域异常");
            return null;
        }
        ModuleDetailEntity tabModuleDetailEntity = moduleDetailRepository.getById(moduleDetailId);
        if (tabModuleDetailEntity == null){
            log.error("未获取tab组件详情, moduleDetailId={}", moduleDetailId);
            return null;
        }
        List<ModuleDetailEntity> allModuleDetailEntityList = moduleDetailRepository.getAllModuleDetailListByModuleIds(Arrays.asList(tabModuleDetailEntity.getModuleId()));
        if (CollectionUtil.isEmpty(allModuleDetailEntityList)) {
            log.error("未获取tab组件, moduleDetailId={}", moduleDetailId);
            return null;
        }
        //赋值区域，用于判断可售性
        allModuleDetailEntityList.stream().forEach(moduleDetailEntity -> {
            moduleDetailEntity.setCityId(pageRegionEntity.getCityId());
            moduleDetailEntity.setDistrictId(pageRegionEntity.getDistrictId());
        });
        tabModuleDetailEntity = allModuleDetailEntityList.stream()
                .filter(moduleDetail -> moduleDetail.getDetailId().equals(moduleDetailId))
                .findFirst().get();
        if (tabModuleDetailEntity != null && tabModuleDetailEntity.getDetailType().equals(ModuleDetailType.SON_TAB.getCode())) {
            if (CollectionUtil.isNotEmpty(allModuleDetailEntityList)) {
                List<ModuleDetailEntity> moduleDetailEntityList = tabModuleDetailEntity.getChildren();
                if (CollectionUtil.isEmpty(moduleDetailEntityList)){
                    log.error("未获取tab组件详情列表, moduleDetailId={}", moduleDetailId);
                    return null;
                }
                this.updateModuleDetailEntityListInfo(moduleDetailEntityList);
                List<ModuleDetailEntity> onSaleModuleDetailList = new ArrayList<>();
                for (ModuleDetailEntity moduleDetailEntity : moduleDetailEntityList) {
                    moduleDetailEntity.setModuleCode(ModuleCodeEnum.TAB.getCode());
                    moduleDetailEntity.setCityId(pageRegionEntity.getCityId());
                    moduleDetailEntity.setDistrictId(pageRegionEntity.getDistrictId());
                    if (moduleDetailEntity.getOnSale()) {
                        onSaleModuleDetailList.add(moduleDetailEntity);
                    }
                }
                if (CollectionUtil.isNotEmpty(onSaleModuleDetailList)){
                    //组装本地生活
                    List<ModuleDetailEntity> localModuleDetailList = new ArrayList<>();
                    try {
                        //本地生活tab组件
                        Integer cityId = pageRegionEntity.getCityId();
                        if (cityId != null && !cityId.equals(0)){
                            //本地生活只使用市查询
                            RegionEntity regionEntity = regionRepository.getRegionById(cityId);
                            if (regionEntity != null){
                                String cityName = regionEntity.getRegionName().substring(0,2);
                                String stationPattern = RedisKeyConstants.PAGE_TAB_LOCAL_LIFE_STATION + cityName + "*";
                                Set<String> stationSet = redisUtil.scan(stationPattern, 10);
                                if (CollectionUtil.isNotEmpty(stationSet)){
                                    Optional<String> firstElement = stationSet.stream().findFirst();
                                    String stationKey = firstElement.get();
                                    String stationValue = redisUtil.get(stationKey);
                                    ModuleLocalLifeInfoEntity moduleLocalLifeInfoEntity = JSONObject.parseObject(stationValue, ModuleLocalLifeInfoEntity.class);
                                    Long siteId = moduleLocalLifeInfoEntity.getSiteId();
                                    if (siteId != null){
                                        String field = siteId + ":" + 10; //推荐
                                        String globalValue = redisUtil.hget(RedisKeyConstants.PAGE_TAB_LOCAL_LIFE, field);
                                        if (StringUtils.isNotEmpty(globalValue)){
                                            localModuleDetailList = JSONArray.parseArray(globalValue, ModuleDetailEntity.class);
                                        }
                                    }
                                }
                            }
                        }
                    }catch (Exception e){
                        log.error("获取本地生活tab失败:{}",e.getMessage());
                    }
                    int localIndex = 0;
                    List<ModuleDetailEntity> tabModuleDetailList = new ArrayList<>();
                    for (ModuleDetailEntity moduleDetailEntity : onSaleModuleDetailList) {
                        if (moduleDetailEntity.getDetailType().equals(ModuleDetailType.LOCAL_LIFE.getCode())){
                            if (localIndex >= localModuleDetailList.size()){
                                continue;
                            }
                            ModuleDetailEntity localModuleDetailEntity = localModuleDetailList.get(localIndex);
                            localModuleDetailEntity.setDetailId(moduleDetailEntity.getDetailId());
                            tabModuleDetailList.add(localModuleDetailEntity);
                            localIndex += 1;
                        }else {
                            tabModuleDetailList.add(moduleDetailEntity);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(tabModuleDetailList)){
                       return this.deepCopyToResponse(tabModuleDetailList);
                    }
                }
            }
        }
        return null;
    }

    private void addTabCache(List<ModuleDetailResponse> moduleDetailEntityList, String key){
        if (CollectionUtil.isNotEmpty(moduleDetailEntityList)){
            Map<String, Double> scoreMembers = new HashMap<>();
            for (int i = 0; i < moduleDetailEntityList.size(); i++){
                scoreMembers.put(JSONObject.toJSONString(moduleDetailEntityList.get(i)), Double.valueOf(i));
            }
            redisUtil.delete(key);
            redisUtil.zadd(key,scoreMembers);
            redisUtil.expire(key, 24* 60 * 60);
        }
    }

    private void updateModuleDetailEntityListInfo(List<ModuleDetailEntity> entities){
        if (CollectionUtil.isEmpty(entities)) {
            return;
        }
        entities.forEach(entity -> updateEntityInfo(entity));
    }

    private void updateEntityInfo(ModuleDetailEntity entity){
        if (entity == null) {
            return;
        }
        ModuleDetailStrategy moduleDetailStrategy = moduleDetailFactory.getModuleDetailStrategy(ModuleDetailType.getByCode(entity.getDetailType()));
        if (moduleDetailStrategy != null){
            moduleDetailStrategy.updateModuleDetailEntityInfo(entity);
        }
        // 递归更新子节点
        updateModuleDetailEntityListInfo(entity.getChildren());
        if (CollectionUtil.isNotEmpty(entity.getChildren())){
            //子节点中无上架商品，则父节点不上架
            if (entity.getChildren().stream().filter(child -> child.getOnSale().equals(true)).count() == 0){
                entity.setOnSale(false);
            }
        }
    }

    // 将 List<ModuleDetailEntity> 深拷贝为 List<ModuleDetailResponse>
    private List<ModuleDetailResponse> deepCopyToResponse(List<ModuleDetailEntity> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return null;
        }
        return entities.stream()
                .map(this::entityToResponse)
                .collect(Collectors.toList());
    }

    // 将单个 ModuleDetailEntity 转换为 ModuleDetailResponse
    private ModuleDetailResponse entityToResponse(ModuleDetailEntity entity) {
        if (entity == null) {
            return null;
        }
        ModuleDetailResponse response = new ModuleDetailResponse();
        response.setDetailId(entity.getDetailId());
        response.setModuleId(entity.getModuleId());
        response.setParentDetailId(entity.getParentDetailId());
        response.setDetailTitle(entity.getDetailTitle());
        response.setDetailSubTitle(entity.getDetailSubTitle());
        response.setDetailType(entity.getDetailType());
        response.setProductId(entity.getProductId());
        response.setProductName(entity.getProductName());
        response.setProductSubName(entity.getProductSubName());
        response.setProductPrice(entity.getProductPrice());
        response.setProductOriginalPrice(entity.getProductOriginalPrice());
        response.setProductType(entity.getProductType());
        response.setImgUrl(entity.getImgUrl());
        response.setContent(entity.getContent());
        response.setSortOrder(entity.getSortOrder());
        response.setSubscriptImg(entity.getSubscriptImg());
        response.setMiniAppId(entity.getMiniAppId());
        response.setDistance(entity.getDistance());
        response.setGhId(entity.getGhId());
        response.setLabelCopy(entity.getLabelCopy());
        response.setPromotionLabels(entity.getPromotionLabels());
        response.setAvailableTimeStr(entity.getAvailableTimeStr());
        response.setDiscountRate(entity.getDiscountRate());
        response.setLat(entity.getLat());
        response.setLng(entity.getLng());
        // 递归拷贝子节点
        List<ModuleDetailResponse> childrenResponses = deepCopyToResponse(entity.getChildren());
        response.setChildren(childrenResponses != null ? childrenResponses : new ArrayList<>());

        return response;
    }

    private void deleteAllPageRegionRedisKeys(Set<String> existKeyList) {
        if (CollectionUtil.isNotEmpty(existKeyList)){
            for (String redisKey : existKeyList) {
              redisUtil.delete(redisKey);
            }
        }
    }

    private List<ModuleDetailEntity> getAllModuleLocalLifeResponse(ActivityListRequest request){
        List<LocalLifeGoodsResponse> localLifeGoodsList =localLifeGateWay.getLocalActivities(request);
        if (CollectionUtil.isEmpty(localLifeGoodsList)){
            return Collections.emptyList();
        }
        List<ModuleDetailEntity> moduleDetailEntityList = new ArrayList<>();
        for (LocalLifeGoodsResponse localLifeGoodsResponse : localLifeGoodsList){
            ModuleDetailEntity moduleDetailEntity = new ModuleDetailEntity();
            if (localLifeGoodsResponse.getProductsType().equals(LocalLifeGoodsEnum.SHOP.getCode())){
                //商家
                moduleDetailEntity.setProductId(String.valueOf(localLifeGoodsResponse.getProductsId()));
                moduleDetailEntity.setProductName(localLifeGoodsResponse.getProductsName());
                moduleDetailEntity.setProductType(ModuleDetailType.LOCAL_LIFE.getCode() + LocalLifeGoodsEnum.SHOP.getCode());
                moduleDetailEntity.setDetailType(ModuleDetailType.LOCAL_LIFE.getCode());
                if (localLifeGoodsResponse.getProductInfo() != null && CollectionUtil.isNotEmpty(localLifeGoodsResponse.getProductInfo().getGoodsList())){
                    LocalLifeProductInfoVO productInfo = localLifeGoodsResponse.getProductInfo();
                    List<String> promotionLabels = new ArrayList<>();
                    for (LocalLifeProductGoodsDetailVO localGoods : localLifeGoodsResponse.getProductInfo().getGoodsList()){
                        if (localGoods.getGoodsType() != null && localGoods.getGoodsType().equals(1)){
                            promotionLabels.add("券" + localGoods.getGoodsName());
                        }else {
                            promotionLabels.add("惠" + localGoods.getGoodsName());
                        }
                    }
                    moduleDetailEntity.setImgUrl(productInfo.getLogo());
                    moduleDetailEntity.setLat(StringUtils.isEmpty(productInfo.getLatitude())? null : productInfo.getLatitude());
                    moduleDetailEntity.setLng(StringUtils.isEmpty(productInfo.getLongitude())? null : productInfo.getLongitude());
                    moduleDetailEntity.setPromotionLabels(promotionLabels);
                }
                moduleDetailEntityList.add(moduleDetailEntity);
            }else if (localLifeGoodsResponse.getProductsType().equals(LocalLifeGoodsEnum.COUPON.getCode())
                    || localLifeGoodsResponse.getProductsType().equals(LocalLifeGoodsEnum.PACKAGE.getCode())){
                //券
                if (localLifeGoodsResponse.getProductInfo() == null){
                    continue;
                }
                LocalLifeProductInfoVO productInfo = localLifeGoodsResponse.getProductInfo();
                moduleDetailEntity.setProductId(String.valueOf(productInfo.getSkuId()));
                moduleDetailEntity.setProductName(localLifeGoodsResponse.getProductsName());
                moduleDetailEntity.setProductType(ModuleDetailType.LOCAL_LIFE.getCode() +  localLifeGoodsResponse.getProductsType());
                moduleDetailEntity.setDetailType(ModuleDetailType.LOCAL_LIFE.getCode());
                moduleDetailEntity.setDiscountRate(productInfo.getDiscountRate());
                moduleDetailEntity.setProductPrice(productInfo.getPrice());
                moduleDetailEntity.setProductOriginalPrice(productInfo.getMarketPrice());
                moduleDetailEntity.setAvailableTimeStr(productInfo.getAvailableTimeStr());
                moduleDetailEntity.setImgUrl(productInfo.getGoodsImage());
                moduleDetailEntity.setLat(StringUtils.isEmpty(productInfo.getLatitude())? null : productInfo.getLatitude());
                moduleDetailEntity.setLng(StringUtils.isEmpty(productInfo.getLongitude())? null : productInfo.getLongitude());
                moduleDetailEntityList.add(moduleDetailEntity);
            }else {
                log.error("未知的本地生活商品类型:{}", localLifeGoodsResponse.getProductsType());
            }
        }
        return moduleDetailEntityList;
    }


    /**
     * 获取商品距离
     * @param detailResponseList
     * @param lat
     * @param lng
     */
    private void goodsDistance(List<ModuleDetailResponse> detailResponseList, Double lat, Double lng){
        try {
            if (lat == null || lng == null){
                return;
            }
            for (ModuleDetailResponse moduleDetailResponse : detailResponseList){
                if (moduleDetailResponse.getLat() != null && moduleDetailResponse.getLng() != null){
                    Double distance = GeoUtil.getDistance(lat, lng, Double.parseDouble(moduleDetailResponse.getLat()), Double.parseDouble(moduleDetailResponse.getLng()));
                    moduleDetailResponse.setDistance(distance.intValue());
                }
            }
        }catch (Exception e){
            log.error("更新商品距离异常:{}",e.getMessage());
        }
    }
}
