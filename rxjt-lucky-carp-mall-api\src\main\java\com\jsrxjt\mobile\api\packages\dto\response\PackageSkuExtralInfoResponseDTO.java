package com.jsrxjt.mobile.api.packages.dto.response;

import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.api.ticket.BrandTicketResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 套餐sku的其他信息，如活动信息等
 * @Author: ywt
 * @Date: 2025-07-16 09:28
 * @Version: 1.0
 */
@Data
public class PackageSkuExtralInfoResponseDTO {
    @Schema(description = "套餐skuId")
    private Long packageSkuId;
    @Schema(description = "套餐spuid")
    private Long packageSpuId;
    @Schema(description = "sku的促销活动信息，为空表示没有活动")
    private PromotionSkuInfo promotionInfo;
    @Schema(description = "优惠券信息")
    private List<BrandTicketResponseDTO> brandTicketList;
}
