package com.jsrxjt.mobile.infra.ticket.persistent.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 优惠券发放表实体类
 * <AUTHOR>
 */
@Data
@TableName("ticket_delivery")
public class TicketDeliveryPO {
    
    /**
     * 优惠券订单编号
     */
    @TableId
    private Long id;
    
    /**
     * 关联的订单id
     */
    private Long ticketOrderNo;
    
    /**
     * 关联订单编号
     */
    private String orderNo;
    
    /**
     * 外部订单编号
     */
    private String externalOrderNo;
    
    /**
     * 优惠券类型：0商家自发优惠券 2瑞祥代发优惠券
     */
    private Byte ticketType;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 核销状态: 1未核销 2已核销，仅瑞祥代发优惠券有效
     */
    private Byte status;
    
    /**
     * 卡营coupon_id
     */
    private String centerCouponId;
    
    /**
     * 卡券卡号
     */
    private String ticketCode;
    
    /**
     * 卡券卡密
     */
    private String ticketPin;
    
    /**
     * 优惠券id
     */
    private Long ticketId;
    
    /**
     * 优惠券名称
     */
    private String ticketName;
    
    /**
     * 券的有效期
     */
    private Date ticketValidDate;
    
    /**
     * 核销页样式 0卡号 1转码
     */
    private Byte offsetPageType;
    
    /**
     * 品牌名
     */
    private String brandName;
    
    /**
     * 核销logo
     */
    private String offsetLogo;
    
    /**
     * 使用说明
     */
    private String useManual;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 创建人
     */
    private Long createId;
    
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modTime;
    
    /**
     * 修改人
     */
    private Long modId;
    
    /**
     * 删除标记: 1:已删除状态 0:正常状态
     */
    private Integer delFlag;

}
