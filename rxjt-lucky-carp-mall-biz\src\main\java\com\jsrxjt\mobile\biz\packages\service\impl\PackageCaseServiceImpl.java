package com.jsrxjt.mobile.biz.packages.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.advertisement.types.AdvertisementTypeEnum;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.coupon.types.CouponStatus;
import com.jsrxjt.mobile.api.packages.dto.request.PackageInfoRequestDTO;
import com.jsrxjt.mobile.api.packages.dto.request.PackageSkuExtraInfoRequestDTO;
import com.jsrxjt.mobile.api.packages.dto.response.PackageInfoResponseDTO;
import com.jsrxjt.mobile.api.packages.dto.response.PackageSkuExtralInfoResponseDTO;
import com.jsrxjt.mobile.api.packages.dto.response.PackageSkuInfoResponseDTO;
import com.jsrxjt.mobile.api.product.dto.ProductExplainResponseDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.api.promotion.types.PromotionActivityTypeEnum;
import com.jsrxjt.mobile.api.ticket.BrandTicketResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketResponseDTO;
import com.jsrxjt.mobile.biz.packages.service.PackageCaseService;
import com.jsrxjt.mobile.domain.advertisement.entity.AdvertisementEntity;
import com.jsrxjt.mobile.domain.advertisement.service.AdvertisementService;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsSkuRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductLableEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductSpecsEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductSpecsValueEntity;
import com.jsrxjt.mobile.domain.product.repository.ProductSpecsRepository;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.promotion.service.PromotionService;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.service.TicketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 套餐服务
 * @Author: ywt
 * @Date: 2025-05-09 17:40
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PackageCaseServiceImpl implements PackageCaseService {
    private final PackageGoodsRepository packageGoodsRepository;
    private final PackageGoodsSkuRepository packageGoodsSkuRepository;
    private final ProductSkuSellRegionService productSkuSellRegionService;
    private final AdvertisementService advertisementService;
    private final ContentRegionService contentRegionService;
    private final ProductSpecsRepository productSpecsRepository;
    private final PromotionService promotionService;
    private final TicketService ticketService;

    @Override
    public BaseResponse<PackageInfoResponseDTO> packageInfo(PackageInfoRequestDTO requestDTO) {
        log.info("套餐packageInfo--{}:{}:{}", requestDTO.getPackageSpuId(), requestDTO.getPackageSkuId(), requestDTO.getRegionId());
        //获取指定的sku信息，若packageSkuId为空返回第一个区域可售sku
        List<PackageGoodsSkuEntity> skuEntityList = packageGoodsSkuRepository.getPackageGoodsSku(requestDTO.getPackageSpuId());
        if (CollectionUtil.isEmpty(skuEntityList)) {
            log.error("套餐：" + requestDTO.getPackageSpuId() + "的sku为空错误");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }
        //SPU的规格信息
        List<ProductSpecsEntity> specsEntityList = productSpecsRepository.getSpecInfoBySpuId(requestDTO.getPackageSpuId(), ProductTypeEnum.PACKAGE.getType());
        if (CollectionUtil.isEmpty(specsEntityList)) {
            log.error("套餐：" + requestDTO.getPackageSpuId() + ":规格为空错误");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }
        //这里只考虑单规格
        ProductSpecsEntity specsEntity = specsEntityList.get(0);
        List<ProductSpecsValueEntity> specsValueEntityList = specsEntity.getSpecsValueList();
        if (CollectionUtil.isEmpty(specsValueEntityList)) {
            log.error("套餐：" + requestDTO.getPackageSpuId() + ":规格值为空错误");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }
        PackageGoodsSkuEntity data = null;
        for (PackageGoodsSkuEntity skuEntity : skuEntityList) {
            if (Objects.isNull(requestDTO.getPackageSkuId())) {
                if (productSkuSellRegionService.isSellableInRegion(requestDTO.getPackageSpuId(), skuEntity.getId(), ProductTypeEnum.PACKAGE, requestDTO.getRegionId())) {
                    //找到区域第一个可售sku
                    data = skuEntity;
                    break;
                }
            } else if (requestDTO.getPackageSkuId().longValue() == skuEntity.getId().longValue()) {
                //获取到指定的sku
                if (!productSkuSellRegionService.isSellableInRegion(requestDTO.getPackageSpuId(), skuEntity.getId(), ProductTypeEnum.PACKAGE, requestDTO.getRegionId())) {
                    log.error("套餐SPU:" + requestDTO.getPackageSpuId() + "的sku:" + requestDTO.getPackageSkuId() + "在区域:" + requestDTO.getRegionId() + "不可售");
                    return BaseResponse.fail(Status.REGION_NOT_SALE.getCode(), Status.REGION_NOT_SALE.getMessage());
                }
                data = skuEntity;
                break;
            }
        }
        if (Objects.isNull(data)) {
            log.error("套餐SPU:" + requestDTO.getPackageSpuId() + "在区域：" + requestDTO.getRegionId() + "没有可售的sku");
            return BaseResponse.fail(Status.NOT_HAVE_SKU_SALE.getCode(), Status.NOT_HAVE_SKU_SALE.getMessage());
        }

        for (ProductSpecsValueEntity valueEntity : specsValueEntityList) {
            if (valueEntity.getId().longValue() == data.getSpecValueId().longValue()) {
                data.setSpecValueName(valueEntity.getSpecsValue());
                break;
            }
        }

        PackageSkuInfoResponseDTO skuInfoResponseDTO = new PackageSkuInfoResponseDTO();
        BeanUtils.copyProperties(data, skuInfoResponseDTO);
        skuInfoResponseDTO.setIsSelected(1);
        skuInfoResponseDTO.setInventoryStatus(1);
        PromotionSkuInfo promotionSkuInfo = promotionService.getPromotionInfoBySkuIdAndCouponType(skuInfoResponseDTO.getId(), ProductTypeEnum.PACKAGE.getType());
        if (Objects.nonNull(promotionSkuInfo)) {
            if (promotionSkuInfo.getActivityType().intValue() == PromotionActivityTypeEnum.DISCOUNT.getType().intValue()) {
                if (Objects.nonNull(promotionSkuInfo.getDiscount())) {
                    promotionSkuInfo.setActivityPrice(skuInfoResponseDTO.getPlatformPrice().multiply(new BigDecimal(promotionSkuInfo.getDiscount())).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                } else {
                    promotionSkuInfo.setActivityPrice(skuInfoResponseDTO.getPlatformPrice());
                }
            }
            skuInfoResponseDTO.setPromotionInfo(promotionSkuInfo);
        }
        setSkuStatus(data.getId(), skuInfoResponseDTO);

        //优惠券信息
        skuInfoResponseDTO.setBrandTicketList(getTicketsBySku(skuInfoResponseDTO.getId(), requestDTO.getRegionId()));

        //获取spu信息
        PackageGoodsEntity goodsEntity = packageGoodsRepository.getPackageGoodsInfo(requestDTO.getPackageSpuId());
        if (Objects.isNull(goodsEntity)) {
            log.error("套餐：" + requestDTO.getPackageSpuId() + "不存在");
            return null;
        }
        PackageInfoResponseDTO responseDTO = new PackageInfoResponseDTO();
        BeanUtils.copyProperties(goodsEntity, responseDTO);

        responseDTO.setExchangeList(BeanUtil.copyToList(goodsEntity.getExchangeList(), ProductExplainResponseDTO.class));
        responseDTO.setOffsetList(BeanUtil.copyToList(goodsEntity.getOffsetList(), ProductExplainResponseDTO.class));
        if (CollectionUtil.isNotEmpty(goodsEntity.getLableList())) {
            List<String> labels = goodsEntity.getLableList().stream().map(ProductLableEntity::getCouponLabelName).collect(Collectors.toList());
            responseDTO.setLabelList(labels);
        }
        responseDTO.setSkuInfoResponseDTO(skuInfoResponseDTO);

        //广告
        List<AdvertisementEntity> adList = advertisementService.getAdvertisementList(AdvertisementTypeEnum.ADV_PACKAGE.getCode(), requestDTO.getPackageSpuId());
        if (CollectionUtil.isNotEmpty(adList)) {
            List<AdvertisementInfoDTO> advertisementInfoDTOList = new ArrayList<>();
            adList.forEach(item -> {
                if (item.getIsNationwide() == 1
                        || contentRegionService.isOnlineInRegion(item.getId(), ContentcenterTypeEnum.CONTENT_ADV.getCode(), requestDTO.getRegionId())) {
                    AdvertisementInfoDTO advertisementInfoDTO = new AdvertisementInfoDTO();
                    BeanUtils.copyProperties(item, advertisementInfoDTO);
                    advertisementInfoDTOList.add(advertisementInfoDTO);
                }
            });
            responseDTO.setAdvertiseList(advertisementInfoDTOList);
        }
        return BaseResponse.succeed(responseDTO);
    }

    @Override
    public List<PackageSkuInfoResponseDTO> skusInfo(PackageInfoRequestDTO requestDTO) {
        List<PackageSkuInfoResponseDTO> responseList = new ArrayList<>();
        List<PackageGoodsSkuEntity> skuEntityList = packageGoodsSkuRepository.getPackageGoodsSku(requestDTO.getPackageSpuId());
        if (CollectionUtil.isEmpty(skuEntityList)) {
            log.error("套餐：" + requestDTO.getPackageSpuId() + "的sku为空错误");
            return responseList;
        }
        List<ProductSpecsEntity> specsEntityList = productSpecsRepository.getSpecInfoBySpuId(requestDTO.getPackageSpuId(), ProductTypeEnum.PACKAGE.getType());
        if (CollectionUtil.isEmpty(specsEntityList)) {
            log.error("套餐：" + requestDTO.getPackageSpuId() + "的规格为空错误");
            return responseList;
        }
        //套餐只会是单规格，且规格属性与sku是一一对应关系
        ProductSpecsEntity specsEntity = specsEntityList.get(0);
        Map<Long, ProductSpecsValueEntity> specValueMap = specsEntity.getSpecsValueList().stream().collect(Collectors.toMap(ProductSpecsValueEntity::getId, entity -> entity));
        for (PackageGoodsSkuEntity skuEntity : skuEntityList) {
            if (productSkuSellRegionService.isSellableInRegion(requestDTO.getPackageSpuId(),
                    skuEntity.getId(),
                    ProductTypeEnum.PACKAGE,
                    requestDTO.getRegionId())) {
                PackageSkuInfoResponseDTO responseDTO = new PackageSkuInfoResponseDTO();
                BeanUtils.copyProperties(skuEntity, responseDTO);
                responseDTO.setInventoryStatus(1);
                ProductSpecsValueEntity valueEntity = specValueMap.get(skuEntity.getSpecValueId());
                if (Objects.isNull(valueEntity)) {
                    log.error("套餐：" + requestDTO.getPackageSpuId() + "--sku:" + skuEntity.getId() + "的规格值缺失错误");
                    continue;
                }
                responseDTO.setSpecValueName(valueEntity.getSpecsValue());
                responseDTO.setIsSelected(0);
                setSkuStatus(skuEntity.getId(), responseDTO);
                responseList.add(responseDTO);
            }
        }
        setSelectSku(requestDTO.getPackageSkuId(), responseList);
        return responseList;
    }

    @Override
    public PackageSkuExtralInfoResponseDTO skuExtraInfo(PackageSkuExtraInfoRequestDTO requestDTO) {
        PackageSkuExtralInfoResponseDTO responseDTO = new PackageSkuExtralInfoResponseDTO();
        responseDTO.setPackageSpuId(requestDTO.getPackageSpuId());
        responseDTO.setPackageSkuId(requestDTO.getPackageSkuId());
        PromotionSkuInfo promotionSkuInfo = promotionService.getPromotionInfoBySkuIdAndCouponType(requestDTO.getPackageSkuId(), ProductTypeEnum.PACKAGE.getType());
        if (Objects.nonNull(promotionSkuInfo)) {
            if (promotionSkuInfo.getActivityType().intValue() == PromotionActivityTypeEnum.DISCOUNT.getType().intValue()) {
                //打折活动需要计算卡券打折后的金额
                List<PackageGoodsSkuEntity> skuEntityList = packageGoodsSkuRepository.getPackageGoodsSku(requestDTO.getPackageSpuId());
                PackageGoodsSkuEntity skuEntity = null;
                for (PackageGoodsSkuEntity packageGoodsSkuEntity : skuEntityList) {
                    if (packageGoodsSkuEntity.getId().longValue() == requestDTO.getPackageSkuId().longValue()) {
                        skuEntity = packageGoodsSkuEntity;
                        break;
                    }
                }
                if (Objects.nonNull(skuEntity)) {
                    if (Objects.nonNull(promotionSkuInfo.getDiscount())) {
                        promotionSkuInfo.setActivityPrice(skuEntity.getPlatformPrice().multiply(new BigDecimal(promotionSkuInfo.getDiscount())).divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                    } else {
                        promotionSkuInfo.setActivityPrice(skuEntity.getPlatformPrice());
                    }
                    log.error("套餐：" + skuEntity.getId() + "，原价：" + skuEntity.getPlatformPrice() + ",打折后价格：" + promotionSkuInfo.getActivityPrice() + ",折扣为：" + promotionSkuInfo.getDiscount());
                }
            }
            responseDTO.setPromotionInfo(promotionSkuInfo);
        }
        //获取优惠券信息
        responseDTO.setBrandTicketList(getTicketsBySku(requestDTO.getPackageSkuId(), requestDTO.getRegionId()));
        return responseDTO;
    }

    //在列表中标记选中的sku
    private void setSelectSku(Long selectedSkuId, List<PackageSkuInfoResponseDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        PackageSkuInfoResponseDTO selectSku = list.get(0);
        if (Objects.nonNull(selectedSkuId)) {
            for (PackageSkuInfoResponseDTO responseDTO : list) {
                if (responseDTO.getId().equals(selectedSkuId)) {
                    selectSku = responseDTO;
                    break;
                }
            }
        }
        selectSku.setIsSelected(1);
//        selectSku.setPromotionInfo(promotionService.getPromotionInfoBySkuIdAndCouponType(selectSku.getId(), ProductTypeEnum.PACKAGE.getType()));
    }

    private void setSkuStatus(Long skuId, PackageSkuInfoResponseDTO responseDTO) {
        //根据子SKU状态得到sku的状态
        List<PackageSubSkuEntity> subSkuList = packageGoodsSkuRepository.getPackageSubSku(skuId);
        if (CollectionUtil.isEmpty(subSkuList)) {
            responseDTO.setPackageSkuStatus(CouponStatus.DOWN.getType());
        } else {
            for (PackageSubSkuEntity subSkuEntity : subSkuList) {
                if (subSkuEntity.getInventory() <= 0 && subSkuEntity.getCouponType() == 1) {
                    responseDTO.setInventoryStatus(0);
                    break;
                }
                if (subSkuEntity.getSubSkuStatus() == CouponStatus.DOWN.getType()) {
                    responseDTO.setPackageSkuStatus(CouponStatus.DOWN.getType());
                    break;
                }
            }
        }
    }

    private List<BrandTicketResponseDTO> getTicketsBySku(Long packageSkuId, Integer regionId) {
        List<BrandTicketResponseDTO> brandTicket = new ArrayList<>();
        List<TicketEntity> ticketList = ticketService.getTicketsBySkuIdAndPrdType(packageSkuId, ProductTypeEnum.PACKAGE.getType());
        if (CollectionUtil.isNotEmpty(ticketList)) {
            log.info("获取单套餐sku：{}的优惠券信息，size:{}", packageSkuId, ticketList.size());
            List<TicketEntity> tickets = new ArrayList<>();
            for (TicketEntity ticket : ticketList) {
                if ((Objects.nonNull(ticket.getIsNationwide()) && ticket.getIsNationwide() == 1)
                        || ticketService.isOnlineInRegion(ticket.getTicketId(), regionId)) {
                    tickets.add(ticket);
                }
            }
            if (CollectionUtil.isNotEmpty(tickets)) {
                Map<Long, List<TicketEntity>> ticketMap = tickets.stream().collect(Collectors.groupingBy(TicketEntity::getBrandId));
                List<Long> brandIds = new ArrayList<>(ticketMap.keySet());
                List<TicketBrandEntity> brandList = ticketService.getTicketBrandsByIds(brandIds);
                if (CollectionUtil.isEmpty(brandList)) {
                    return null;
                }
                Map<Long, TicketBrandEntity> brandMap = brandList.stream().collect(Collectors.toMap(TicketBrandEntity::getId, brand -> brand));
                for (Map.Entry<Long, List<TicketEntity>> entry : ticketMap.entrySet()) {
                    Long brandId = entry.getKey();
                    List<TicketEntity> valueList = entry.getValue();
                    BrandTicketResponseDTO responseDTO = new BrandTicketResponseDTO();
                    responseDTO.setBrandName(brandMap.get(brandId).getBrandName());
                    responseDTO.setTicketList(BeanUtil.copyToList(valueList, TicketResponseDTO.class));
                    brandTicket.add(responseDTO);
                }
            }
        }
        return brandTicket;
    }

}
