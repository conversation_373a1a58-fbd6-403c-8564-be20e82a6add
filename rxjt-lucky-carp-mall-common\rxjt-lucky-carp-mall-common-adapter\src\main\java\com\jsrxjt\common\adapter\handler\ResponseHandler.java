package com.jsrxjt.common.adapter.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jsrxjt.common.adapter.annotation.BaseResult;
import com.jsrxjt.common.core.vo.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;

/**
 *  ResponseHandler
 * 
 * <AUTHOR> Feng<PERSON>
 * 2023/3/1 11:46
 * 
 **/
@Slf4j
@ControllerAdvice
@AllArgsConstructor
@SuppressWarnings("NullableProblems")
public class ResponseHandler implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        Method method = returnType.getMethod();
        Class<?> clazz = Objects.requireNonNull(method, "method is null").getDeclaringClass();

        BaseResult annotation = Optional.ofNullable(clazz.getAnnotation(BaseResult.class))
                .orElse(method.getAnnotation(BaseResult.class));

        //如果是FileSystemResource 则不拦截
        if (method.getAnnotatedReturnType().getType().getTypeName()
                .equals(FileSystemResource.class.getTypeName())) {
            return false;
        }

        return annotation != null && !annotation.ignore();
    }

    @SneakyThrows
    @Override
    public Object beforeBodyWrite(Object body,
                                  MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {

        BaseResponse<Object> successInfo = BaseResponse.succeed(body);

        if ((body instanceof String) && !MediaType.APPLICATION_XML_VALUE.equals(selectedContentType.toString())) {
            ObjectMapper om = new ObjectMapper();
            response.getHeaders().set("Content-Type", "application/json");
            return om.writeValueAsString(successInfo);
        }

        if (Objects.isNull(body) && MediaType.TEXT_HTML_VALUE.equals(returnType.toString())) {
            ObjectMapper om = new ObjectMapper();
            response.getHeaders().set("Content-Type", "application/json");
            return om.writeValueAsString(successInfo);
        }

        return successInfo;
    }
}
