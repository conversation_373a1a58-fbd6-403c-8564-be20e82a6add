package com.jsrxjt.mobile.api.distribution.dto.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/7/29 17:17
 */
@Data
public class DistributionAfterSalesNotifyDTO {
    /**
     * 分销应用类型
     */
    private String distributionType;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 退款订单号
     */
    private String refundOrderNo;

    /**
     * 分销中心退款订单号
     */
    private String distributionCenterRefundOrderNo;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * 退款状态
     */
    private Integer status;

}
