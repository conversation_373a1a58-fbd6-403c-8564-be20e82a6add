package com.jsrxjt.mobile.biz.distribution.service.impl;

import com.jsrxjt.mobile.biz.distribution.service.DistributionAfterSalesService;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025/7/29 17:14
 */
@Service
@RequiredArgsConstructor
public class DistributionAfterSalesServiceImpl implements DistributionAfterSalesService {

    private final UnifiedDistributionApi unifiedDistributionApi;
}
