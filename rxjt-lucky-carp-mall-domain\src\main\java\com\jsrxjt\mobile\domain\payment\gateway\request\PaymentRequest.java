package com.jsrxjt.mobile.domain.payment.gateway.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 发起支付请求
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
public class PaymentRequest {
    
    /**
     * 业务订单号
     */
    @JSONField(name = "order_no")
    private String orderNo;
    
    /**
     * 预支付订单号
     */
    @JSONField(name = "pre_order_no")
    private String preOrderNo;

    /**
     * 支付密码
     */
    @JSONField(name = "pass")
    private String pass;
}