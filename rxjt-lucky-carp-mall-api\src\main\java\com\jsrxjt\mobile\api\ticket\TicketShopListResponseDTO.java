package com.jsrxjt.mobile.api.ticket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "门店优惠券")
public class TicketShopListResponseDTO {
    @Schema(description = "券id")
    private Long id;

    @Schema(description = "优惠券标题")
    private String ticketName;

    @Schema(description = "面值")
    private BigDecimal amount;

    @Schema(description = "券描述")
    private String summary;

    @Schema(description = "优惠券券码")
    private String ticketCode;

    @Schema(description = "优惠券开始时间")
    private Date startTime;

    @Schema(description = "优惠券结束时间")
    private Date endTime;

    @Schema(description = "券状态 1未使用 2已使用 3已过期")
    private Integer ticketStatus;

    @Schema(description = "门店券使用规则")
    private String rule;

    @Schema(description = "门店券logo")
    private String logo;

}
