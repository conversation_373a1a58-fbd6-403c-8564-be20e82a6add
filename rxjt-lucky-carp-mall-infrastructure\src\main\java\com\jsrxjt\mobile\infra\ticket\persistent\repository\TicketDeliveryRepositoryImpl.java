package com.jsrxjt.mobile.infra.ticket.persistent.repository;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketDeliveryMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketDeliveryPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description: 优惠券发放
 * @Author: zy
 */
@Repository
@RequiredArgsConstructor
public class TicketDeliveryRepositoryImpl implements TicketDeliveryRepository {
    private final TicketDeliveryMapper ticketDeliveryMapper;


    /**
     * @param userId
     * @return
     */
    @Override
    public PageDTO<TicketDeliveryEntity> getTicketDeliveryByUserId(Long userId, Long pageNum, Long pageSize) {
        Page<TicketDeliveryPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TicketDeliveryPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TicketDeliveryPO::getCustomerId,userId);
        queryWrapper.eq(TicketDeliveryPO::getDelFlag,0);
        queryWrapper.orderByAsc(TicketDeliveryPO::getStatus);
        queryWrapper.orderByDesc(TicketDeliveryPO::getCreateTime);
        Page<TicketDeliveryPO> ticketDeliveryPOPage = ticketDeliveryMapper.selectPage(page, queryWrapper);
        if(ticketDeliveryPOPage.getTotal()==0){
            return PageDTO.emptyBuild(pageNum,pageSize);
        }
        List<TicketDeliveryPO> records = ticketDeliveryPOPage.getRecords();
        List<TicketDeliveryEntity> ticketDeliveryEntities = records.stream().map(item -> {
            TicketDeliveryEntity ticketDeliveryEntity = new TicketDeliveryEntity();
            BeanUtil.copyProperties(item, ticketDeliveryEntity);
            return ticketDeliveryEntity;
        }).toList();
        return PageDTO.build(ticketDeliveryEntities,ticketDeliveryPOPage.getTotal(),pageSize,pageNum,ticketDeliveryPOPage.getPages());
    }

    /**
     * 获取优惠券详情
     *
     * @param id
     * @return
     */
    @Override
    public TicketDeliveryEntity getTicketDeliveryById(Long id) {
        TicketDeliveryPO po = ticketDeliveryMapper.selectById(id);
        if(po!=null){
            TicketDeliveryEntity ticketDeliveryEntity = new TicketDeliveryEntity();
            BeanUtil.copyProperties(po, ticketDeliveryEntity);
            return ticketDeliveryEntity;
        }
        return null;
    }

    /**
     * 删除优惠券发放
     *
     * @param id
     */
    @Override
    public void delTicketDelivery(Long id) {
        LambdaUpdateWrapper<TicketDeliveryPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(TicketDeliveryPO::getId,id);
        updateWrapper.set(TicketDeliveryPO::getDelFlag,1);
        ticketDeliveryMapper.update(null,updateWrapper);
    }
}
