package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后单列表响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Setter
public class AfterSaleListResponseDTO {
    
    @Schema(description = "售后单号")
    private String afterSaleNo;
    
    @Schema(description = "订单号")
    private String orderNo;
    
    @Schema(description = "商品名称")
    private String productName;
    
    @Schema(description = "品牌名称")
    private String brandName;
    
    @Schema(description = "商品图片")
    private String productLogo;

    @Schema(description = "面值")
    private BigDecimal faceAmount;

    @Schema(description = "商品价格")
    private BigDecimal sellPrice;

    @Schema(description = "购买数量")
    private Integer quantity;
    
    @Schema(description = "售后类型：1-仅退款 2-退货退款")
    private Integer afterSaleType;
    
    @Schema(description = "售后状态: 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成")
    private Integer afterSaleStatus;

    
    @Schema(description = "退款状态: 0-未退款 1-退款中 2-退款成功 3-退款失败 4-拒绝退款")
    private Integer refundStatus;

    
    @Schema(description = "退款金额")
    private BigDecimal refundAmount;
    
    @Schema(description = "申请数量")
    private Integer applyQuantity;


    @Schema(description = "申请时间")
    private LocalDateTime createTime;
}
