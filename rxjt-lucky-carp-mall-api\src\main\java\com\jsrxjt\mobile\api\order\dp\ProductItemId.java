package com.jsrxjt.mobile.api.order.dp;

import lombok.Value;

/**
 * ProductItem标识值对象
 * 
 * <AUTHOR>
 * @since 2025/5/27
 */
@Value
public class ProductItemId {
    Long spuId;
    Long skuId;
    Integer productType;

    public static ProductItemId of(Long spuId, Long skuId, Integer productType) {
        return new ProductItemId(spuId, skuId, productType);
    }

    @Override
    public String toString() {
        return String.format("%d_%d_%d", spuId, skuId, productType);
    }
}