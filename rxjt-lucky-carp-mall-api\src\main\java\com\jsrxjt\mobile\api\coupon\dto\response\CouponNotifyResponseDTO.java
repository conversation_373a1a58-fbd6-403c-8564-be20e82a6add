package com.jsrxjt.mobile.api.coupon.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 卡券通知响应DTO
 * <AUTHOR>
 * @since 2025/7/8
 **/
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class CouponNotifyResponseDTO {
    /**
     * 响应码 success成功
     */
    private String code;
    /**
     * 响应信息
     */
    private String msg;





    public static CouponNotifyResponseDTO success() {
        return new CouponNotifyResponseDTO("success","ok");
    }

    public static CouponNotifyResponseDTO fail(String msg) {
        return new CouponNotifyResponseDTO("fail", msg);
    }
}
