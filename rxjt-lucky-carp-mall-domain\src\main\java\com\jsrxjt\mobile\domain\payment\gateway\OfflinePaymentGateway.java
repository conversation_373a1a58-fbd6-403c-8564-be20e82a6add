package com.jsrxjt.mobile.domain.payment.gateway;

import com.jsrxjt.mobile.domain.payment.gateway.request.OfflineCardSortRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.OfflinePrePayRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.OfflineCardSortResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.OfflinePrePayResponse;

import java.util.List;

/**
 * 线下支付gateway接口

 */
public interface OfflinePaymentGateway {

    /**
     * 线下查询卡支付顺序
     * @param request
     * @return
     */
    OfflineCardSortResponse findOfflineCardSort(OfflineCardSortRequest request);


    /**
     * 线下预支付
     * @param request
     * @return
     */
    OfflinePrePayResponse prePayment(OfflinePrePayRequest request);


}
