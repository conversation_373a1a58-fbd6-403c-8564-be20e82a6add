package com.jsrxjt.mobile.infra.packages.persistent.mapper;

import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuJobEntity;
import com.jsrxjt.mobile.infra.packages.persistent.po.PackageGoodsSkuPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 套餐skumapper
 * @Author: ywt
 * @Date: 2025-05-10 14:06
 * @Version: 1.0
 */
@Mapper
public interface PackageGoodsSkuMapper extends CommonBaseMapper<PackageGoodsSkuPO> {

    void updatePackageGoodsSkuStatus(@Param("list") List<PackageGoodsSkuJobEntity> list);

    /**
     * 原子性增加套餐SKU销量
     *
     * @param packageSkuId 套餐SKU ID
     * @param quantity     增加的数量
     * @return 更新的行数
     */
    int increaseSoldNum(@Param("packageSkuId") Long packageSkuId, @Param("quantity") Integer quantity);
}
