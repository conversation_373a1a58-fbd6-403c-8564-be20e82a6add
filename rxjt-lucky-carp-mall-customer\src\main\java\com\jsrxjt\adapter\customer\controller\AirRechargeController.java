package com.jsrxjt.adapter.customer.controller;

import com.jsrxjt.common.adapter.annotation.VerifySign;
import com.jsrxjt.common.core.vo.BaseRequest;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.airRecharge.AirRechargeRequest;
import com.jsrxjt.mobile.api.customer.response.AirRechargeInfoResponse;
import com.jsrxjt.mobile.biz.airRecharge.service.AirRechargeService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description: 空中充值控制器
 * @Author: ywt
 * @Date: 2025-08-26 14:15
 * @Version: 1.0
 */
@RestController
@RequestMapping("/v1/airrecharge")
@Slf4j
@RequiredArgsConstructor
public class AirRechargeController {
    private final AirRechargeService airRechargeService;

    @PostMapping("/getAirRechargeInfo")
    @Operation(summary = "获取空充信息，注意：只有登录状态才能调用")
    @VerifySign(hasToken=true)
    public BaseResponse<AirRechargeInfoResponse> getAirRechargeInfo(@RequestBody @Valid BaseRequest request){
        return BaseResponse.succeed(airRechargeService.getAirRechargeInfo());
    }

    @PostMapping("/updateAirRechargeStatus")
    @Operation(summary = "更新空充状态为已查看（不在重复弹框）")
    @VerifySign(hasToken=true)
    public BaseResponse updateAirRechargeStatus(@RequestBody @Valid AirRechargeRequest request){
        airRechargeService.updateAirRechargeStatus(request);
        return BaseResponse.succeed();
    }
}
