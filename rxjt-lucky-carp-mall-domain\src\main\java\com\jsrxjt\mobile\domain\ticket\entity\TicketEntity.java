package com.jsrxjt.mobile.domain.ticket.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 优惠券
 * @Author: ywt
 * @Date: 2025-08-15 15:09
 * @Version: 1.0
 */
@Data
public class TicketEntity {
    private Long ticketId;
    private String ticketName;//优惠券名称
    private Integer ticketType;//优惠券类型0商家自发优惠券 1全球购线上商城优惠券 2瑞祥代发优惠券
    private Long brandId;//优惠券品牌id
    private String centerCouponId;//卡管/商城coupon_id
    private Integer ticketCatCode;//优惠券分类 0满减券 1折扣券 2满赠券 3权益包 4无门槛券
    private Integer ticketValidDate;//自发券后有效期日
    private String specPicUrl;//规格图片url
    private String useManual;//使用说明
    private Integer offsetPageType;//核销页样式 0卡号 1转码
    private Integer status;//状态 0禁用 1启用
    private Integer isNationwide;//上线城市：0非全国  1全国
}
