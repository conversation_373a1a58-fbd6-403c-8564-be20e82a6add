package com.jsrxjt.mobile.biz.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.dto.request.DefaultSearchKeywordRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSearchRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.ProductSuggestionRequestDTO;
import com.jsrxjt.mobile.api.product.dto.response.ProductSuggestionResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.SearchKeywordResponseDTO;
import com.jsrxjt.mobile.biz.product.service.ProductSearchCaseService;
import com.jsrxjt.mobile.domain.product.entity.ProductSpuBaseInfo;
import com.jsrxjt.mobile.domain.product.entity.SearchKeyword;
import com.jsrxjt.mobile.domain.product.repository.ProductBaseInfoRepository;
import com.jsrxjt.mobile.domain.product.repository.SearchKeywordRepository;
import com.jsrxjt.mobile.domain.product.service.ProductSpuBaseInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品搜索服务实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/8
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSearchCaseServiceImpl implements ProductSearchCaseService {

    private final ProductSpuBaseInfoService productSpuBaseInfoService;
    private final ProductBaseInfoRepository productBaseInfoRepository;
    private final SearchKeywordRepository searchKeywordRepository;

    @Override
    public PageDTO<ProductSpuBaseInfo> pageSearchProducts(ProductSearchRequestDTO request) {
        log.info("搜索产品开始，关键词：{}, 页码：{}, 每页大小：{}",
                request.getKeyword(), request.getToPage(), request.getPageRows());

        // 调用领域层搜索产品并直接返回结果
        PageDTO<ProductSpuBaseInfo> result = productBaseInfoRepository.pageProducts(
                request.getKeyword(), request.getToPage(), request.getPageRows());

        log.info("搜索产品完成，共查询到{}条记录", result.getTotal());
        return result;
    }

    @Override
    public List<ProductSpuBaseInfo> searchProducts(ProductSearchRequestDTO request) {
        List<ProductSpuBaseInfo> productSpuBaseInfos = productBaseInfoRepository.listProducts(request.getKeyword());
        if (productSpuBaseInfos == null || productSpuBaseInfos.isEmpty()) {
            log.info("没有搜索到产品 关键词{}", request.getKeyword());
            return List.of();
        }

        List<ProductSpuBaseInfo> result = productSpuBaseInfoService.filterByRegion(request.getRegionId(), productSpuBaseInfos);

        productSpuBaseInfoService.setPromotionLabels(result);

        return result;
    }

    @Override
    public List<ProductSuggestionResponseDTO> getSuggestions(ProductSuggestionRequestDTO request) {
        String keyword = request.getKeyword();
        Integer maxCount = request.getMaxCount();

        log.info("获取产品联想词开始，关键词：{}, 最大返回数：{}", keyword, maxCount);

        // 调用领域层获取联想词并直接返回结果
        List<ProductSuggestionResponseDTO> result = productBaseInfoRepository.getSuggestions(
                keyword, maxCount);

        log.info("获取产品联想词完成，共获取到{}条记录", result.size());
        return result;
    }

    @Override
    public List<SearchKeywordResponseDTO> getDefaultSearchKeywords(DefaultSearchKeywordRequestDTO request) {
        log.info("获取默认搜索词，区域ID：{}", request.getDistrictId());
        
        // 调用领域层获取默认搜索词
        List<SearchKeyword> keywords = searchKeywordRepository.findDefaultSearchKeywords(request.getDistrictId(), request.getType());
        
        // 转换为DTO
        List<SearchKeywordResponseDTO> result = keywords.stream()
                .map(this::convertToDefaultKeywordDTO)
                .collect(Collectors.toList());
        
        log.info("获取默认搜索词完成，共获取到{}条记录", result.size());
        return result;
    }

    /**
     * 将领域实体转换为默认搜索词DTO
     */
    private SearchKeywordResponseDTO convertToDefaultKeywordDTO(SearchKeyword keyword) {
        SearchKeywordResponseDTO dto = new SearchKeywordResponseDTO();
        BeanUtil.copyProperties(keyword, dto);
        return dto;
    }
}
