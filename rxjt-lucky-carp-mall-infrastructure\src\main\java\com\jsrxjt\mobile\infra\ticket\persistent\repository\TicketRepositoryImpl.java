package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 优惠券
 * @Author: ywt
 * @Date: 2025-08-15 15:19
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
public class TicketRepositoryImpl implements TicketRepository {
    private final TicketMapper ticketMapper;

    @Override
    public List<TicketEntity> getTicketsByIds(List<Long> ids) {
        List<TicketPO> ticketList = ticketMapper.selectList(new LambdaQueryWrapper<TicketPO>()
                .in(TicketPO::getTicketId, ids)
                .eq(TicketPO::getStatus, 1));
        return BeanUtil.copyToList(ticketList, TicketEntity.class);
    }
}
