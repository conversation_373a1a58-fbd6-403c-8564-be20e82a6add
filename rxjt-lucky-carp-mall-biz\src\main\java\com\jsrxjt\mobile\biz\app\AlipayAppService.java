package com.jsrxjt.mobile.biz.app;

import com.jsrxjt.mobile.api.app.request.AlipayAppOrderRequest;
import com.jsrxjt.mobile.api.app.request.AlipayAppPreOrderRequest;
import com.jsrxjt.mobile.api.app.request.AlipayAppRequest;
import com.jsrxjt.mobile.api.app.request.AlipayVoucherRechargeRecordRequest;
import com.jsrxjt.mobile.api.app.response.AlipayAppCatResponse;
import com.jsrxjt.mobile.api.app.response.AlipayAppResponse;
import com.jsrxjt.mobile.api.app.response.AlipayVoucherRechargeRecordResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;

import java.util.List;

public interface AlipayAppService {
    /**
     * 获取支付宝红包分类
     * @return
     */
    List<AlipayAppCatResponse> getAlipayAppCats();

    /**
     * 获取支付宝红包详情信息
     * @param request
     * @return
     */
    AlipayAppResponse getAlipayAppInfo(AlipayAppRequest request);

    /**
     * 支付宝红包预充值是否满足条件
     * @param request
     * @return
     */
    Boolean checkImmediatePayment(AlipayAppPreOrderRequest request);

    /**
     * 支付宝红包充值确认
     * @param request
     * @return
     */
    OrderInfoEntity payAppOrder(AlipayAppOrderRequest request);

    /**
     * 获取支付宝红包充值记录
     * @param request 充值记录请求参数
     * @return 充值记录
     */
//    PageDTO<AlipayVoucherRechargeRecordResponse> getRechargeRecords(AlipayVoucherRechargeRecordRequest request);

    /**
     * 获取上次充值账号
     * @param customerId 客户id
     * @return 上次充值账号
     */
    String  getLastRechargeAccount(Long customerId);
}
