package com.jsrxjt.mobile.domain.payment.gateway;

import com.jsrxjt.mobile.domain.payment.gateway.request.CheckFindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.FindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.PaymentRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.PrePayRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.CheckFindCardBalanceResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.FindCardBalanceResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.PaymentResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.PrePayResponse;

import java.util.List;

/**
 * 线上支付gateway接口
 * @author: Cheng
 * @since 2025/8/8
 */
public interface OnlinePaymentGateway {

    /**
     * 预支付接口
     * 根据订单号和来源获取预支付信息
     * 
     * @param request 预支付请求参数
     * @return 预支付响应结果
     */
    PrePayResponse prePayment(PrePayRequest request);
    
    /**
     * 发起支付接口
     * 根据订单号和预支付订单号发起支付
     * 
     * @param request 发起支付请求参数
     * @return 发起支付响应结果
     */
    PaymentResponse pay(PaymentRequest request);

    /**
     * 多卡查询卡余额接口
     *
     * @param request 查询卡余额请求参数
     * @return 查询卡余额响应结果
     */
    List<FindCardBalanceResponse> findBalance(FindCardBalanceRequest request);

    /**
     * 卡号+卡密查询卡余额接口
     *
     * @param request 查询卡余额请求参数
     * @return 查询卡余额响应结果
     */
    CheckFindCardBalanceResponse findBalanceByCardNoAndPassword(CheckFindCardBalanceRequest request);
}
