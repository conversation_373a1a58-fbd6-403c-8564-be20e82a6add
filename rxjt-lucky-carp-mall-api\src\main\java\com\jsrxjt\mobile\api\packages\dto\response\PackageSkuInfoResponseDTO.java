package com.jsrxjt.mobile.api.packages.dto.response;

import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.api.ticket.BrandTicketResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 套餐sku信息响应
 * @Author: ywt
 * @Date: 2025-05-09 18:09
 * @Version: 1.0
 */
@Data
@Schema(description = "套餐sku信息响应")
public class PackageSkuInfoResponseDTO {
    @Schema(description = "套餐skuId")
    private Long id;
    @Schema(description = "套餐spuid")
    private Long packageSpuId;
    @Schema(description = "手续费百分比")
    private BigDecimal commissionFee;//手续费百分比
    @Schema(description = "福鲤圈的售价")
    private BigDecimal platformPrice;
    @Schema(description = "每月每人单卡限购数量")
    private Integer limitNumPerMonth;//每月每人单卡限购数量
    @Schema(description = "已售数量")
    private Integer soldNum;//已售数量
    @Schema(description = "套餐库存: 0--缺货 1--有库存")
    private Integer inventoryStatus;
    @Schema(description = "套餐状态 0:下架 1:出售中")
    private Integer packageSkuStatus;
    @Schema(description = "规格值，规格属性与sku是一对一关系")
    private Long specValueId;
    @Schema(description = "规格名,用于前端展示套餐sku的名称")
    private String specValueName;
    @Schema(description = "备注富文本")
    private String remark;
    @Schema(description = "是否选中， 0--未选中 1--选中")
    private Integer isSelected;
    @Schema(description = "sku的促销活动信息，仅isSelected为1时有效")
    private PromotionSkuInfo promotionInfo;
    @Schema(description = "优惠券信息")
    private List<BrandTicketResponseDTO> brandTicketList;
}
