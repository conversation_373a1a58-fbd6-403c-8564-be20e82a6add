package com.jsrxjt.adapter.order.controller;

import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionOrderPayInfoQueryDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionOrderPayInfoResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionOrderPayInfoWithAmountResponseDTO;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.ZoneId;
import java.util.Objects;

@RestController
@RequestMapping("/v1/distribution-order-query")
@RequiredArgsConstructor
public class DistributionOrderQueryController {

    private final OrderRepository orderRepository;

    /**
     * 订单支付状态查询包含支付金额(叮咚买菜,大润发,食行,卫岗,物美,永辉,中免日上,清美)
     *
     * @param request
     * @return
     */
    @PostMapping("/order/payInfoWithPayAmount")
    public ApiResponse<Object> distributionOrderPayInfoWithPayAmount(@RequestBody @Valid DistributionOrderPayInfoQueryDTO request) {
        DistributionOrderPayInfoWithAmountResponseDTO dto = new DistributionOrderPayInfoWithAmountResponseDTO();
        String orderNo = request.getOrderNo();
        String tradeNo = request.getTradeNo();
        OrderInfoEntity order = orderRepository.findOrderByExternalOrderNoAndTradeNo(orderNo, tradeNo);
        if (order != null) {
            dto.setOrderNo(orderNo);
            dto.setTradeNo(tradeNo);
            dto.setThirdOrderNo(order.getExternalOrderNo());
            Integer payStatus = order.getPaymentStatus();
            PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum.getByCode(payStatus);
            switch (Objects.requireNonNull(paymentStatusEnum)) {
                case UNPAID -> {
                    dto.setTradeStatus("01");
                    dto.setTradeTime(0);
                    dto.setPayAmount("0.00");
                }
                case PAID -> {
                    dto.setTradeStatus("00");
                    dto.setTradeTime((int) order.getPaymentTime()
                            .atZone(ZoneId.systemDefault())
                            .toInstant()
                            .getEpochSecond());
                    dto.setPayAmount(String.valueOf(order.getPaymentAmount()));
                }
//                case FAIL_PAY -> {
//                    dto.setTradeStatus("02");
//                    dto.setTradeTime((int) tradeOrder.getTradeTime()
//                            .atZone(ZoneId.systemDefault())
//                            .toInstant()
//                            .getEpochSecond());
//                    dto.setPayAmount(String.valueOf(tradeOrder.getPayAmount()));
//                }
            }
        }

        return ApiResponse.success(dto);
    }

    /**
     * 订单支付状态查询(美团,视听,水韵,同程,团油,西橙电影)
     *
     * @param request
     * @return
     */
    @PostMapping("/order/payInfo")
    public ApiResponse<DistributionOrderPayInfoResponseDTO> distributionOrderPayInfo(@RequestBody @Valid DistributionOrderPayInfoQueryDTO request) {
        DistributionOrderPayInfoResponseDTO dto = new DistributionOrderPayInfoResponseDTO();
        String orderNo = request.getOrderNo();
        String tradeNo = request.getTradeNo();
        OrderInfoEntity order = orderRepository.findOrderByExternalOrderNoAndTradeNo(orderNo, tradeNo);
        if (order != null) {
            dto.setOrderNo(orderNo);
            dto.setTradeNo(tradeNo);
            dto.setThirdOrderNo(order.getOrderNo());
            Integer payStatus = order.getPaymentStatus();
            PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum.getByCode(payStatus);
            switch (Objects.requireNonNull(paymentStatusEnum)) {
                case UNPAID -> {
                    dto.setTradeStatus(0);
                    dto.setTradeTime(0);
                }
                case PAID -> {
                    dto.setTradeStatus(1);
                    dto.setTradeTime((int) order.getPaymentTime()
                            .atZone(ZoneId.systemDefault())
                            .toInstant()
                            .getEpochSecond());
                }
//                case FAIL_PAY -> {
//                    dto.setTradeStatus("2");
//                    dto.setTradeTime((int) tradeOrder.getTradeTime()
//                            .atZone(ZoneId.systemDefault())
//                            .toInstant()
//                            .getEpochSecond());
//                }
            }
        }
        return ApiResponse.success(dto);
    }
}
