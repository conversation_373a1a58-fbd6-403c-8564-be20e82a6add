package com.jsrxjt.adapter.distribution.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.jsrxjt.mobile.biz.homeScanPay.service.ScanPayWssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 放量计划消息监听器
 * 
 * <AUTHOR>
 * @since 2025/3/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScanPayWssMessageListener implements MessageListener {

  private final ScanPayWssService scanPayWssService;

  @Override
  public ConsumeResult consume(MessageView messageView) {
    try {
      // 解析消息内容
      String msgBody = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
      log.info("收到扫码付发布消息 id是{}: 消息内容是{}",messageView.getMessageId(), msgBody);
      Map<String, Object> releaseMessage = JSON.parseObject(msgBody, Map.class);
      Long customerId = (Long) releaseMessage.get("customerId");
      String payCode = (String) releaseMessage.get("payCode");
      String message = (String) releaseMessage.get("message");
      scanPayWssService.handleScanPayWssMessage(customerId, payCode, message);
      return ConsumeResult.SUCCESS;
    } catch (Exception e) {
      log.error("处理装修页发布消息异常", e);
      return ConsumeResult.FAILURE;
    }
  }
}