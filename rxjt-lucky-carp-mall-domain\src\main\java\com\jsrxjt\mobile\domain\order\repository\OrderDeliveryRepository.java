package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.order.entity.CouponPackageEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderDeliveryEntity;
import com.jsrxjt.mobile.domain.order.entity.SelOrderDeliveryDetailEntity;
import com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery;

import java.util.List;

/**
 * 订单发货仓储接口
 * 
 * <AUTHOR> Fengping
 * @since 2025/7/9
 */
public interface OrderDeliveryRepository {

    /**
     * 保存订单发货信息
     * 
     * @param orderDelivery 订单发货信息
     */
    void saveOrderDelivery(OrderDeliveryEntity orderDelivery);

    /**
     * 批量保存订单发货信息
     * 
     * @param orderDeliveries 订单发货信息列表
     */
    void batchSaveOrderDelivery(List<OrderDeliveryEntity> orderDeliveries);


    /**
     * 根据订单编号查询发货信息
     * 
     * @param orderNo 订单编号
     * @return 发货信息列表
     */
    List<OrderDeliveryEntity> findByOrderNo(String orderNo);

    /**
     * 更新发货信息
     * 
     * @param orderDelivery 发货信息
     */
    void updateOrderDelivery(OrderDeliveryEntity orderDelivery);

    /**
     * 获取卡包列表
     * @param query
     * @return {@link PageDTO}<{@link CouponPackageEntity}>
     */
    PageDTO<CouponPackageEntity> getCustomerCouponPackageList(CouponPackageListQuery query);


    /**
     * 根据id查询订单发货信息
     * @param id
     * @return
     */
    OrderDeliveryEntity findById(Long id);

    /**
     * 批量更新订单发货信息delFlag
     * @param idList
     * @param delFlag
     */
    void updateOrderDelivery (List<Long> idList,Integer originDelFlag,Integer delFlag);

    /**
     * 获取用户该sku下所有自发券
     * @param skuId
     * @return
     */
    List<SelOrderDeliveryDetailEntity> getSelOrderDeliveryDetailList(Long customerId,Long skuId);

    PageDTO<CouponPackageEntity> getCycCouponPackageList(CouponPackageListQuery query);

    /**
     * 获取用户卡包数量
     * @param customerId 用户id
     * @return 卡包数量
     */
    int getCustomerCouponPackageNum(Long customerId);
}