package com.jsrxjt.mobile.biz.distribution.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantRxShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.MerchantShopRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopDetailResponseDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.MerchantShopResponseDTO;
import com.jsrxjt.mobile.biz.distribution.service.MerchantCaseService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.merchant.entity.MerchantShopDataEntity;
import com.jsrxjt.mobile.domain.merchant.gateway.MerchantGateway;
import com.jsrxjt.mobile.domain.merchant.request.MerchantShopRequest;
import com.jsrxjt.mobile.domain.merchant.service.MerchantService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 商户服务
 * @Author: ywt
 * @Date: 2025-05-27 14:27
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MerchantCaseServiceImpl implements MerchantCaseService {
    private final MerchantGateway merchantGateway;
    private final AppGoodsRepository appGoodsRepository;
    private final RegionRepository regionRepository;
    private final MerchantService merchantService;

    @Override
    public PageDTO<MerchantShopResponseDTO> getNearShopList(MerchantShopRequestDTO requestDTO) {
        if (requestDTO.getPage() < 1) {
            requestDTO.setPage(1);
        }
        if (requestDTO.getPageSize() < 1) {
            requestDTO.setPageSize(10);
        }
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(requestDTO.getAppId());
        if (Objects.isNull(appGoodsEntity)) {
            log.error("获取附近商户门店，没有查询到扫码付应用：" + requestDTO.getAppId());
            throw BizException.NOT_FIND_SCAN_APP;
        }
        if (Objects.isNull(appGoodsEntity.getThirdId())) {
            log.error("获取附近商户门店，没有查询到扫码付应用：" + requestDTO.getAppId());
            throw BizException.SCAN_APP_THIRD_ID_IS_NULL;
        }
        RegionEntity regionEntity = regionRepository.getRegionById(requestDTO.getRegionId());
        if (Objects.isNull(regionEntity) || StringUtils.isEmpty(regionEntity.getAdcode())) {
            log.error("获取附近商户门店，没有查询到对应的城市码：" + requestDTO.getRegionId());
            throw BizException.REGION_ERROR;
        }
        RegionEntity region = regionRepository.getCurrentRegion(StpUtil.getLoginIdAsLong());
        MerchantShopRequest request = new MerchantShopRequest();
        BeanUtils.copyProperties(requestDTO, request);
        request.setThirdId(appGoodsEntity.getThirdId());
        request.setCityCode(regionEntity.getAdcode());
        request.setLongitude(region.getLng());
        request.setLatitude(region.getLat());
        MerchantShopDataEntity shopDataEntity = merchantGateway.getNearShopList(request);
        if (Objects.isNull(shopDataEntity) || shopDataEntity.getCount() <= 0 || CollectionUtils.isEmpty(shopDataEntity.getList())) {
            return PageDTO.<MerchantShopResponseDTO>builder()
                    .records(Collections.emptyList())
                    .total(0L)
                    .current(requestDTO.getPage().longValue())
                    .size(requestDTO.getPageSize().longValue())
                    .build();
        }
        List<MerchantShopResponseDTO> list = BeanUtil.copyToList(shopDataEntity.getList(), MerchantShopResponseDTO.class);
        PageDTO<MerchantShopResponseDTO> result = PageDTO.<MerchantShopResponseDTO>builder()
                .records(list)
                .total(shopDataEntity.getCount().longValue())
                .current(requestDTO.getPage().longValue())
                .size(requestDTO.getPageSize().longValue())
                .build();
        return result;
    }

    @Override
    public PageDTO<MerchantShopResponseDTO> getRxShopList(MerchantRxShopRequestDTO requestDTO) {
        if (requestDTO.getPage() < 1) {
            requestDTO.setPage(1);
        }
        if (requestDTO.getPageSize() < 1) {
            requestDTO.setPageSize(10);
        }
        RegionEntity region = regionRepository.getCurrentRegion(StpUtil.getLoginIdAsLong());
        MerchantShopRequest request = new MerchantShopRequest();
        BeanUtils.copyProperties(requestDTO, request);
        request.setCityCode(region.getAdcode());
        request.setLongitude(region.getLng());
        request.setLatitude(region.getLat());
        List<MerchantShopDataEntity.MerchantShopEntity> shopEntityList = merchantService.getRxShopList(request);
        if (CollectionUtil.isEmpty(shopEntityList)) {
            return PageDTO.<MerchantShopResponseDTO>builder()
                    .records(Collections.emptyList())
                    .total(0L)
                    .current(requestDTO.getPage().longValue())
                    .size(requestDTO.getPageSize().longValue())
                    .build();
        }
        // 计算总记录数
        int totalCount = shopEntityList.size();
        // 计算总页数
        int totalPage = (totalCount + requestDTO.getPageSize() - 1) / requestDTO.getPageSize();
        // 页码越界处理
        if (requestDTO.getPage() < 1) {
            requestDTO.setPage(1);
        }
        if (requestDTO.getPage() > totalPage) {
            return PageDTO.<MerchantShopResponseDTO>builder()
                    .records(Collections.emptyList())
                    .total((long) totalCount)
                    .current(requestDTO.getPage().longValue())
                    .size(requestDTO.getPageSize().longValue())
                    .build();
        }
        // 计算起始索引和结束索引
        int fromIndex = (requestDTO.getPage() - 1) * requestDTO.getPageSize();
        int toIndex = Math.min(fromIndex + requestDTO.getPageSize(), totalCount);

        List<MerchantShopDataEntity.MerchantShopEntity> shopList = shopEntityList.subList(fromIndex, toIndex);
        shopList.forEach(item -> {
            double kilometers = item.getDistance() / 1000;
            BigDecimal bd = new BigDecimal(kilometers)
                    .setScale(1, RoundingMode.HALF_UP);
            item.setDistance(bd.doubleValue());
        });
        /*MerchantShopDataEntity shopDataEntity = merchantGateway.getNearRxShopList(request);
        if (Objects.isNull(shopDataEntity) || shopDataEntity.getCount() <= 0 || CollectionUtils.isEmpty(shopDataEntity.getList())) {
            return PageDTO.<MerchantShopResponseDTO>builder()
                    .records(Collections.emptyList())
                    .total(0L)
                    .current(requestDTO.getPage().longValue())
                    .size(requestDTO.getPageSize().longValue())
                    .build();
        }*/
        List<MerchantShopResponseDTO> list = BeanUtil.copyToList(shopList, MerchantShopResponseDTO.class);
        PageDTO<MerchantShopResponseDTO> result = PageDTO.<MerchantShopResponseDTO>builder()
                .records(list)
                .total((long) totalCount)
                .current(requestDTO.getPage().longValue())
                .size(requestDTO.getPageSize().longValue())
                .build();
        return result;
    }

    /**
     * @param shopId
     * @return
     */
    @Override
    public MerchantShopDetailResponseDTO getShopDetail(String shopId) {
        MerchantShopDetailResponseDTO dataResponse = merchantGateway.getShopDetail(shopId);
        return dataResponse;
    }

}
