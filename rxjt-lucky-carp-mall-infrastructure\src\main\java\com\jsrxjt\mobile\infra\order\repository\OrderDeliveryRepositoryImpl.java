package com.jsrxjt.mobile.infra.order.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.CouponPackageEntity;
import com.jsrxjt.mobile.domain.order.entity.CouponPackageListEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderDeliveryEntity;
import com.jsrxjt.mobile.domain.order.entity.SelOrderDeliveryDetailEntity;
import com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.infra.order.persistent.mapper.OrderDeliveryMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageBrandPO;
import com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageDetailPO;
import com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageListPO;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderDeliveryPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单发货仓储实现类
 * 
 * <AUTHOR> Fengping
 * @since 2025/7/9
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderDeliveryRepositoryImpl implements OrderDeliveryRepository {

    private final BusinessIdGenerator businessIdGenerator;

    private final OrderDeliveryMapper orderDeliveryMapper;

    @Override
    @Transactional
    public void saveOrderDelivery(OrderDeliveryEntity orderDelivery) {
        OrderDeliveryPO orderDeliveryPO = new OrderDeliveryPO();
        BeanUtils.copyProperties(orderDelivery, orderDeliveryPO);
        orderDeliveryPO.setId(businessIdGenerator.generateId());

        int insert = orderDeliveryMapper.insert(orderDeliveryPO);
        if (insert <= 0) {
            throw new BizException("订单发货信息保存失败");
        }

        log.info("订单发货信息保存成功，发货ID：{}，订单号：{}", orderDeliveryPO.getId(), orderDelivery.getOrderNo());
    }

    @Override
    @Transactional
    public void batchSaveOrderDelivery(List<OrderDeliveryEntity> orderDeliveries) {
        if (orderDeliveries == null || orderDeliveries.isEmpty()) {
            return;
        }

        List<OrderDeliveryPO> orderDeliveryPOs = orderDeliveries.stream().map(entity -> {
            OrderDeliveryPO po = new OrderDeliveryPO();
            BeanUtils.copyProperties(entity, po);
            po.setId(businessIdGenerator.generateId());
            po.setDelFlag(0);
            return po;
        }).toList();

        int insert = orderDeliveryMapper.insertAllBatch(orderDeliveryPOs);
        if (insert <= 0) {
            throw new BizException("批量保存订单发货信息失败");
        }

        log.info("批量保存订单发货信息成功，共{}条记录", orderDeliveries.size());
    }


    @Override
    public List<OrderDeliveryEntity> findByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderDeliveryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDeliveryPO::getOrderNo, orderNo);

        List<OrderDeliveryPO> orderDeliveryPOs = orderDeliveryMapper.selectList(queryWrapper);

        return orderDeliveryPOs.stream().map(this::convertToEntity).toList();
    }

    @Override
    @Transactional
    public void updateOrderDelivery(OrderDeliveryEntity orderDelivery) {
        OrderDeliveryPO orderDeliveryPO = new OrderDeliveryPO();
        BeanUtils.copyProperties(orderDelivery, orderDeliveryPO);

        LambdaQueryWrapper<OrderDeliveryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDeliveryPO::getId, orderDeliveryPO.getId());

        int update = orderDeliveryMapper.update(orderDeliveryPO, queryWrapper);
        if (update <= 0) {
            throw new BizException("订单发货信息更新失败");
        }

        log.info("订单发货信息更新成功，发货ID：{}，订单号：{}", orderDelivery.getId(), orderDelivery.getOrderNo());
    }

    @Override
    public PageDTO<CouponPackageEntity> getCustomerCouponPackageList(CouponPackageListQuery query) {
        Page<CouponPackageBrandPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        //分页获取品牌列表
        IPage<CouponPackageBrandPO> couponPackageResult = orderDeliveryMapper.selectCustomerCouponPackageBrandList(page, query);
        List<CouponPackageBrandPO> couponPackageResultList = couponPackageResult.getRecords();
        if (CollectionUtil.isEmpty(couponPackageResultList)){
            return PageDTO.<CouponPackageEntity>builder()
                    .total(couponPackageResult.getTotal())
                    .size(couponPackageResult.getSize())
                    .current(couponPackageResult.getCurrent())
                    .build();
        }
        //获取品牌下的卡券列表
        List<Long> brandIdList = couponPackageResultList.stream().map(CouponPackageBrandPO::getBrandId).collect(Collectors.toList());
        query.setBrandIdList(brandIdList);
        List<CouponPackageListPO> couponPackageList = orderDeliveryMapper.selectCustomerCouponPackageList(query);
        Map<Long, List<CouponPackageListPO>> couponPackageListMap = couponPackageList.stream().collect(Collectors.groupingBy(CouponPackageListPO::getBrandId));
        List<CouponPackageEntity> couponPackageEntities = new ArrayList<>(couponPackageResultList.size());
        for (CouponPackageBrandPO couponPackageBrandPO : couponPackageResultList) {
            CouponPackageEntity couponPackageEntity = new CouponPackageEntity();
            couponPackageEntity.setBrandName(couponPackageBrandPO.getBrandName());
            couponPackageEntity.setCouponNum(couponPackageBrandPO.getCouponNum());
            couponPackageEntity.setBrandLogo(couponPackageBrandPO.getBrandLogo());
            List<CouponPackageListEntity> couponList = couponPackageListMap.get(couponPackageBrandPO.getBrandId())
                            .stream()
                            .map(couponPackageListPO -> {
                                CouponPackageListEntity couponPackageListEntity = new CouponPackageListEntity();
                                BeanUtil.copyProperties(couponPackageListPO, couponPackageListEntity);
                                couponPackageListEntity.setCouponName(couponPackageBrandPO.getBrandName() + couponPackageListPO.getAmountName());
                                return couponPackageListEntity;
                            })
                            .sorted(Comparator.comparing(CouponPackageListEntity::getCreateTime).reversed())
                            .toList();
            couponPackageEntity.setCouponList(couponList);
            couponPackageEntities.add(couponPackageEntity);
        }

        return PageDTO.<CouponPackageEntity>builder()
                .records(couponPackageEntities)
                .total(couponPackageResult.getTotal())
                .size(couponPackageResult.getSize())
                .current(couponPackageResult.getCurrent())
                .build();
    }

    /**
     * 根据id查询订单发货信息
     *
     * @param id
     * @return
     */
    @Override
    public OrderDeliveryEntity findById(Long id) {
        OrderDeliveryPO orderDeliveryPO = orderDeliveryMapper.selectById(id);
        if (orderDeliveryPO == null) {
            log.error("订单发货信息不存在，ID：{}", id);
            throw new BizException("订单发货信息获取失败");
        }

        return convertToEntity(orderDeliveryPO);
    }

    /**
     * @param idList
     * @param delFlag
     */
    @Override
    public void updateOrderDelivery(List<Long> idList,Integer originDelFlag, Integer delFlag) {
        if(CollectionUtil.isEmpty(idList) || delFlag == null || originDelFlag == null){
            return;
        }
        orderDeliveryMapper.updateDelFlag(idList, originDelFlag,delFlag);
    }

    /**
     * 获取用户该品牌所有自发券
     *
     * @param customerId
     * @param skuId
     * @return
     */
    @Override
    public List<SelOrderDeliveryDetailEntity> getSelOrderDeliveryDetailList(Long customerId, Long skuId) {
        List<CouponPackageDetailPO> couponPackageDetailPOS = orderDeliveryMapper.selectCouponPackageListBySkuId(skuId, customerId);
        if(CollectionUtil.isEmpty(couponPackageDetailPOS)){
            return new ArrayList<>();
        }
        List<SelOrderDeliveryDetailEntity> selOrderDeliveryDetailEntities = BeanUtil.copyToList(couponPackageDetailPOS, SelOrderDeliveryDetailEntity.class);
        for (SelOrderDeliveryDetailEntity selOrderDeliveryDetailEntity : selOrderDeliveryDetailEntities) {
            String couponUrl = selOrderDeliveryDetailEntity.getCouponUrl();
            //截取连接 等号后面的字段
            String couponOffsetCode = couponUrl.substring(couponUrl.indexOf("=") + 1);
            selOrderDeliveryDetailEntity.setCheckCode(couponOffsetCode);
        }
        return selOrderDeliveryDetailEntities;
    }

    /**
     * @param query
     * @return
     */
    @Override
    public PageDTO<CouponPackageEntity> getCycCouponPackageList(CouponPackageListQuery query) {
        Page<CouponPackageBrandPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        //分页获取品牌列表
        IPage<CouponPackageBrandPO> couponPackageResult = orderDeliveryMapper.selectCycCouponPackageBrandList(page, query);
        List<CouponPackageBrandPO> couponPackageResultList = couponPackageResult.getRecords();
        if (CollectionUtil.isEmpty(couponPackageResultList)) {
            return PageDTO.<CouponPackageEntity>builder()
                    .total(couponPackageResult.getTotal())
                    .size(couponPackageResult.getSize())
                    .current(couponPackageResult.getCurrent())
                    .build();
        }
        //获取品牌下的卡券列表
        List<Long> brandIdList = couponPackageResultList.stream().map(CouponPackageBrandPO::getBrandId).collect(Collectors.toList());
        query.setBrandIdList(brandIdList);
        List<CouponPackageListPO> couponPackageBrandPOS = orderDeliveryMapper.selectCycCouponPackageList(query);
        Map<Long, List<CouponPackageListPO>> couponPackageListMap = couponPackageBrandPOS.stream().collect(Collectors.groupingBy(CouponPackageListPO::getBrandId));
        List<CouponPackageEntity> couponPackageEntities = new ArrayList<>(couponPackageResultList.size());
        for (CouponPackageBrandPO couponPackageBrandPO : couponPackageResultList) {
            CouponPackageEntity couponPackageEntity = new CouponPackageEntity();
            couponPackageEntity.setBrandName(couponPackageBrandPO.getBrandName());
            couponPackageEntity.setCouponNum(couponPackageBrandPO.getCouponNum());
            couponPackageEntity.setBrandLogo(couponPackageBrandPO.getBrandLogo());
            List<CouponPackageListEntity> couponList = couponPackageListMap.get(couponPackageBrandPO.getBrandId())
                    .stream()
                    .map(couponPackageListPO -> {
                        CouponPackageListEntity couponPackageListEntity = new CouponPackageListEntity();
                        BeanUtil.copyProperties(couponPackageListPO, couponPackageListEntity);
                        couponPackageListEntity.setCouponName(couponPackageListPO.getAmountName());
                        return couponPackageListEntity;
                    })
                    .sorted(Comparator.comparing(CouponPackageListEntity::getCreateTime).reversed())
                    .toList();
            couponPackageEntity.setCouponList(couponList);
            couponPackageEntities.add(couponPackageEntity);
        }
        return PageDTO.<CouponPackageEntity>builder()
                .records(couponPackageEntities)
                .total(couponPackageResult.getTotal())
                .size(couponPackageResult.getSize())
                .current(couponPackageResult.getCurrent())
                .build();
    }

    @Override
    public int getCustomerCouponPackageNum(Long customerId) {
        return orderDeliveryMapper.selectCustomerCouponPackageNum(customerId);
    }

    /**
     * PO转换为Entity
     */
    private OrderDeliveryEntity convertToEntity(OrderDeliveryPO po) {
        OrderDeliveryEntity entity = new OrderDeliveryEntity();
        BeanUtils.copyProperties(po, entity);
        return entity;
    }
}