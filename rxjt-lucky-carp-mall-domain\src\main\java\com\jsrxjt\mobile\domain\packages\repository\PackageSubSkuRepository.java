package com.jsrxjt.mobile.domain.packages.repository;

import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSkuSubJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;

import java.util.List;

/**
 * @Description: 套餐subsku服务
 */
public interface PackageSubSkuRepository {

    /**
     * 批量更新状态
     */
    void updatePackageSubSkuStatus(List<PackageSkuSubJobEntity> entityList);

    PackageSkuSubJobEntity getPackageSubSkuById(Long id);

    /**
     * 原子性增加套餐子SKU销量
     *
     * @param packageSubSkuId 套餐子SKU ID
     * @param quantity        增加的数量
     * @return 更新的行数
     */
    int increaseSoldNum(Long packageSubSkuId, Integer quantity);
}
