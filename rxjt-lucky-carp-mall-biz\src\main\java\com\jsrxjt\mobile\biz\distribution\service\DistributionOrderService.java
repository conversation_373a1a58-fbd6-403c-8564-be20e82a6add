package com.jsrxjt.mobile.biz.distribution.service;

import com.jsrxjt.mobile.api.distribution.dto.request.DistributionOrderPayNotifyDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.order.service.strategy.OrderInfoBuilder;

/**
 * <AUTHOR>
 * @Date 2025/7/24 17:48
 */
public interface DistributionOrderService {

    DistributionCreateOrderResponseDTO distributionCreateOrder(CreateOrderDTO createOrderDTO, OrderInfoBuilder builder);

    void distributionOrderPayNotify(DistributionOrderPayNotifyDTO dto);
}
