package com.jsrxjt.mobile.biz.payment.strategy.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.coupon.types.CouponTypeEnum;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformFactory;
import com.jsrxjt.mobile.domain.coupon.gateway.CouponPlatformStrategy;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsSkuRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageSubSkuRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 卡券类型支付成功处理策略（基于flatProductType）
 * 支持 flatProductType / 100 = 1 的所有卡券类型
 * 
 * <AUTHOR> Fengping
 * @since 2025/7/28
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PackagePaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final OrderRepository orderRepository;
    private final PackageGoodsSkuRepository packageGoodsSkuRepository;
    private final PackageSubSkuRepository packageSubSkuRepository;

    @Override
    public boolean supports(Integer flatProductType) {
        // 支持 套餐类型
        return flatProductType != null && flatProductType / 100 == 2;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理套餐订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());
        // 如果订单有externalOrderNo 则表示已经推单成功，直接返回
        if (StringUtils.isNotBlank(order.getExternalOrderNo())) {
            log.warn("套餐订单已推单成功，直接返回,订单号：{}，外部订单号：{}",
                    order.getOrderNo(), order.getExternalOrderNo());
            return;
        }

        // 获取套餐订单项的子SKU订单列表
        List<SubSkuOrderEntity> subSkuOrders = order.getOrderItems().get(0).getSubSkuOrders();
        if (subSkuOrders == null || subSkuOrders.isEmpty()) {
            log.error("套餐订单子SKU订单为空，订单号：{}", order.getOrderNo());
            throw new BizException("套餐订单子SKU订单为空");
        }

        // 1. 遍历套餐子商品并推单到卡管
        List<String> externalOrderNos = new ArrayList<>();
        for (SubSkuOrderEntity subSkuOrder : subSkuOrders) {
            // 如果子SKU订单的外部订单号为空，则推单
            if (StringUtils.isBlank(subSkuOrder.getExternalOrderNo())) {
                String externalOrderNo = pushSubCouponOrder(subSkuOrder);
                externalOrderNos.add(externalOrderNo);
                // 记录外部订单号到sub_sku_order
                updateSubSkuOrderExternalNo(subSkuOrder, externalOrderNo);
                // 推单成功，更新子SKU销量
                updatePackageSubSkuSoldNum(subSkuOrder);
                // 推单成功即更新订单状态为发货中
                updateOrderToDelivering(order);
            } else {
                log.info("套餐订单子SKU订单已推单成功，不再重推，订单号：{}，子订单号：{}，外部订单号：{}",
                        order.getOrderNo(), subSkuOrder.getSubOrderNo(), subSkuOrder.getExternalOrderNo());
                externalOrderNos.add(subSkuOrder.getExternalOrderNo());
            }
        }

        // 2. 更新t_order订单的外部订单号
        updateOrderExternalOrderNos(order, String.join(",", externalOrderNos));

        // 3. 更新套餐SKU销量
        updatePackageSkuSoldNum(order);

        log.info("套餐订单支付成功处理完成，订单号：{}，子订单推送数量：{}",
                order.getOrderNo(), subSkuOrders.size());
    }

    /**
     * 推送子商品订单到卡管
     */
    private String pushSubCouponOrder(SubSkuOrderEntity subSkuOrder) {
        // 根据子SKU的卡券类型获取对应的卡券平台策略
        CouponTypeEnum couponType = CouponTypeEnum.getByType(subSkuOrder.getCouponType());

        if (couponType == null) {
            log.error("无法识别的卡券类型，子订单号：{}，卡券类型码：{}",
                    subSkuOrder.getSubOrderNo(), subSkuOrder.getCouponType());
            throw new BizException("无法识别的卡券类型，卡券类型码：" + subSkuOrder.getCouponType());
        }

        log.info("识别子SKU卡券类型成功，子订单号：{}，卡券类型：{}",
                subSkuOrder.getSubOrderNo(), couponType.getText());

        // 获取对应的卡券平台策略并推单
        CouponPlatformStrategy couponPlatform = CouponPlatformFactory.getCouponPlatform(couponType);
        CouponCreateOrderResponseDTO pushResult = couponPlatform.pushSubCouponOrder(subSkuOrder);

        if (pushResult == null || pushResult.getExternalOrderNo() == null) {
            log.error("套餐子商品推单失败，子订单号：{}，卡券类型：{}",
                    subSkuOrder.getSubOrderNo(), couponType.getText());
            throw new BizException("套餐子商品推单失败");
        }

        log.info("套餐子商品推单成功，子订单号：{}，卡券类型：{}，外部订单号：{}",
                subSkuOrder.getSubOrderNo(), couponType.getText(), pushResult.getExternalOrderNo());

        return pushResult.getExternalOrderNo();
    }

    /**
     * 更新子SKU订单的外部订单号
     */
    private void updateSubSkuOrderExternalNo(SubSkuOrderEntity subSkuOrder, String externalOrderNo) {
        SubSkuOrderEntity updateSubSku = new SubSkuOrderEntity();
        updateSubSku.setId(subSkuOrder.getId());
        updateSubSku.setCustomerId(subSkuOrder.getCustomerId());
        updateSubSku.setExternalOrderNo(externalOrderNo);
        updateSubSku.setModTime(LocalDateTime.now());
        updateSubSku.setSubSkuDeliveryStatus(DeliveryStatusEnum.DELIVERING.getCode().intValue());
        orderRepository.updateSubSkuOrder(updateSubSku);

        log.info("子SKU订单外部订单号更新成功，子订单号：{}，外部订单号：{}",
                subSkuOrder.getSubOrderNo(), externalOrderNo);
    }

    /**
     * 更新订单状态为发货中
     */
    private void updateOrderToDelivering(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERING.getCode().intValue());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("套餐订单发货状态更新成功，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERING.getDescription());
    }

    private void updateOrderExternalOrderNos(OrderInfoEntity order, String externalOrderNos) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setExternalOrderNo(externalOrderNos);
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
        log.info("套餐订单外部订单号更新成功，订单号：{}，外部订单号：{}", order.getOrderNo(), externalOrderNos);

    }

    /**
     * 更新套餐子SKU销量
     */
    private void updatePackageSubSkuSoldNum(SubSkuOrderEntity subSkuOrder) {
        try {
            Long packageSubSkuId = subSkuOrder.getSubSkuId();
            Integer quantity = subSkuOrder.getQuantity();

            if (packageSubSkuId == null) {
                log.warn("套餐子SKU ID为空，无法更新销量，子订单号：{}", subSkuOrder.getSubOrderNo());
                return;
            }

            if (quantity == null || quantity <= 0) {
                log.warn("套餐子SKU数量无效，无法更新销量，子订单号：{}，数量：{}",
                        subSkuOrder.getSubOrderNo(), quantity);
                return;
            }

            // 使用原子操作直接增加销量，避免并发问题
            int updateResult = packageSubSkuRepository.increaseSoldNum(packageSubSkuId, quantity);
            if (updateResult > 0) {
                log.info("套餐子SKU销量更新成功，子SKU ID：{}，增加数量：{}，子订单号：{}",
                        packageSubSkuId, quantity, subSkuOrder.getSubOrderNo());
            } else {
                log.warn("套餐子SKU销量更新失败，可能子SKU不存在，子SKU ID：{}，子订单号：{}",
                        packageSubSkuId, subSkuOrder.getSubOrderNo());
            }

        } catch (Exception e) {
            log.error("更新套餐子SKU销量异常，子订单号：{}，错误信息：{}",
                    subSkuOrder.getSubOrderNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新套餐SKU销量
     */
    private void updatePackageSkuSoldNum(OrderInfoEntity order) {
        try {
            // 获取订单中的套餐SKU ID和数量
            Long packageSkuId = order.getProductSkuId();
            Integer quantity = 1; // 默认数量为1

            // 如果订单有订单项，从订单项中获取数量
            if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
                quantity = order.getOrderItems().get(0).getQuantity();
                // 如果订单主表的productSkuId为空，从订单项中获取
                if (packageSkuId == null) {
                    packageSkuId = order.getOrderItems().get(0).getProductSkuId();
                }
            }

            if (packageSkuId == null) {
                log.warn("套餐SKU ID为空，无法更新销量，订单号：{}", order.getOrderNo());
                return;
            }

            if (quantity == null || quantity <= 0) {
                log.warn("套餐数量无效，无法更新销量，订单号：{}，数量：{}", order.getOrderNo(), quantity);
                return;
            }

            // 使用原子操作直接增加销量，避免并发问题
            int updateResult = packageGoodsSkuRepository.increaseSoldNum(packageSkuId, quantity);
            if (updateResult > 0) {
                log.info("套餐SKU销量更新成功，套餐SKU ID：{}，增加数量：{}，订单号：{}",
                        packageSkuId, quantity, order.getOrderNo());
            } else {
                log.warn("套餐SKU销量更新失败，可能套餐SKU不存在，套餐SKU ID：{}，订单号：{}",
                        packageSkuId, order.getOrderNo());
            }

        } catch (Exception e) {
            log.error("更新套餐SKU销量异常，订单号：{}，错误信息：{}", order.getOrderNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

}
