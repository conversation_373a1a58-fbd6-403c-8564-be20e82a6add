package com.jsrxjt.mobile.infra.global.gatewayimpl;

import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.SpringContextHolder;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.global.gateway.GlobalGateWay;
import com.jsrxjt.mobile.domain.global.request.GlobalGoodsListRequest;
import com.jsrxjt.mobile.domain.global.request.GlobalTicketListRequest;
import com.jsrxjt.mobile.domain.global.response.GlobalGoodsListResponse;

import com.jsrxjt.mobile.domain.global.response.GlobalTicketResponse;
import com.jsrxjt.mobile.domain.ticket.entity.TicketEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class GlobalGateWayImpl implements GlobalGateWay {

    protected final HttpClientGateway httpClientGateway;

    @Value("${rxGlobal.activePageLink}")
    private String activePageLink;

    @Value("${rxGlobal.ticketListLink}")
    private String ticketListLink;

    @Value("${rxGlobal.key}")
    private String key;

    @Override
    public List<GlobalGoodsListResponse> getGoodsList(GlobalGoodsListRequest request) {
        JSONObject json = new JSONObject();
        json.put("keyword", request.getKeyword());
        try {
            String rxResponse = httpClientGateway.doPostJson(activePageLink, json.toJSONString(), 3000, 5000);
            JSONObject result = new JSONObject().parseObject(rxResponse);
            if (ObjectUtil.equal(result.getInteger("code"), 1)){
                JSONArray productJsonArray = result.getJSONObject("response").getJSONArray("floorList").getJSONObject(0).getJSONArray("relateGoodsList");
                if (productJsonArray != null){
                    return productJsonArray.stream().map(product -> {
                        JSONObject productJson = (JSONObject) product;
                        GlobalGoodsListResponse response = new GlobalGoodsListResponse();
                        response.setProductId(productJson.getString("productId"));
                        response.setProductItemId(productJson.getString("productItemId"));
                        response.setImgUrl(productJson.getString("imgUrl"));
                        response.setProductName(productJson.getString("productName"));
                        response.setProductSubtitle(productJson.getString("productSubtitle"));
                        response.setPromotionLabels(productJson.getJSONArray("promotionLabels").toList(String.class));
                        BigDecimal price = productJson.getBigDecimal("price");
                        BigDecimal originalPrice = productJson.getBigDecimal("originalPrice");
                        if (price != null && price.compareTo(BigDecimal.ZERO) > 0){
                            response.setPrice(price);
                            response.setOriginalPrice(originalPrice);
                        }else {
                            response.setPrice(originalPrice);
                        }
                        return response;
                    }).collect(Collectors.toList());
                }else {
                    return Collections.emptyList();
                }
            }
        }catch (IOException e){
            log.error("查询全球购活动商品列表失败", e.getMessage());
        }
        return null;
    }

    @Override
    public PageDTO<GlobalTicketResponse> getTicketList(GlobalTicketListRequest request) {
        JSONObject json = new JSONObject();
        json.put("unionId", request.getUnionId());
        json.put("toPage", request.getToPage());
        json.put("pageRows", request.getPageRows());
        String sign = DigestUtil.md5Hex("key=" + key +"&body=" + json);
        try {
            String rxResponse = httpClientGateway.doPostJson(ticketListLink + "?sign=" + sign, json.toJSONString(), 3000, 5000);
            JSONObject result = JSONObject.parseObject(rxResponse);
            if (ObjectUtil.equal(result.getInteger("code"), 1)){
                List<GlobalTicketResponse> list = JSONArray.parseArray(result.getJSONArray("response").toJSONString(), GlobalTicketResponse.class);
                Long total = result.getJSONObject("page").getLong("totalRows");
                Long size = result.getJSONObject("page").getLong("pageRows");
                Long current = result.getJSONObject("page").getLong("curPage");
                Long pages = result.getJSONObject("page").getLong("pageAmount");
                return PageDTO.build(list,total, size, current, pages);
            }
        }catch (IOException e){
            log.error("查询全球购券列表失败", e.getMessage());
        }
        return null;
    }


}
