package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/23 17:22
 * 分销应用创建订单通用参数
 */
@Data
public class DistributionBaseCreateOrderDTO {
    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "分销业务中心订单号")
    private String orderNo;

    @Schema(description = "分销业务中心交易号")
    private String tradeNo;

    @Schema(description = "订单金额，单位元，两位小数")
    private String totalAmount;

    @Schema(description = "订单金额，单位元，两位小数")
    private String tradeAmount;

    @Schema(description = "下单时间，UTC时间戳，10位")
    private String orderTime;

    @Schema(description = "订单过期时间 单位：分钟")
    private String orderExpire;

    @Schema(description = "交易时间，UTC时间戳，10位")
    private String tradeTime;

    @Schema(description = "支付超时时间，UTC时间戳，10位")
    private String expireTime;

    @Schema(description = "支付结果页面(支付结束后的跳转页面)")
    private String resultPageUrl;

    @Schema(description = "支付结果页面(支付结束后的跳转页面)")
    private String resultUrl;

    @Schema(description = "订单详情页")
    private String detailPageUrl;

    @Schema(description = "订单详情页")
    private String detailUrl;

    @Schema(description = "订单详情页")
    private String returnUrl;
}
