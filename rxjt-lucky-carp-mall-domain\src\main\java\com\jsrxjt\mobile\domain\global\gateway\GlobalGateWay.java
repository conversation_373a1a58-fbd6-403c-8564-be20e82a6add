package com.jsrxjt.mobile.domain.global.gateway;


import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.global.request.GlobalGoodsListRequest;
import com.jsrxjt.mobile.domain.global.request.GlobalTicketListRequest;
import com.jsrxjt.mobile.domain.global.response.GlobalGoodsListResponse;
import com.jsrxjt.mobile.domain.global.response.GlobalTicketResponse;

import java.util.List;

/**
 * 全球购相关对接
 * <AUTHOR>
 * @date 2025/06/24
 */
public interface GlobalGateWay {

    /**
     * 全球购活动商品列表
     * @param request
     * @return {@link List}<{@link GlobalGoodsListResponse}>
     */
    List<GlobalGoodsListResponse> getGoodsList(GlobalGoodsListRequest request);

    /**
     * 获取全球购商城优惠券列表
     * @param request 请求参数
     * @return 券列表
     */
    PageDTO<GlobalTicketResponse> getTicketList(GlobalTicketListRequest request);

}
