package com.jsrxjt.mobile.domain.payment.gateway.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 预支付响应结果
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
public class PrePayResponse {
    
    /**
     * 业务订单号
     */
    @JSONField(name = "order_no")
    private String orderNo;
    
    /**
     * 交易流水号
     */
    @JSONField(name = "trade_no")
    private String tradeNo;
    
    /**
     * 预支付订单号
     */
    @JSONField(name = "pre_order_no")
    private String preOrderNo;
    
    /**
     * 是否存在微信支付
     */
    @JSONField(name = "is_exist_wx")
    private Boolean isExistWx;

    /**
     * 是否设置了支付密码
     */
    @JSONField(name = "is_set_pay_pass")
    private Boolean isSetPayPass;

    /**
     * 是否免密支付
     */
    @JSONField(name = "is_password_free_payment")
    private Boolean isPasswordFreePayment;
    
    /**
     * 微信支付金额(分)
     */
    @JSONField(name = "wx_pay_amount")
    private Integer wxPayAmount;

    
    /**
     * 支付金额(分)
     */
    @JSONField(name = "pay_amount")
    private Integer payAmount;

    /**
     * 卡支付金额(分)
     */
    @JSONField(name = "card_pay_amount")
    private Integer cardPayAmount;
    
    /**
     * 卡类型信息
     */
    @JSONField(name = "trade_category_info")
    private List<TradeCategoryInfo> cardCategoryInfo;

    /**
     * 来源
     */
    @JSONField(name = "source")
    private String source;

    /**
     * 运行数据
     */
    @JSONField(name = "run_data")
    private String runData;

    
    @Data
    public static class TradeCategoryInfo  {
        /**
         * 卡类型
         */
        @JSONField(name = "card_type")
        private String cardType;
        
        /**
         * 金额
         */
        @JSONField(name = "amount")
        private Integer amount;
    }
}