package com.jsrxjt.mobile.infra.order.persistent.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.common.mybatis.config.CommonBaseMapper;
import com.jsrxjt.mobile.domain.order.query.CouponPackageListQuery;
import com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageBrandPO;
import com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageDetailPO;
import com.jsrxjt.mobile.infra.order.persistent.po.CouponPackageListPO;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderDeliveryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单发货数据访问层
 * 
 * <AUTHOR>
 * @since 2025/7/9
 */
@Mapper
public interface OrderDeliveryMapper extends CommonBaseMapper<OrderDeliveryPO> {

    IPage<CouponPackageBrandPO> selectCustomerCouponPackageBrandList(Page<CouponPackageBrandPO> page,
                                                                     @Param("param") CouponPackageListQuery query);

    List<CouponPackageListPO> selectCustomerCouponPackageList(@Param("param") CouponPackageListQuery query);

    List<CouponPackageDetailPO> selectCouponPackageListBySkuId(@Param("skuId")Long  skuId,@Param("customerId") Long customerId);

    IPage<CouponPackageBrandPO> selectCycCouponPackageBrandList(Page<CouponPackageBrandPO> page,
                                                                     @Param("param") CouponPackageListQuery query);

    List<CouponPackageListPO> selectCycCouponPackageList(@Param("param") CouponPackageListQuery query);

    int selectCustomerCouponPackageNum(@Param("customerId") Long customerId);

   void updateDelFlag(@Param("idList") List<Long> idList,@Param("originDelFlag")Integer originDelFlag, @Param("delFlag")Integer delFlag);

}