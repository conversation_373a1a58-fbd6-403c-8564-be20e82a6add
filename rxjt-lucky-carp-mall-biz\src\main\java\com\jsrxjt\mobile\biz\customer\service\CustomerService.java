package com.jsrxjt.mobile.biz.customer.service;

import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.customer.request.*;
import com.jsrxjt.mobile.api.customer.response.*;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;

import java.util.List;

public interface CustomerService {

    CustomerResponse login(CustomerLoginRequest request);

    void sendVerificationCode(CustomerSendVerificationCodeRequest request);

    CustomerResponse bindPhone(CustomerBindPhoneRequest request);

    CustomerInfoResponse getCustomerInfo(Long customerId);

    void editCustomerInfo(CustomerEditRequest request);

    /**
     * 新增或修改密码
     */
    void addOrModifyPassword(CustomerPasswordEditRequest request);

    void setFreePassword(CustomerSetFreePasswordRequest request);

    List<CustomerCardResponse> getCardList(CustomerCardRequest request);

    CustomerCheckLoginOffResponse checkIsAllowedLoginOff(CustomerRequest request);

    void deleteAccount(CustomerDeleteAccountRequest request);

    void bindCard(CustomerBindCardRequest request);

    void unbindCard(CustomerUnbindCardRequest request);

    void setPaySort(CustomerSetPaySortRequest request);

    CustomerPaySortResponse getPaySort(Long customerId);

    /**
     * 获取个人中心广告列表
     * @param regionId 三级地址id
     * @return 广告列表
     */
    List<AdvertisementInfoDTO> getPersonalCenterAd(Integer regionId);

    Boolean checkCustomerHasPayPassword(Long customerId);

    LoginOffConfigResponse getLoginOffConfig();

    CustomerEntity getCustomerById(Long customerId);

    CardBalanceResponse getBalanceByCardNoAndPassword(CardBalanceQueryRequest request);

}
