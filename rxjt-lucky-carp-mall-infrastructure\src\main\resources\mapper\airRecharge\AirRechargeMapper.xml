<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.airRecharge.persistent.mapper.AirRechargeMapper">

    <select id="getAirRechargeInfo" resultType="com.jsrxjt.mobile.infra.airRecharge.persistent.po.AirRechargePO">
        SELECT v.id AS id,
               v.mobile AS mobile,
               v.price AS price,
               v.true_price AS truePrice,
               v.card_no AS cardNo,
               v.create_time AS createTime,
               v.update_time AS updateTime,
               v.status AS status,
               v.batch_recharge_id AS batchRechargeId,
               v.is_newreg AS isNewreg,
               v.is_msg AS isMsg,
               v.is_underline AS isUnderline,
               v.success_time AS successTime,
               v.suc_login_time AS sucLoginTime,
               v.order_no AS orderNo,
               v.revocation_no AS revocationNo,
               v.revocation_time AS revocationTime,
               v.need_sign AS needSign,
               v.is_windows AS isWindows,
               v.company_id AS companyId,
               v.sign_pic AS signPic,
               r.recharge_type AS rechargeType
        FROM batch_vip v left join batch_recharge r on v.batch_recharge_id = r.id
        where v.status = 3
          and v.mobile = #{mobile}
          and v.is_msg = 0
    </select>

</mapper>
