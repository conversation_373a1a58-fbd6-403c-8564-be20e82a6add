package com.jsrxjt.mobile.infra.airRecharge.persistent;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jsrxjt.mobile.domain.airRecharge.entity.AirRechargeEntity;
import com.jsrxjt.mobile.domain.airRecharge.repository.AirRechargeRepository;
import com.jsrxjt.mobile.infra.airRecharge.persistent.mapper.AirRechargeMapper;
import com.jsrxjt.mobile.infra.airRecharge.persistent.po.AirRechargePO;
import com.jsrxjt.mobile.infra.airRecharge.persistent.po.BatchVipPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description: 空中充值
 * @Author: ywt
 * @Date: 2025-08-26 15:14
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
public class AirRechargeRepositoryImpl implements AirRechargeRepository {
    private final AirRechargeMapper airRechargeMapper;

    @Override
    public List<AirRechargeEntity> getAirRechargeInfo(String mobile) {
        List<AirRechargePO> list = airRechargeMapper.getAirRechargeInfo(mobile);
        return BeanUtil.copyToList(list, AirRechargeEntity.class);
    }

    @Override
    public void updateAirRechargeStatus(Integer id) {
        LambdaUpdateWrapper<BatchVipPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BatchVipPO::getId, id)
                .set(BatchVipPO::getIsMsg, 1);
        airRechargeMapper.update(null, updateWrapper);
    }
}
