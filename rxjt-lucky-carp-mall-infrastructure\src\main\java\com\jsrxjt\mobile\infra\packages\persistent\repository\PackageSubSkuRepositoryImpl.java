package com.jsrxjt.mobile.infra.packages.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.api.product.types.ProductExplainTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSkuSubJobEntity;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsRepository;
import com.jsrxjt.mobile.domain.packages.repository.PackageSubSkuRepository;
import com.jsrxjt.mobile.domain.product.entity.ProductExplainEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductLableEntity;
import com.jsrxjt.mobile.infra.packages.persistent.mapper.PackageGoodsMapper;
import com.jsrxjt.mobile.infra.packages.persistent.mapper.PackageGoodsSkuMapper;
import com.jsrxjt.mobile.infra.packages.persistent.mapper.PackageSubSkuMapper;
import com.jsrxjt.mobile.infra.packages.persistent.po.PackageGoodsPO;
import com.jsrxjt.mobile.infra.packages.persistent.po.PackageGoodsSkuPO;
import com.jsrxjt.mobile.infra.packages.persistent.po.PackageSubSkuPO;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductExplainMapper;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductLableMapper;
import com.jsrxjt.mobile.infra.product.persistent.mapper.ProductSubscriptMapper;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductExplainPO;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductLablePO;
import com.jsrxjt.mobile.infra.product.persistent.po.ProductSubscriptPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 套餐服务
 * @Author: ywt
 * @Date: 2025-05-09 18:17
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class PackageSubSkuRepositoryImpl implements PackageSubSkuRepository {

    private final PackageSubSkuMapper packageSubSkuMapper;

    /**
     * 批量更新状态
     *
     * @param entityList
     */
    @Override
    public void updatePackageSubSkuStatus(List<PackageSkuSubJobEntity> entityList) {
        packageSubSkuMapper.updatePackageSubSkuStatus(entityList);
    }

    /**
     * @param id
     * @return
     */
    @Override
    public PackageSkuSubJobEntity getPackageSubSkuById(Long id) {
        PackageSubSkuPO packageSubSkuPO = packageSubSkuMapper.selectById(id);
        if (packageSubSkuPO != null) {
            return BeanUtil.copyProperties(packageSubSkuPO, PackageSkuSubJobEntity.class);
        }
        return null;

    }

    @Override
    public int increaseSoldNum(Long packageSubSkuId, Integer quantity) {
        return packageSubSkuMapper.increaseSoldNum(packageSubSkuId, quantity);
    }
}
