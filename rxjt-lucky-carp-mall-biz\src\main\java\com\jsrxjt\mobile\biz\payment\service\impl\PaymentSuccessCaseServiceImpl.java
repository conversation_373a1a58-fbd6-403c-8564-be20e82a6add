package com.jsrxjt.mobile.biz.payment.service.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.payment.service.PaymentSuccessCaseService;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategyFactory;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.payment.types.PaymentSuccessMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 支付成功业务处理（重构版 - 使用策略模式，基于flatProductType）
 * 
 * <AUTHOR> <PERSON>
 * @since 2025/1/27
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentSuccessCaseServiceImpl implements PaymentSuccessCaseService {

    private final OrderRepository orderRepository;
    private final DistributedLock distributedLock;
    private final PaymentSuccessStrategyFactory strategyFactory;

    private static final String PAYMENT_SUCCESS_LOCK_PREFIX = "payment_success_lock:";

    @Override
    public void handlePaymentSucceeded(PaymentSuccessMessage event) {
        log.info("开始处理支付已成功事件，订单号：{}", event.getOrderNo());

        String orderNo = event.getOrderNo();
        String lockKey = PAYMENT_SUCCESS_LOCK_PREFIX + orderNo;

        // 加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                throw new BizException("获取支付成功处理锁失败，订单号：" + orderNo);
            }

            // 1. 查询订单信息
            OrderInfoEntity order = orderRepository.findByOrderNo(orderNo);
            if (order == null) {
                log.warn("订单不存在，订单号：{}", orderNo);
                return;
            }

            // 2. 幂等校验：检查发货状态，如果不是待发货状态或者发货中（为什么发货中也校验通过，因为套餐有部分发货的情况），则不处理
            if (!DeliveryStatusEnum.UNDELIVERED.getCode().equals(order.getDeliveryStatus().byteValue())
                    && !DeliveryStatusEnum.DELIVERING.getCode().equals(order.getDeliveryStatus().byteValue())) {
                log.info("订单发货状态不是待发货，跳过处理，订单号：{}，当前发货状态：{}",
                        orderNo, order.getDeliveryStatus());
                return;
            }

            // 3. 获取扁平化产品类型，选择对应的处理策略
            Integer flatProductType = order.getFlatProductType();

            if (flatProductType == null) {
                log.error("扁平化产品类型为空，订单号：{}", orderNo);
                throw new BizException("扁平化产品类型为空");
            }

            // 4. 使用策略处理业务逻辑
            PaymentSuccessStrategy strategy = strategyFactory.getStrategy(flatProductType);
            strategy.handle(order);

            log.info("成功处理支付已成功事件，订单号：{}，扁平化产品类型：{}",
                    orderNo, flatProductType);

        } catch (BizException e) {
            log.error("处理支付成功事件业务异常，订单号：{}，错误信息：{}", orderNo, e.getMsg(), e);
            throw e;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("获取分布式锁被中断，订单号：" + orderNo);
        } catch (Exception e) {
            log.error("处理支付成功事件系统异常，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            throw e;
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }
}
