package com.jsrxjt.mobile.biz.birth.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.birth.request.BirthCouponRequestDTO;
import com.jsrxjt.mobile.api.birth.response.BirthCouponResponseDTO;
import com.jsrxjt.mobile.biz.birth.CustomerBirthCouponService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.gateway.FuliquanShopTicketGateway;
import com.jsrxjt.mobile.domain.customer.gateway.request.SendTicketRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.SendTicketResponseDTO;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerBirthCouponServiceImpl implements CustomerBirthCouponService {

    private final CustomerRepository customerRepository;
    private final FuliquanShopTicketGateway fuliquanShopTicketGateway;
    /**
     * 领取会员生日券（300-100）
     * @return
     */
    @Override
    public void receiveBirthCoupon() {
//        Long customerId = StpUtil.getLoginIdAsLong();
//        //判断是否已经领取
//        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
//        if(customerEntity == null || customerEntity.getDelFlag() == 1 || customerEntity.getStatus() != 1){
//            throw new BizException("该会员账号不存在或注销");
//        }
//        String birthday = customerEntity.getBirthday();
//        if(birthday == null){
//            throw new BizException("请您先设置生日");
//        }
      //  Date jdkDate = DateUtil.parse(birthday).toJdkDate();
        //生日是否在当月之中
//        if(DateTime.of(jdkDate).month() != DateTime.of(new Date()).month()){
//            //前端用于特殊展示
//            throw BizException.BIRTH_COUPON_NOT_THIS_MONTH;
//        }
    //    Long vipId = customerEntity.getVipId();
        Long vipId = 651027l;
        SendTicketRequestDTO requestDTO = new SendTicketRequestDTO();
        requestDTO.setVipId(vipId);
        requestDTO.setTimestamp(Instant.now().getEpochSecond());
        requestDTO.setNonce(IdUtil.fastSimpleUUID());
        SendTicketResponseDTO sendTicketResponseDTO = fuliquanShopTicketGateway.sendCoupon(requestDTO);
        if(sendTicketResponseDTO == null || !sendTicketResponseDTO.getSendCoupon().equals("1")){
            throw new BizException("领取生日券失败");
        }
    }
}
