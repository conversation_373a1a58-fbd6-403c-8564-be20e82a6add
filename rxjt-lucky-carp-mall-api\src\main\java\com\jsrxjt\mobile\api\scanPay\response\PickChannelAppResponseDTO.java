package com.jsrxjt.mobile.api.scanPay.response;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 展码付应用
 */
@Schema(description = "展示付区域应用")
@Data
public class PickChannelAppResponseDTO {
    @Schema(description = "应用ID ")
    private Long appId;
    /*@Schema(description = "商户id，提货券分销中台提供（仅type=4有效）")
    private String thirdId;*/
    @Schema(description = "应用名称")
    private String appName;
    @Schema(description = "log图片url")
    private String logoUrl;
    @Schema(description = "应用类型：1普通应用 4扫码提货 6首页提货")
    private Integer type;
    @Schema(description = "是否选中， 0--未选中 1--选中")
    private Integer isSelected;
    @Schema(description = "付款码信息，仅isSelected为1有效")
    private HomeScanNumberResponseDTO payCodeRes;
}
