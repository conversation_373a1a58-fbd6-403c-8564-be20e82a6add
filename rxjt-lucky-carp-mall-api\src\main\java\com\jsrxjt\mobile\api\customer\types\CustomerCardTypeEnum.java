package com.jsrxjt.mobile.api.customer.types;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 用户卡类型枚举
 */
public enum CustomerCardTypeEnum {

    BUSINESS_CARD(2, 2, "商联卡", "RBIZ"),

    RED_CARD(21, 2, "红卡", "RR"),

    WHITE_CARD(7, 7, "白金账户", "RW"),

    BLACK_CARD(5, 5, "黑金账户", "RB"),

//    DELIVERY_VOUCHER(22, 22, "提货凭证", "VT"),

    TRADE_UNION_VOUCHER(23, 23, "工会凭证", "LT");

    CustomerCardTypeEnum(int type, int rxMemberCardType, String name, String cardTypeCode) {
        this.type = type;
        this.rxMemberCardType = rxMemberCardType;
        this.name = name;
        this.cardTypeCode = cardTypeCode;
    }

    /**
     * 用户卡类型
     */
    private final int type;

    /**
     * 大会员中心卡类型
     */
    private final int rxMemberCardType;

    /**
     * 卡名称
     */
    private final String name;

    /**
     * 卡类型编码
     */
    private final String cardTypeCode;

    public int getType() {
        return type;
    }

    public int getRxMemberCardType() {
        return rxMemberCardType;
    }

    public String getName() {
        return name;
    }

    public String getCardTypeCode() {
        return cardTypeCode;
    }

    public static String getAllRxMemberCardTypes() {
        return Arrays.stream(CustomerCardTypeEnum.values())
                .mapToInt(CustomerCardTypeEnum::getRxMemberCardType)
                .distinct()  // Remove duplicates
                .mapToObj(String::valueOf)
                .collect(Collectors.joining(","));
    }

    public static Integer getRxMemberCardTypeByType(int type) {
        for (CustomerCardTypeEnum cardTypeEnum : values()) {
            if (cardTypeEnum.type == type) {
                return cardTypeEnum.rxMemberCardType;
            }
        }
        throw new IllegalArgumentException("不支持的卡类型: " + type);
    }

    public static String getCardTypeCodeByType(int type) {
        for (CustomerCardTypeEnum cardTypeEnum : values()) {
            if (cardTypeEnum.type == type) {
                return cardTypeEnum.cardTypeCode;
            }
        }
        throw new IllegalArgumentException("不支持的卡类型: " + type);
    }

}
