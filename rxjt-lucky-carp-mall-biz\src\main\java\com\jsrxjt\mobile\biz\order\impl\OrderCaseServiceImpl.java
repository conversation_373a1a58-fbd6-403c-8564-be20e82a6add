package com.jsrxjt.mobile.biz.order.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.dto.request.OrderListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.GiftTicketResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderListResponseDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.order.GiftTicketValidationService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.inventory.entity.SkuReleaseInventoryEntity;
import com.jsrxjt.mobile.domain.inventory.service.SkuReleaseInventoryService;
import com.jsrxjt.mobile.domain.order.entity.*;
import com.jsrxjt.mobile.domain.order.messaging.OrderMessageProducer;
import com.jsrxjt.mobile.domain.order.query.OrderListQuery;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.repository.TradeOrderRepository;
import com.jsrxjt.mobile.domain.order.service.CalculateAmountService;
import com.jsrxjt.mobile.domain.order.service.strategy.OrderInfoBuilder;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.DefaultOrderInfoBuilder;
import com.jsrxjt.mobile.domain.order.types.OrderMessage;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.product.service.ProductItemDomainService;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.product.service.validator.ProductInventoryValidationService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Fengping
 * @since 2025/6/11
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderCaseServiceImpl implements OrderCaseService {

    private final ProductItemDomainService productItemDomainService;

    private final CalculateAmountService calculateAmountService;

    private final OrderRepository orderRepository;

    private final DefaultOrderInfoBuilder defaultOrderInfoBuilder;

    private final BusinessIdGenerator businessIdGenerator;

    private final ProductInventoryValidationService productInventoryValidationService;

    private final SkuReleaseInventoryService skuReleaseInventoryService;

    private final ProductSkuSellRegionService productSkuSellRegionService;

    private final GiftTicketValidationService giftTicketValidationService;

    private final RegionRepository regionRepository;

    private final CustomerRepository customerRepository;

    private final TradeOrderRepository tradeOrderRepository;

    private final OrderMessageProducer orderMessageProducer;

    private final Environment env;
    private final RedisUtil redisUtil;

    @Override
    public OrderInfoEntity submitOrder(CreateOrderDTO request) {

        // 使用默认构建器
        return submitOrder(request, defaultOrderInfoBuilder);
    }

    @Override
    public OrderInfoEntity submitOrder(CreateOrderDTO request, OrderInfoBuilder builder) {

        // 1. 通用业务校验和准备
        OrderSubmitContext context = prepareOrderSubmit(request);

        // 2. 使用指定的构建器构建订单信息
        OrderInfoEntity orderInfo = builder.buildOrderInfo(request, context.getProductItem(),
                context.getOrderAmountResult());
        // 后补充的赠券
        orderInfo.setGiftTickets(context.getGiftTickets());

        // 3. 构建订单明细
        OrderItemEntity orderItem = buildOrderItem(orderInfo, context.getProductItem(),
                request.getQuantity(), context.getOrderAmountResult());

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItem);
        orderInfo.setOrderItems(orderItems);

        // 4. 补充特有信息
        builder.supplementOrderInfo(orderInfo, request);

        // 5. 放量库存校验和扣减
        checkAndReduceSkuReleaseInventories(request, context, orderInfo);


        // 6. 增加当月该商品的购买金额
        addProductMonthlyAmount(orderInfo);

        // 7. 增加当月该商品的购买数量
        addProductMonthlyQuantity(orderInfo);

        // 8. 保存订单
        orderRepository.saveOrder(orderInfo);

        // 9. 记录日志
        log.info("订单信息构建完成，订单号：{}, 订单金额：{}, 支付金额：{},",
                orderInfo.getOrderNo(), context.getOrderAmountResult().getOrderAmount(),
                context.getOrderAmountResult().getPayAmount());
        log.info("订单明细构建完成，商品名称：{}, 数量：{}, 单价：{}",
                orderItem.getProductName(), orderItem.getQuantity(), orderItem.getSellPrice());
        // 10. 发送自动取消订单的延迟消息
        if (orderInfo.getPayExpireTimestamp() != null) {
            orderMessageProducer.sendOrderDelayAutoCancel(new OrderMessage(orderInfo), orderInfo.getPayExpireTimestamp());
        }
        return orderInfo;
    }

    @Override
    public void cancelOrder(String orderNo,Long customerId) {
        log.info("开始手动取消订单，订单号：{}", orderNo);
        cancelOrder(orderNo,customerId,true);
        log.info("取消订单完成，订单号：{}", orderNo);
    }

    @Override
    public void cancelOrderBySystem(String orderNo) {
        log.info("开始系统取消订单，订单号：{}", orderNo);
        cancelOrder(orderNo,null,false);
        log.info("系统取消订单完成，订单号：{}", orderNo);
    }

    private void cancelOrder(String orderNo,Long customerId,boolean isManualCancel) {
        // 1. 查询订单信息
        OrderInfoEntity orderInfo = orderRepository.findByOrderNo(orderNo);
        if (orderInfo == null) {
            throw new BizException("订单不存在");
        }
        if (isManualCancel && !Objects.equals(orderInfo.getCustomerId(), customerId)) {
            throw new BizException("订单不属于当前用户");
        }

        // 2. 校验订单状态是否可以取消
        validateOrderCanCancel(orderInfo);

        // 处理取消订单流程
        processCancelOrder(isManualCancel, orderInfo);

    }

    private void processCancelOrder(boolean isManualCancel, OrderInfoEntity orderInfo) {
        // 3. 更新订单状态为已取消
        updateOrderToCanceled(orderInfo, isManualCancel);

        // 4. 恢复放量库存（如果有放量库存）
        restoreReleaseInventoryIfNeeded(orderInfo);

        // 5. 恢复当月该商品的购买金额
        reduceProductMonthlyAmount(orderInfo);

         // 6. 恢复当月该商品的购买数量
        reduceProductMonthlyQuantity(orderInfo);
    }

    /**
     * 校验订单是否可以取消
     */
    private void validateOrderCanCancel(OrderInfoEntity orderInfo) {
        // 只有待支付状态的订单才能取消
        if (!Objects.equals(orderInfo.getPaymentStatus(), 0)) { // 0-待支付
            throw new BizException("只有待支付状态的订单才能取消");
        }
        
        // 订单状态必须是待支付
        if (!Objects.equals(orderInfo.getOrderStatus(), 0)) { // 0-待支付
            throw new BizException("订单状态不允许取消");
        }
    }

    /**
     * 更新订单状态为已取消
     */
    private void updateOrderToCanceled(OrderInfoEntity orderInfo,boolean isManualCancel) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(orderInfo.getId());
        updateOrder.setCustomerId(orderInfo.getCustomerId());
        if (isManualCancel) {
            updateOrder.setOrderStatus(OrderStatusEnum.MANUAL_CANCEL.getCode());
        } else {
            updateOrder.setOrderStatus(OrderStatusEnum.TIMEOUT_CANCEL.getCode());
            updateOrder.setCancelReason("超时系统自动取消");
        }
        updateOrder.setCancelTime(LocalDateTime.now());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrderCheckCurrentStatus(updateOrder,PaymentStatusEnum.UNPAID,OrderStatusEnum.PENDING_PAYMENT);
        
        log.info("订单状态更新为已取消，订单号：{}", orderInfo.getOrderNo());
    }

    /**
     * 恢复放量库存（如果需要）
     */
    private void restoreReleaseInventoryIfNeeded(OrderInfoEntity orderInfo) {
        if (orderInfo.getFlatProductType() / 100 == ProductTypeEnum.COUPON.getType()
                || orderInfo.getFlatProductType() / 100 == ProductTypeEnum.PACKAGE.getType()) {
            skuReleaseInventoryService.recoverSkuReleaseInventory(orderInfo.getOrderNo());
        }
    }

    private void checkAndReduceSkuReleaseInventories(CreateOrderDTO request, OrderSubmitContext context, OrderInfoEntity orderInfo) {
        if (context.getProductItem().hasReleaseInventoryProp()) {
            ProductItem productItem = context.getProductItem();
            ProductItemId productItemId = ProductItemId.of(productItem.getSpuId(), productItem.getSkuId(),
                    productItem.getProductType());
            List<SkuReleaseInventoryEntity> skuReleaseInventoryEntities = skuReleaseInventoryService
                    .checkAndGetSkuReleaseInventories(productItemId,
                    request.getRegionId(), request.getQuantity());
            skuReleaseInventoryService.reduceSkuReleaseInventory(skuReleaseInventoryEntities, request.getQuantity(),
                    orderInfo.getOrderNo());

        }
    }

    /**
     * 准备订单提交的通用逻辑
     */
    private OrderSubmitContext prepareOrderSubmit(CreateOrderDTO request) {


        if (isLocalProfile()) {
            request.setRegionId(37);
            //request.setRegionId(10170);
            request.setLongitude(new BigDecimal("116.4431360000"));
            request.setLatitude(new BigDecimal("39.9214440000"));
        } else {
            // 获取经纬度和regionId
            RegionEntity regionEntity = regionRepository.getCurrentRegion(request.getCustomerId());
            request.setRegionId(regionEntity.getId());
            request.setLongitude(new BigDecimal(regionEntity.getLng()));
            request.setLatitude(new BigDecimal(regionEntity.getLat()));
        }

        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());

        request.setCustomerMobile(customerEntity.getPhone());

        // 构建统一商品信息
        ProductItemId productItemId = ProductItemId.of(request.getProductSpuId(),
                request.getProductSkuId(), request.getProductType());
        ProductItem productItem = productItemDomainService.buildProductItem(productItemId);



        // 可售性校验
        if (!productItem.isAvailable()) {
            throw new BizException("商品不可售");
        }
        if (!productSkuSellRegionService.isSellableInRegion(request.getProductSpuId(),
                request.getProductSkuId(),
                ProductTypeEnum.getByType(request.getProductType()),
                request.getRegionId())) {
            throw new BizException("商品在当前区域不可售");

        }

        // 库存校验
        productInventoryValidationService.validateInventory(productItem, request.getQuantity());

        if (productItem.hasLimitNumPerMonth()) {
            Integer monthlyPurchaseQuantity = calculateAmountService.getMonthlyPurchaseQuantity(request.getCustomerId(), productItemId);
            if (monthlyPurchaseQuantity + request.getQuantity() > productItem.getLimitNumPerMonth()) {
                throw new BizException("单月购买数量超出限制,限购" + productItem.getLimitNumPerMonth() + "张，本月已购买"
                        + monthlyPurchaseQuantity + "张");
            }
        }
        // 补充应用的商品价格
        if (Objects.equals(productItem.getProductType(), ProductTypeEnum.APP.getType())
                && productItem.getPlatformPrice() == null) {
            productItem.setPlatformPrice(request.getExternalAppProductPrice());
        }

        // 赠券信息校验
        List<GiftTicketInfo> giftTickets = giftTicketValidationService.validateAndProcessGiftTickets(
                productItemId, request.getGiftTickets(), request.getQuantity());

        // 获取订单金额信息
        Integer regionId = request.getRegionId() == null ? 0 : request.getRegionId();
        PurchaseInfo purchaseInfo = PurchaseInfo.of(productItem, request.getQuantity(), regionId,
                request.getCustomerId());
        OrderAmountResult orderAmountResult = calculateAmountService.calculateOrderAmount(purchaseInfo);


        return new OrderSubmitContext(productItem, orderAmountResult,giftTickets);
    }

    /**
     * 构建订单明细信息
     */
    private OrderItemEntity buildOrderItem(OrderInfoEntity order,
            ProductItem productItem,
            Integer quantity,
            OrderAmountResult orderAmountResult) {

        OrderItemEntity orderItem = new OrderItemEntity();
        orderItem.setId(businessIdGenerator.generateId());

        // 订单关联信息
        orderItem.setOrderId(order.getId());
        orderItem.setOrderNo(order.getOrderNo());
        orderItem.setCustomerId(order.getCustomerId());

        // 商品基础信息
        orderItem.setProductType(productItem.getProductType());
        orderItem.setFlatProductType(productItem.getFlatProductType());
        orderItem.setProductId(productItem.getSpuId());
        orderItem.setProductName(productItem.getProductName());
        orderItem.setProductSkuId(productItem.getSkuId());
        orderItem.setProductSkuName(productItem.getFaceName());
        orderItem.setProductLogo(productItem.getProductLogo());
        orderItem.setFirstCategoryId(productItem.getFirstCategoryId());
        orderItem.setCategoryId(productItem.getCategoryId());

        // 渠道品牌信息
        orderItem.setChannelId(productItem.getChannelId());
        orderItem.setChannelName(productItem.getChannelName());
        orderItem.setBrandId(productItem.getBrandId());
        orderItem.setBrandName(productItem.getBrandName());

        // 价格信息
        orderItem.setSellPrice(productItem.getPlatformPrice());
        if (productItem.getPromotionSkuInfo() != null) {
            orderItem.setPromotionActivityId(productItem.getPromotionSkuInfo().getActivityId());
            BigDecimal promotionPrice = productItem.getPlatformPrice();
            if (productItem.getPromotionSkuInfo().getDiscount() != null) {
                promotionPrice = promotionPrice
                        .multiply(new BigDecimal(productItem.getPromotionSkuInfo().getDiscount()))
                        .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            } else {
                promotionPrice = productItem.getPromotionSkuInfo().getActivityPrice();
            }
            orderItem.setActualPrice(promotionPrice);

        } else {
            orderItem.setActualPrice(productItem.getPlatformPrice());
        }
        orderItem.setCostPrice(productItem.getCostPrice());
        orderItem.setFaceAmount(productItem.getFaceAmount());

        // 数量信息
        orderItem.setQuantity(quantity);

        // 手续费信息（单个商品的手续费）
        BigDecimal singleServiceFee = orderAmountResult.getSingleGoodsServiceFee();
        orderItem.setServiceFee(singleServiceFee);
        orderItem.setServiceFeeRate(orderAmountResult.getServiceFeeRate());

        // 外部商品信息
        orderItem.setOutGoodsId(productItem.getOutProductId());
        orderItem.setSourceTable(productItem.getSourceTable());

        // 应用标记（针对应用类商品）
        orderItem.setAppFlag(productItem.getAppFlag());

        if (!CollectionUtils.isEmpty(productItem.getPackageSubSkus())) {
            // 套餐类型 构建子商品订单信息
            String rechargeAccount = StringUtils.hasText(order.getRechargeAccount())
                    ? order.getCustomerMobile() : order.getRechargeAccount();
            List<SubSkuOrderEntity> subSkuOrders = buildSubSkuOrders(orderItem, productItem, rechargeAccount);
            orderItem.setSubSkuOrders(subSkuOrders);
        }

        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        orderItem.setCreateTime(now);
        orderItem.setModTime(now);

        return orderItem;
    }

    /**
     * 构建套餐子SKU订单信息
     */
    private List<SubSkuOrderEntity> buildSubSkuOrders(OrderItemEntity orderItem, ProductItem productItem,String rechargeAccount) {
        try {
            // 解析套餐子SKU信息
            List<PackageSubSkuEntity> subSkus = productItem.getPackageSubSkus();
            if (subSkus == null || subSkus.isEmpty()) {
                log.warn("套餐产品子SKU信息为空，spuId: {}, skuId: {}", productItem.getSpuId(), productItem.getSkuId());
                return Collections.emptyList();
            }

            List<SubSkuOrderEntity> subSkuOrders = new ArrayList<>();
            
            for (PackageSubSkuEntity subSku : subSkus) {
                // 每个子SKU根据套餐购买数量创建对应数量的子订单
                int subSkuTotalQuantity = orderItem.getQuantity() * subSku.getPackageCouponNum();
                
                SubSkuOrderEntity subSkuOrder = new SubSkuOrderEntity();
                subSkuOrder.setId(businessIdGenerator.generateId());
                
                // 订单关联信息
                subSkuOrder.setOrderNo(orderItem.getOrderNo());
                subSkuOrder.setSubOrderNo(generateSubOrderNo(orderItem.getOrderNo(), subSku.getId()));
                subSkuOrder.setCustomerId(orderItem.getCustomerId());
                subSkuOrder.setOrderItemId(orderItem.getId());
                subSkuOrder.setRechargeAccount(rechargeAccount);
                
                // 产品信息
                subSkuOrder.setProductType(productItem.getProductType());
                subSkuOrder.setSpuId(productItem.getSpuId());
                subSkuOrder.setSkuId(productItem.getSkuId());
                subSkuOrder.setSubSkuId(subSku.getId());
                subSkuOrder.setOuterId(subSku.getOuterId());
                subSkuOrder.setSubSkuImg(subSku.getImgUrl());
                
                // 面值和价格信息
                subSkuOrder.setFaceName(subSku.getAmountName());
                subSkuOrder.setFaceAmount(subSku.getAmount());
                subSkuOrder.setCostPrice(subSku.getCostPrice());
                
                // 数量信息
                subSkuOrder.setQuantity(subSkuTotalQuantity);
                
                // 卡券类型信息
                subSkuOrder.setCouponType(subSku.getCouponType());
                subSkuOrder.setOutCheckType(subSku.getType());
                subSkuOrder.setCheckType(subSku.getFlqType());
                
                // 发货状态
                subSkuOrder.setSubSkuDeliveryStatus(0); // 0-待发货
                
                // 时间信息
                LocalDateTime now = LocalDateTime.now();
                subSkuOrder.setCreateTime(now);
                subSkuOrder.setModTime(now);
                subSkuOrder.setDelFlag(0);
                
                subSkuOrders.add(subSkuOrder);
            }
            
            log.info("构建套餐子SKU订单完成，订单号：{}，子订单数量：{}", orderItem.getOrderNo(), subSkuOrders.size());
            return subSkuOrders;
            
        } catch (Exception e) {
            log.error("构建套餐子SKU订单失败，订单号：{} 套餐skuId{}", orderItem.getOrderNo(), productItem.getSkuId(), e);
            throw new BizException("构建套餐子订单失败");
        }
    }
    
    /**
     * 生成子订单号
     */
    private String generateSubOrderNo(String parentOrderNo, Long subSkuId) {
        return parentOrderNo + "_" + subSkuId;
    }


    private  boolean isLocalProfile() {
        String[] activeProfiles = env.getActiveProfiles();
        for (String activeProfile : activeProfiles) {
            if (activeProfile.equals("local")) {
                log.debug("active profile is {} then use default region",activeProfile);
                return true;
            }
        }
        return false;
    }

    /**
     * 订单提交上下文
     */
    @Getter
    private static class OrderSubmitContext {
        private final ProductItem productItem;
        private final OrderAmountResult orderAmountResult;
        private final List<GiftTicketInfo> giftTickets;

        public OrderSubmitContext(ProductItem productItem, OrderAmountResult orderAmountResult, List<GiftTicketInfo> giftTickets) {
            this.productItem = productItem;
            this.orderAmountResult = orderAmountResult;
            this.giftTickets = giftTickets;
        }

    }

    @Override
    public PageDTO<OrderListResponseDTO> pageOrderList(Long customerId, OrderListRequestDTO request) {
        log.info("开始查询用户订单列表，客户ID：{}，订单状态：{}，时间范围：{} - {}，一级分类ID：{}",
                 customerId, request.getOrderStatus(), request.getCreateTimeStart(), 
                 request.getCreateTimeEnd(), request.getFirstCategoryId());
        
        // 时间字符串转换
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        
        if (StringUtils.hasText(request.getCreateTimeStart())) {
            try {
                LocalDateTime startDate = LocalDateTimeUtil.parse(request.getCreateTimeStart(), "yyyy-MM-dd");
                startTime = LocalDateTimeUtil.beginOfDay(startDate);
            } catch (Exception e) {
                log.warn("创建时间开始格式错误：{}", request.getCreateTimeStart());
                throw new BizException("创建时间开始格式错误，请使用 yyyy-MM-dd格式");
            }
        }
        
        if (StringUtils.hasText(request.getCreateTimeEnd())) {
            try {
                LocalDateTime endDate = LocalDateTimeUtil.parse(request.getCreateTimeEnd(), "yyyy-MM-dd");
                endTime = LocalDateTimeUtil.endOfDay(endDate);
            } catch (Exception e) {
                log.warn("创建时间结束格式错误：{}", request.getCreateTimeEnd());
                throw new BizException("创建时间结束格式错误，请使用 yyyy-MM-dd格式");
            }
        }
        String orderNo = null;
        String productName = null;
        if (NumberUtil.isNumber(request.getKeyword())) {
            orderNo = request.getKeyword();
        } else if (StringUtils.hasText(request.getKeyword())) {
            productName = request.getKeyword();
        }
        
        // 构建查询条件
        OrderListQuery query = OrderListQuery.builder()
                .customerId(customerId)
                .orderStatus(request.getOrderStatus() < 0 ? null : request.getOrderStatus())
                .createTimeStart(startTime)
                .createTimeEnd(endTime)
                .firstCategoryId(request.getFirstCategoryId())
                .orderNo(orderNo)
                .productName(productName)
                .isShow( true)
                .pageNum(request.getToPage())
                .pageSize(request.getPageRows())
                .build();
        
        // 查询订单列表
        PageDTO<OrderInfoEntity> orderPage = orderRepository.findOrderListByPage(query);
        
        // 转换为响应DTO
        List<OrderListResponseDTO> orderList = orderPage.getRecords().stream()
                .map(this::convertToOrderListResponse)
                .collect(Collectors.toList());
        
        return PageDTO.<OrderListResponseDTO>builder()
                .records(orderList)
                .total(orderPage.getTotal())
                .current(orderPage.getCurrent())
                .size(orderPage.getSize())
                .pages(orderPage.getPages())
                .build();
    }

    /**
     * 转换订单实体为列表响应DTO
     */
    private OrderListResponseDTO convertToOrderListResponse(OrderInfoEntity order) {
        OrderListResponseDTO response = new OrderListResponseDTO();
        
        // 基本订单信息
        response.setOrderNo(order.getOrderNo());
        response.setPaymentAmount(order.getPaymentAmount());
        response.setRechargeAccount(order.getRechargeAccount());
        response.setOrderStatus(order.getOrderStatus());
        response.setDeliveryStatus(order.getDeliveryStatus());
        response.setAfterSaleStatus(order.getAfterSaleStatus());
        response.setCreateTime(order.getCreateTime());
        response.setPaymentExpireTime(order.getPayExpireTimestamp());
        response.setOrderDetailUrl(order.getOrderDetailUrl());
        
        // 商品信息（取第一个商品项）
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            OrderItemEntity orderItem = order.getOrderItems().get(0);
            response.setBrandId(orderItem.getBrandId());
            response.setBrandName(orderItem.getBrandName());
            response.setProductName(orderItem.getProductName());
            response.setProductSpuId(orderItem.getProductId());
            response.setProductSkuId(orderItem.getProductSkuId());
            response.setProductLogo(orderItem.getProductLogo());
            response.setSellPrice(orderItem.getSellPrice());
            response.setFaceValue(orderItem.getFaceAmount());
            response.setQuantity(orderItem.getQuantity());
            response.setProductType(orderItem.getProductType());
            response.setFlatProductType(orderItem.getFlatProductType());
        }
        
        return response;
    }

    @Override
    public OrderDetailResponseDTO getOrderDetail(Long customerId, String orderNo) {
        log.info("开始查询订单详情，客户ID：{}，订单号：{}", customerId, orderNo);
        
        // 查询订单信息
        OrderInfoEntity order = orderRepository.findByOrderNo(orderNo);
        if (order == null) {
            log.warn("订单不存在，订单号：{}", orderNo);
            throw new BizException("订单不存在");
        }
        
        // 验证订单归属
        if (!order.getCustomerId().equals(customerId)) {
            log.warn("订单不属于当前用户，客户ID：{}，订单客户ID：{}", customerId, order.getCustomerId());
            throw new BizException("无权限查看该订单");
        }
        OrderItemEntity orderItem = order.getOrderItems().get(0);
        if (Objects.equals(orderItem.getProductType(), ProductTypeEnum.COUPON.getType())
                || Objects.equals(orderItem.getProductType(), ProductTypeEnum.PACKAGE.getType())) {
            //卡券 套餐类 查询赠券情况
            String giftTicketsKey = RedisKeyConstants.ORDER_GIFT_TICKET_KEY + order.getOrderNo();
            if (redisUtil.exists(giftTicketsKey)) {
                String s = redisUtil.get(giftTicketsKey);
                if (StringUtils.hasText(s)) {
                    List<GiftTicketInfo> giftTickets = JSON.parseArray(s, GiftTicketInfo.class);
                    order.setGiftTickets(giftTickets);
                }
            }
        }
        
        // 转换为响应DTO
        OrderDetailResponseDTO result = convertToOrderDetailResponse(order);
        
        log.info("订单详情查询完成，订单号：{}，订单项数量：{}", orderNo, 
                 result.getOrderItems() != null ? result.getOrderItems().size() : 0);
        
        return result;
    }

    /**
     * 转换为订单详情响应DTO
     */
    private OrderDetailResponseDTO convertToOrderDetailResponse(OrderInfoEntity order) {
        OrderDetailResponseDTO response = new OrderDetailResponseDTO();

        BeanUtils.copyProperties(order, response,"paymentDetails");
        // 转化支付详情
        if (!Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.UNPAID.getCode())
                && !Objects.isNull(order.getTradeNo())) {
            List<OrderDetailResponseDTO.OrderPaymentDTO> orderPaymentList = getPaymentCardInfo(order);
            response.setPaymentDetails(orderPaymentList);
        }
        
        // 转换订单项
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            List<OrderDetailResponseDTO.OrderItemDetailDTO> itemList = order.getOrderItems().stream()
                    .map(this::convertToOrderItemDetailDTO)
                    .collect(Collectors.toList());
            response.setOrderItems(itemList);
        }
        // 待赠券列表
        response.setGiftTickets(convert2GiftTicketsDTO(order));
        
        return response;
    }

    private  List<GiftTicketResponseDTO> convert2GiftTicketsDTO(OrderInfoEntity order) {
        if (!CollectionUtils.isEmpty(order.getGiftTickets())) {
            List<GiftTicketResponseDTO> giftTicketsDTO = order.getGiftTickets().stream()
                    .map(giftTicketInfo -> {
                        GiftTicketResponseDTO ticketResponseDTO = new GiftTicketResponseDTO();
                        ticketResponseDTO.setTicketId(giftTicketInfo.getTicketId());
                        ticketResponseDTO.setTicketName(giftTicketInfo.getTicketName());
                        ticketResponseDTO.setSpecPicUrl(giftTicketInfo.getSpecPicUrl());
                        ticketResponseDTO.setTicketNum(giftTicketInfo.getTicketNum());
                        return ticketResponseDTO;
                    })
                    .toList();
            return giftTicketsDTO;
        }
        return null;
    }

    private List<OrderDetailResponseDTO.OrderPaymentDTO> getPaymentCardInfo(OrderInfoEntity order) {
        List<OrderDetailResponseDTO.OrderPaymentDTO> orderPaymentList
                = new ArrayList<>();
        List<TradeOrderInfoEntity> tradeOrderInfoEntities = tradeOrderRepository
                .listTradeOrderInfoByTradeNoAndOrderNo(order.getTradeNo(), order.getOrderNo());
        if (!CollectionUtils.isEmpty(tradeOrderInfoEntities)) {
            tradeOrderInfoEntities.forEach(tradeOrderInfo -> {
                OrderDetailResponseDTO.OrderPaymentDTO orderPaymentDTO = new OrderDetailResponseDTO.OrderPaymentDTO();
                orderPaymentDTO.setPaymentMethod(tradeOrderInfo.getCardTradeType());
                orderPaymentDTO.setAccountNo(tradeOrderInfo.getCardNo());
                orderPaymentDTO.setAmount(tradeOrderInfo.getPayAmount());
                orderPaymentList.add(orderPaymentDTO);
            });
        }
        return orderPaymentList;
    }

    /**
     * 转换订单项为详情DTO
     */
    private OrderDetailResponseDTO.OrderItemDetailDTO convertToOrderItemDetailDTO(OrderItemEntity item) {
        OrderDetailResponseDTO.OrderItemDetailDTO dto = new OrderDetailResponseDTO.OrderItemDetailDTO();
        
        // 直接复制所有属性
        BeanUtils.copyProperties(item, dto);
        
        // 如果有子SKU订单，转换为套餐子产品信息
        if (item.getSubSkuOrders() != null && !item.getSubSkuOrders().isEmpty()) {
            List<OrderDetailResponseDTO.PackageSubProductInfo> subProductList = item.getSubSkuOrders().stream()
                    .map(subSkuOrder -> {
                        OrderDetailResponseDTO.PackageSubProductInfo subProduct = new OrderDetailResponseDTO.PackageSubProductInfo();
                        subProduct.setAmountName(subSkuOrder.getFaceName());
                        subProduct.setAmount(subSkuOrder.getFaceAmount());
                        subProduct.setPackageCouponNum(subSkuOrder.getQuantity()/item.getQuantity());
                        // 如果有子SKU图片，可以设置
                        subProduct.setPackageCouponImg(subSkuOrder.getSubSkuImg());
                        return subProduct;
                    })
                    .toList();
            dto.setSubProductList(subProductList);
        }
        
        return dto;
    }

    /**
     * 增加当月该商品的购买金额
     */
    private void addProductMonthlyAmount(OrderInfoEntity orderInfo) {
        updateMonthlyAmount(orderInfo,"ADD");
    }

    /**
     * 增加当月该商品的购买数量
     */
    private void addProductMonthlyQuantity(OrderInfoEntity orderInfo) {
        updateMonthlyQuantity(orderInfo,"ADD");
    }

    /**
     * 减少当月该商品的购买金额
     */
    private void reduceProductMonthlyAmount(OrderInfoEntity orderInfo) {
        updateMonthlyAmount(orderInfo,"SUBTRACT");
    }

    /**
     * 减少当月该商品的购买数量
     */
    private void reduceProductMonthlyQuantity(OrderInfoEntity orderInfo) {
        updateMonthlyQuantity(orderInfo,"SUBTRACT");
    }



    private void updateMonthlyAmount(OrderInfoEntity orderInfo, String operation) {
        ProductItemId productItemId = ProductItemId.of(
                orderInfo.getProductSpuId(),
                orderInfo.getProductSkuId(),
                orderInfo.getFlatProductType()/100);
        calculateAmountService.updateMonthlyPurchaseAmount(
                orderInfo.getCustomerId(),
                productItemId,
                orderInfo.getTotalSellAmount(),
                operation);
    }



    private void updateMonthlyQuantity(OrderInfoEntity orderInfo, String operation) {
        ProductItemId productItemId = ProductItemId.of(
                orderInfo.getProductSpuId(),
                orderInfo.getProductSkuId(),
                orderInfo.getFlatProductType() / 100);

        // 计算购买数量：从订单项中获取实际购买数量
        Integer quantity = calculateQuantityFromOrder(orderInfo);

        calculateAmountService.updateMonthlyPurchaseQuantity(
                orderInfo.getCustomerId(),
                productItemId,
                quantity,
                operation);
    }

    /**
     * 从订单信息中计算购买数量
     */
    private Integer calculateQuantityFromOrder(OrderInfoEntity orderInfo) {
        // 从订单项中获取购买数量
        if (orderInfo.getOrderItems() != null && !orderInfo.getOrderItems().isEmpty()) {
            // 当前设计是一品一单，直接取第一个订单项的数量
            return orderInfo.getOrderItems().get(0).getQuantity();
        }

        // 如果没有订单项信息，使用默认值1
        log.warn("订单项信息为空，使用默认购买数量1，订单号：{}", orderInfo.getOrderNo());
        return 1;
    }

    @Override
    public void markOrderAsHidden(String orderNo, Long customerId) {
        log.info("开始删除订单，订单号：{}，客户ID：{}", orderNo, customerId);
        
        // 1. 查询订单信息
        OrderInfoEntity orderInfo = orderRepository.findByOrderNo(orderNo);
        if (orderInfo == null) {
            throw new BizException("订单不存在");
        }
        
        // 2. 校验订单归属
        if (!Objects.equals(orderInfo.getCustomerId(), customerId)) {
            throw new BizException("订单不属于当前用户");
        }
        
        // 3. 校验订单状态是否可以删除
        validateOrderCanDelete(orderInfo);
        
        // 4. 设置订单为不显示状态
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(orderInfo.getId());
        updateOrder.setCustomerId(orderInfo.getCustomerId());
        updateOrder.setHidden(); // 设置为不显示
        updateOrder.setModTime(LocalDateTime.now());
        
        orderRepository.updateOrder(updateOrder);
        
        log.info("订单删除成功，订单号：{}，客户ID：{}", orderNo, customerId);
    }

    /**
     * 校验订单是否可以删除
     */
    private void validateOrderCanDelete(OrderInfoEntity orderInfo) {
        
        // 已经隐藏的订单不能重复删除
        if (orderInfo.isHidden()) {
            throw new BizException("订单已删除");
        }
        if (!orderInfo.isCanHidden()) {
            throw new BizException("订单状态不允许删除");
        }
    }

}
