package com.jsrxjt.mobile.domain.riskcontrol.service;

import com.jsrxjt.mobile.api.riskcontrol.dto.request.QueryProductRiskControlRequest;
import com.jsrxjt.mobile.api.riskcontrol.dto.response.ProductRiskControlResponse;

/**
 * <AUTHOR>
 * @Date 2025/6/25 15:12
 */
public interface ProductRiskControlService {

    /**
     * 用户下单查询风控策略
     * @param request
     * @return
     */
    ProductRiskControlResponse getProductRiskControl(QueryProductRiskControlRequest request);
}
