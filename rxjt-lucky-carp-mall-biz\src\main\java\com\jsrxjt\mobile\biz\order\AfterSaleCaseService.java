package com.jsrxjt.mobile.biz.order;

import com.jsrxjt.mobile.api.order.dto.response.AfterSaleDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.request.AfterSaleListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.AfterSaleListResponseDTO;
import com.jsrxjt.mobile.api.common.PageDTO;

/**
 * 售后业务服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface AfterSaleCaseService {
    
    /**
     * 获取售后详情
     * 
     * @param afterSaleNo 售后单号
     * @param customerId 客户ID
     * @return 售后详情
     */
    AfterSaleDetailResponseDTO getAfterSaleDetail(String afterSaleNo, Long customerId);
    
    /**
     * 分页查询售后单列表
     * 
     * @param customerId 客户ID
     * @param request 查询请求
     * @return 分页结果
     */
    PageDTO<AfterSaleListResponseDTO> pageAfterSaleList(Long customerId, AfterSaleListRequestDTO request);
}
