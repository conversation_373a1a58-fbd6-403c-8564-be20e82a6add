package com.jsrxjt.mobile.domain.order.query;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 售后单列表查询条件
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Builder
public class AfterSaleListQuery {
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 售后状态
     */
    private Integer afterSaleStatus;
    
    /**
     * 售后类型
     */
    private Integer afterSaleType;
    
    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
}