package com.jsrxjt.mobile.infra.ticket.persistent.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 优惠券与区域关联表
 * @Author: ywt
 * @Date: 2025-08-18 13:59
 * @Version: 1.0
 */
@Data
@TableName("ticket_region_relation")
public class TicketRegionPO {
    private Long id;
    private Long ticketId;//优惠券ID
    private Integer regionId; // 区域id,全国传0
    /**
     * 区域名称
     */
    private String regionName;
    /**
     * 区域类型,1 省  2  市  3 区
     */
    private Integer regionType;
    /**
     * 父类id，用于聚合前端展示
     */
    private Integer parentRegionId;
    private String parentRegionName;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime; // 创建时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modTime; // 修改时间
    private Byte delFlag; // 删除标志 0:否 1:删除
    private Long modId; // 修改人id
    private Long createId; // 创建人id
}
