package com.jsrxjt.mobile.api.distribution.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "展示付应用区域请求")
@Data
public class PickChannelAppRequestDTO {
    @Schema(description = "区域id 手动选择区域时要传")
    private Integer regionId;
    @Schema(description = "应用appId，若不传默认选中第一个")
    private Long appId;
}
