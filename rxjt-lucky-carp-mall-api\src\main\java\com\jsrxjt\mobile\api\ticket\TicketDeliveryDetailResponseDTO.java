package com.jsrxjt.mobile.api.ticket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 优惠券发放体类
 */
@Data
public class TicketDeliveryDetailResponseDTO {
    
    /**
     * 优惠券订单编号
     */
    @Schema(description = "优惠券订单编号")
    private Long id;
    
    /**
     * 关联的订单id
     */
    @Schema(description = "关联的订单")
    private Long ticketOrderNo;
    
    /**
     * 关联订单编号
     */
    @Schema(description = "关联订单编号")
    private String orderNo;
    
    /**
     * 外部订单编号
     */
    @Schema(description = "外部订单编号")
    private String externalOrderNo;
    
    /**
     * 优惠券类型：0商家自发优惠券 2瑞祥代发优惠券
     */
    @Schema(description = "优惠券类型：0商家自发优惠券 2瑞祥代发优惠券")
    private Byte ticketType;
    
    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;
    
    /**
     * 核销状态: 1未核销 2已核销，仅瑞祥代发优惠券有效
     */
    @Schema(description = "核销状态: 1未核销 2已核销，仅瑞祥代发优惠券有效")
    private Byte status;
    
    /**
     * 卡营coupon_id
     */
    @Schema(description = "卡营coupon_id")
    private String centerCouponId;
    
    /**
     * 卡券卡号
     */
    @Schema(description = "卡券卡号")
    private String ticketCode;
    
    /**
     * 卡券卡密
     */
    @Schema(description = "卡券卡密")
    private String ticketPin;
    
    /**
     * 优惠券id
     */
    @Schema(description = "优惠券id")
    private Long ticketId;
    
    /**
     * 优惠券名称
     */
    @Schema(description = "优惠券名称")
    private String ticketName;
    
    /**
     * 券的有效期
     */
    @Schema(description = "券的有效期")
    private Date ticketValidDate;
    
    /**
     * 核销页样式 0卡号 1转码
     */
    @Schema(description = "核销页样式 0卡号 1转码")
    private Byte offsetPageType;
    
    /**
     * 品牌名
     */
    @Schema(description = "品牌名")
    private String brandName;
    
    /**
     * 核销logo
     */
    @Schema(description = "核销logo")
    private String offsetLogo;
    
    /**
     * 使用说明
     */
    @Schema(description = "使用说明")
    private String useManual;

}
