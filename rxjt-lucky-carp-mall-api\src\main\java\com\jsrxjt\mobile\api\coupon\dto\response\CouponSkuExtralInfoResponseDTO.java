package com.jsrxjt.mobile.api.coupon.dto.response;

import com.jsrxjt.mobile.api.promotion.dto.PromotionSkuInfo;
import com.jsrxjt.mobile.api.ticket.BrandTicketResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketResponseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: SKU其他信息响应
 * @Author: ywt
 * @Date: 2025-07-15 10:58
 * @Version: 1.0
 */
@Data
public class CouponSkuExtralInfoResponseDTO {
    @Schema(description = "卡券skuId")
    private Long couponSkuId;
    @Schema(description = "卡券spuid")
    private Long couponSpuId;//卡券spuid(coupon_goods主键)
    @Schema(description = "sku的促销活动信息，为空表示没有活动")
    private PromotionSkuInfo promotionInfo;
    @Schema(description = "优惠券")
    private List<BrandTicketResponseDTO> brandTicketList;
}
