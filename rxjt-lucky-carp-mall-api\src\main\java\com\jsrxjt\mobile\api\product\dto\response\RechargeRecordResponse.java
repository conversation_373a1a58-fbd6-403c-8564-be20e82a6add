package com.jsrxjt.mobile.api.product.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 产品充值记录响应
 * @Author: ywt
 * @Date: 2025-08-08 15:54
 * @Version: 1.0
 */
@Data
public class RechargeRecordResponse {
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "spuId")
    private Long productSpuId;

    @Schema(description = "skuId")
    private Long productSkuId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "logo图片地址")
    private String productLogo;

    @Schema(description = "充值账户")
    private String rechargeAccount;

    @Schema(description = "订单状态")
    private int orderStatus;

    @Schema(description = "充值状态")
    private int deliveryStatus;

    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    @Schema(description = "下单时间")
    private LocalDateTime createTime;
}
