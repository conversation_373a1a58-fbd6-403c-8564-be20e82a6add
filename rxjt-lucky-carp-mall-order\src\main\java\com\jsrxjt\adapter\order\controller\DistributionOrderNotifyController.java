package com.jsrxjt.adapter.order.controller;

import com.jsrxjt.common.core.vo.ApiResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionBaseCreateOrderDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionOrderStatusNotifyDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.biz.distribution.service.DistributionOrderService;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.CommonDistributionOrderInfoBuilder;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:38
 * 分销应用下单通知接口
 */
@RestController
@RequestMapping("/v1/distribution-order-notify")
@RequiredArgsConstructor
public class DistributionOrderNotifyController {

    private final DistributionOrderService distributionOrderService;

    private final CommonDistributionOrderInfoBuilder commonDistributionOrderInfoBuilder;

    private final OrderRepository orderRepository;

    /**
     * 通用分销应用创建订单
     *
     * @param request 创建订单请求参数
     * @return
     */

    @PostMapping("/common/order/created")
    public ApiResponse<DistributionCreateOrderResponseDTO> commonDistributionOrderCreatedNotify(@RequestBody @Valid DistributionBaseCreateOrderDTO request) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setCustomerId(Long.valueOf(request.getUserId()));
        createOrderDTO.setExternalOrderNo(request.getOrderNo());
        createOrderDTO.setTradeNo(request.getTradeNo());
        // 兼容金额字段不同场景
        BigDecimal externalOrderAmount = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(request.getTotalAmount())) {
            externalOrderAmount = BigDecimal.valueOf(Double.valueOf(request.getTotalAmount()));
        }
        if (StringUtils.isNotBlank(request.getTradeAmount())) {
            externalOrderAmount = BigDecimal.valueOf(Double.valueOf(request.getTradeAmount()));
        }
        createOrderDTO.setExternalAppProductPrice(externalOrderAmount);
        // 兼容不同场景订单支付超时时间
        String orderTime = request.getOrderTime();
        String orderExpire = request.getOrderExpire();
        if (StringUtils.isNotBlank(orderTime) && StringUtils.isNotBlank(orderExpire)) {
            Instant originalInstant = Instant.ofEpochSecond(Long.valueOf(orderTime));
            // 增加分钟
            Instant newInstant = originalInstant.plus(Duration.ofMinutes(Long.valueOf(orderExpire)));
            // 获取新的时间戳
            long externalOrderExpireTime = newInstant.getEpochSecond();
            createOrderDTO.setExternalOrderExpireTime(String.valueOf(externalOrderExpireTime));

        }
        if (StringUtils.isNotBlank(request.getExpireTime())) {
            createOrderDTO.setExternalOrderExpireTime(request.getExpireTime());
        }
        // 兼容订单支付完成跳转页面
        String externalPayResultUrl = request.getResultPageUrl();
        if (StringUtils.isNotBlank(externalPayResultUrl)) {
            externalPayResultUrl = request.getResultUrl();
        }
        if (StringUtils.isNotBlank(externalPayResultUrl)) {
            externalPayResultUrl = request.getDetailUrl();
        }
        if (StringUtils.isNotBlank(externalPayResultUrl)) {
            externalPayResultUrl = request.getReturnUrl();
        }
        createOrderDTO.setExternalPayResultUrl(externalPayResultUrl);

        DistributionCreateOrderResponseDTO responseDTO = distributionOrderService.distributionCreateOrder(createOrderDTO, commonDistributionOrderInfoBuilder);
        responseDTO.setCashierUrl("");
        return ApiResponse.success(responseDTO);
    }

    /**
     * 通用分销应用订单状态变更 (叮咚买菜,大润发,食行生鲜,卫岗,清美)
     *
     * @param request
     * @return
     */

    @PostMapping("/common/order/status-change")
    public ApiResponse<Object> commonDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("0", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    /**
     * 视听分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/stfx/order/status-change")
    public ApiResponse<Object> stfxDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getChangeStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("5", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    /**
     * 水韵分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/syfx/order/status-change")
    public ApiResponse<Object> syfxDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getChangeStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("1", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    /**
     * 同程分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/tcfx/order/status-change")
    public ApiResponse<Object> tcfxDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("3", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    /**
     * 团油分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/tyfx/order/status-change")
    public ApiResponse<Object> tyfxDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getChangeStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("1", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    /**
     * 西橙分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/mvfx/order/status-change")
    public ApiResponse<Object> mvfxDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getChangeStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("1", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    /**
     * 团油分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/yhfx/order/status-change")
    public ApiResponse<Object> yhfxDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
            if (Objects.equals("5", orderStatus)) {
                OrderInfoEntity orderInfo = new OrderInfoEntity();
                orderInfo.setExternalOrderNo(orderNo);
                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
                orderRepository.updateOrderByExternalOrderNo(orderInfo);
            }
        }
        return ApiResponse.success();
    }

    /**
     * 团油分销应用订单状态变更
     *
     * @param request
     * @return
     */
    @PostMapping("/zmrsfx/order/status-change")
    public ApiResponse<Object> zmrsfxDistributionOrderStatusNotify(@RequestBody @Valid DistributionOrderStatusNotifyDTO request) {
        String orderNo = request.getOrderNo();
        String orderStatus = request.getOrderStatus();
//        if (StringUtils.isNotBlank(orderStatus) && StringUtils.isNotBlank(orderNo)) {
//            if (Objects.equals("1", orderStatus)) {
//                OrderInfoEntity orderInfo = new OrderInfoEntity();
//                orderInfo.setOrderNo(orderNo);
//                orderInfo.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
//                orderRepository.updateOrderByOrderNo(orderInfo);
//            }
//        }
        return ApiResponse.success();
    }
}
