package com.jsrxjt.mobile.domain.ticket.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.ticket.entity.TicketBrandEntity;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;

/**
 * @Description: 优惠券发放
 */
public interface TicketDeliveryRepository {
    /**
     * 商家优惠券
     * 获取用户优惠券发放列表
     * @param userId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageDTO<TicketDeliveryEntity> getTicketDeliveryByUserId(Long userId, Long pageNum, Long pageSize);

    /**
     * 获取优惠券详情
     * @param id
     * @return
     */
    TicketDeliveryEntity getTicketDeliveryById(Long id);

    /**
     * 删除优惠券发放
     * @param id
     */
    void delTicketDelivery(Long id);

}
