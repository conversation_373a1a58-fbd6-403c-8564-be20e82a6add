package com.jsrxjt.mobile.domain.packages.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 套餐sku表
 *
 */
@Data
public class PackageSkuSubJobEntity {
    private Long id;
    private Long packageSpuId;
    private Long brandId;
    private Long packageSkuId;
    private Long outerId;
    private Byte couponType;//卡券类型 1卡券 3品诺
    private String amountName;//卡管面值名称
    private BigDecimal amount;//面值
    private BigDecimal price;//卡管的售价
    private BigDecimal costPrice;//卡管的成本价
    private Integer soldNum;//已售数量
    private Byte subSkuStatus;//子sku状态 0:下架 1:出售中
    private Integer inventory;//卡券库存
    private Byte  isMoreOffset;//是否多次核销 0否 1是
    private Integer packageCouponNum;//该套餐中的子sku数量
    private Integer onSaleNum;//起售数量
    private Integer rationSaleNum;//单次限售数量
    private String backgroundColor;//核销页背景色
    private String scenarios; //使用说明
    // private Date couponValidTime;//卡券有效期时间
    private Byte centerStatus;//卡管平台状态 0:下架 1:出售中
    @Schema(description = "充值类型")
    private Byte accountType;
    @Schema(description = "核销类型")
    private Integer type;
    @Schema(description = "品诺类型 直充类商品2卡券类商品")
    private Integer pnType;
    @Schema(description = "自定义核销类型")
    private Integer flqType;
}
