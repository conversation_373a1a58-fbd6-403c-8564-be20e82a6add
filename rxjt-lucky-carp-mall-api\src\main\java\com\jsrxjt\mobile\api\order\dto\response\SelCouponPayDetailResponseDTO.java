package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:购券明细
 **/
@Data
public class SelCouponPayDetailResponseDTO {

    //支付明细订单号
    @Schema(description = "支付明细订单号")
    private String payOrderSn;
    //退款订单号
    @Schema(description = "退款订单号")
    private String refundOrderSn;
    //消费门店ID
    @Schema(description = "消费门店ID")
    private String shopId;
    //消费门店
    @Schema(description = "消费门店")
    private String shopName;
    //消费金额（正数为退款，负数为消费）
    @Schema(description = "消费金额（正数为退款，负数为消费）")
    private String price;
    //交易时间
    @Schema(description = "交易时间")
    private String createdAt;

}
