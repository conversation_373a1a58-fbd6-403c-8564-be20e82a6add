<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jsrxjt</groupId>
        <artifactId>rxjt-lucky-carp-mall-common</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>rxjt-lucky-carp-mall-common-adapter</artifactId>
    <name>rxjt-lucky-carp-mall-common-adapter</name>
    <packaging>jar</packaging>
    <description>rxjt-lucky-carp-mall 适配层工具</description>
    <properties>
        <java.version>17</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.jsrxjt</groupId>
            <artifactId>rxjt-lucky-carp-mall-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
    </dependencies>

</project>
