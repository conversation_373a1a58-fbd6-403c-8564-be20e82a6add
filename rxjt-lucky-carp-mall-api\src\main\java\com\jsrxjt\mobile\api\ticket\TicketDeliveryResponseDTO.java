package com.jsrxjt.mobile.api.ticket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 我的呢商家优惠券
 */
@Data
public class TicketDeliveryResponseDTO {
    
    /**
     * 优惠券订单编号
     */
    @Schema(description = "优惠券订单编号")
    private Long id;

    /**
     * 优惠券类型：0商家自发优惠券 2瑞祥代发优惠券
     */
    @Schema(description = "优惠券类型：0商家自发优惠券 2瑞祥代发优惠券")
    private Byte ticketType;

    /**
     * 核销状态: 1未核销 2已核销，仅瑞祥代发优惠券有效
     */
    @Schema(description = "核销状态: 1未核销 2已核销，仅瑞祥代发优惠券有效")
    private Byte status;

    /**
     * 优惠券名称
     */
    @Schema(description = "优惠券名称")
    private String ticketName;
    
    /**
     * 券的有效期
     */
    @Schema(description = "券的有效期")
    private Date ticketValidDate;

    /**
     * 品牌名
     */
    @Schema(description = "品牌名")
    private String brandName;

}
