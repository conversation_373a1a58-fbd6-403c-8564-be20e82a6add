package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.biz.homeScanPay.service.ScanPayWssService;
import com.jsrxjt.mobile.biz.mq.QueueSenderService;
import com.jsrxjt.mobile.biz.mq.constants.DestinationConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;


@RequiredArgsConstructor
@Slf4j
@Service
public class ScanPayWssServiceImpl implements ScanPayWssService {

    private final QueueSenderService queueSenderService;

    private final ScanWebSocketHandler scanWebSocketHandler;

    @Override
    public void sendMessageToCustomer(Long customerId, String payCode, String message) {
        if (customerId == null || StringUtils.isBlank(payCode) || StringUtils.isBlank(message)){
            log.error("参数错误");
            throw new BizException("参数错误");
        }
        JSONObject jsonObject = new JSONObject()
                .fluentPut("customerId", customerId)
                .fluentPut("payCode", payCode)
                .fluentPut("message", message);
        queueSenderService.send(DestinationConstants.DestinationName.SCAN_PAY_WSS_TOPIC,  jsonObject.toJSONString());
    }

    @Override
    public void handleScanPayWssMessage(Long customerId, String payCode, String message) {
        scanWebSocketHandler.sendMessageToCustomer(customerId, payCode, message);
    }
}
