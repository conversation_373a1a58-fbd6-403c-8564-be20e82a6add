package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.query.AfterSaleListQuery;

import java.util.List;

/**
 * 售后仓储接口
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface AfterSaleRepository {
    
    /**
     * 保存售后信息
     * 
     * @param afterSale 售后信息
     */
    void save(AfterSaleEntity afterSale);
    
    /**
     * 更新售后信息
     * 
     * @param afterSale 售后信息
     */
    void update(AfterSaleEntity afterSale);
    
    /**
     * 根据订单号查询有效的售后申请单
     * 包括：审核通过、退款中、退款成功状态的售后单
     * 
     * @param orderNo 订单号
     * @return 有效的售后申请单列表
     */
    List<AfterSaleEntity> findValidAfterSalesByOrderNo(String orderNo);
    
    /**
     * 根据售后单号和客户ID查询售后详情
     * 
     * @param afterSaleNo 售后单号
     * @param customerId 客户ID
     * @return 售后实体
     */
    AfterSaleEntity findByAfterSaleNoAndCustomerId(String afterSaleNo, Long customerId);
    
    /**
     * 分页查询售后单列表
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageDTO<AfterSaleEntity> findAfterSaleListByPage(AfterSaleListQuery query);

    /**
     * 统计待审核、审核通过状态的售后订单数量
     * @param customerId 客户id
     * @return 售后订单数量
     */
    int countAfterSaleByStatus(Long customerId);
}



