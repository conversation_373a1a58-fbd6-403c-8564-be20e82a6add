package com.jsrxjt.common.adapter.handler;

import cn.dev33.satoken.exception.NotLoginException;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.vo.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.Objects;

/**
 * 
 *  全局异常处理
 * <AUTHOR> Fengping
 * 2023/3/1 13:44
 * 
 **/
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 最大的兜底错误处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    public BaseResponse<Object> exception(Exception e) {
        log.error("system exception message:{}", e.getMessage());
        log.error("system exception:", e);
        return BaseResponse.fail("小羊开小差了，请稍后再试");
    }
    @ExceptionHandler(value = UndeclaredThrowableException.class)
    public BaseResponse<Object> exception(UndeclaredThrowableException e) {
        // 可能是被限流了
        log.error("UndeclaredThrowableException message:{}", e.getMessage());
        log.error("UndeclaredThrowableException:", e);
        return BaseResponse.fail(Status.LIMIT_ERROR);
    }

    /**
     * 登录异常处理
     * @param e
     * @return {@link BaseResponse}<{@link Object}>
     */
    @ExceptionHandler(value = NotLoginException.class)
    public BaseResponse<Object> exception(NotLoginException e) {
        log.error("NotLoginException message:{}", e.getMessage());
        log.error("NotLoginException:", e);
        return BaseResponse.fail(e.getCode(),  e.getMessage());
    }

    /**
     * 参数绑定错误
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = BindException.class)
    public BaseResponse<Object> exception(BindException e) {
        String defaultMessage = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        log.warn("param bind exception message:{}", defaultMessage);
        log.warn("param bind exception message", e);
        return BaseResponse.fail(Status.REQUEST_PARAM.getCode(),defaultMessage);
    }

    /**
     * 业务异常处理
     * @param e
     * @return
     */
    @ExceptionHandler(value = BizException.class)
    public BaseResponse<Object> bizException(BizException e) {
        log.warn("biz exception  code {} msg {}", e.getCode(),e.getMsg());
        log.warn("biz exception info:", e);
        return BaseResponse.fail(e.getCode(),e.getMsg());
    }

    @ExceptionHandler(value = DataIntegrityViolationException.class)
    public BaseResponse<Object> dataIntegrityViolationException(Exception e) {
        log.error("Exception dataIntegrityViolationException:{}", e.getMessage());
        log.error("dataIntegrityViolationException:", e);
        String message = e.getMessage();
        String[] split = message.split("\r\n###");
        for (String str : split) {
            if (str.trim().isBlank() || str.trim().contains("Error")){
                continue;
            }
            String[] split1 = str.split(":");
            if (split1.length > 0) {
                message = split1[split1.length - 1].trim();
            }
        }
        return BaseResponse.fail(Status.ERROR.getCode(),message);
    }
}
