package com.jsrxjt.mobile.infra.ticket.persistent.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.domain.ticket.repository.TicketProductRepository;
import com.jsrxjt.mobile.infra.ticket.persistent.mapper.TicketProductMapper;
import com.jsrxjt.mobile.infra.ticket.persistent.po.TicketProductPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 优惠券与卡券关联实现类
 * @Author: ywt
 * @Date: 2025-08-15 15:34
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
public class TicketProductRepositoryImpl implements TicketProductRepository {
    private final TicketProductMapper ticketCouponMapper;

    @Override
    public List<Long> getTicketsIdBySkuIdAndPrdType(Long productSkUID, Integer productType) {
        List<TicketProductPO> list = ticketCouponMapper.selectList(new LambdaQueryWrapper<TicketProductPO>()
                .eq(TicketProductPO::getProductSkuId, productSkUID)
                .eq(TicketProductPO::getProductType, productType)
                .eq(TicketProductPO::getStatus, 1));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(TicketProductPO::getTicketId).collect(Collectors.toList());
    }
}
