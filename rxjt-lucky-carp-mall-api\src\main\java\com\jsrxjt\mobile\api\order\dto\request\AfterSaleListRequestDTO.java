package com.jsrxjt.mobile.api.order.dto.request;

import com.jsrxjt.common.core.vo.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 售后单列表查询请求参数
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Setter
public class AfterSaleListRequestDTO extends BaseParam {
    
    @Schema(description = "售后状态：1-待审核 2-审核通过 3-审核拒绝 4-已取消，-1查全部")
    @NotNull(message = "售后状态不能为空")
    private Integer afterSaleStatus;

    @Schema(description = "售后类型：1-仅退款 2-退货退款")
    private Integer afterSaleType;
}
