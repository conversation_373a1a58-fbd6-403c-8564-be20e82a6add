package com.jsrxjt.mobile.api.product.types;

import lombok.Getter;

/**
 * 大的产品类型枚举
 * <AUTHOR>
 * @since 2025/3/10
 **/
@Getter
public enum ProductTypeEnum {
    /**
     * 卡券
     */
    COUPON(1, "卡券"),

    /**
     * 套餐
     */
    PACKAGE(2, "套餐"),

    APP(3,"应用"),
    /**
     * 类卡券的面值型应用
     */
    COUPON_APP(4,"支付宝/苏西话费");

    /**
     * 类型值
     */
    private final Integer type;

    /**
     * 类型描述
     */
    private final String desc;

    ProductTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 根据type获取枚举
     *
     * @param type 类型值
     * @return 对应的枚举值，如果没找到返回null
     */
    public static ProductTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (ProductTypeEnum productType : values()) {
            if (productType.getType().equals(type)) {
                return productType;
            }
        }
        return null;
    }

    /**
     * 判断是否是有效的商品类型
     *
     * @param type 类型值
     * @return true:有效 false:无效
     */
    public static boolean isValidType(Integer type) {
        return getByType(type) != null;
    }
}
