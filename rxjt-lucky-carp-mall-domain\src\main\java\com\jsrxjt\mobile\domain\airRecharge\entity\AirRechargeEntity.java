package com.jsrxjt.mobile.domain.airRecharge.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 空中充值实体类
 * @Author: ywt
 * @Date: 2025-08-26 15:17
 * @Version: 1.0
 */
@Data
public class AirRechargeEntity {
    private Integer id;
    private String mobile;
    private BigDecimal truePrice;//真实已经成功充值的金额
    private String cardNo;//充值卡号
    private Integer rechargeType;//1 黑金主卡 2 白金主卡 3 提货凭证
}
