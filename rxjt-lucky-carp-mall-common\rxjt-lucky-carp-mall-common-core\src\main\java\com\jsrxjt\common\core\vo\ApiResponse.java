package com.jsrxjt.common.core.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ApiResponse<T> implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    // 状态码
    private Integer code;
    // 消息
    private String message;
    // 数据
    private T data;

    // 构造方法
    public ApiResponse() {}
    
    public ApiResponse(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // 快速生成成功响应的静态方法
    public static <T> ApiResponse<T> success() {
        return success(null);
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<T>(200, "操作成功", data);
    }

    // 快速生成失败响应的静态方法
    public static <T> ApiResponse<T> fail(Integer code, String message) {
        return new ApiResponse<T>(code, message, null);
    }

    public static <T> ApiResponse<T> fail(String message) {
        return fail(500, message);
    }

}