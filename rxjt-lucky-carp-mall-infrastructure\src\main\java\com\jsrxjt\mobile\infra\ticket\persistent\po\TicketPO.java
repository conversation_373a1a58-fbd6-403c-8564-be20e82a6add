package com.jsrxjt.mobile.infra.ticket.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description:
 * @Author: ywt
 * @Date: 2025-08-15 15:25
 * @Version: 1.0
 */
@Data
@TableName("ticket")
public class TicketPO {
    @Schema(description = "id")
    @TableId(value = "ticket_id", type = IdType.AUTO)
    private Long ticketId;

    @Schema(description = "优惠券名称")
    @TableField("ticket_name")
    private String ticketName;

    @Schema(description = "优惠券类型0商家自发优惠券 1全球购线上商城优惠券 2瑞祥代发优惠券")
    @TableField("ticket_type")
    private Byte ticketType;

    @Schema(description = "优惠券品牌id")
    @TableField("brand_id")
    private Long brandId;

    @Schema(description = "卡管/商城coupon_id")
    @TableField("center_coupon_id")
    private String centerCouponId;

    @Schema(description = "优惠券分类 0满减券 1折扣券 2满赠券 3权益包 4无门槛券")
    @TableField("ticket_cat_code")
    private Byte ticketCatCode;

    @Schema(description = "自发券后有效期日")
    @TableField("ticket_valid_date")
    private Integer ticketValidDate;

    @Schema(description = "规格图片url")
    @TableField("spec_pic_url")
    private String specPicUrl;

    @Schema(description = "使用说明")
    @TableField("use_manual")
    private String useManual;

    @Schema(description = "核销页样式 0卡号 1转吗")
    @TableField("offset_page_type")
    private Byte offsetPageType;

    @Schema(description = "状态 0禁用 1启用")
    @TableField("status")
    private Byte status;

    @Schema(description = "上线城市：0非全国  1全国")
    @TableField("is_nationwide")
    private Integer isNationwide;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "创建人id")
    @TableField("create_id")
    private Long createId;

    @Schema(description = "编辑人id")
    @TableField("mod_id")
    private Long modId;

    @Schema(description = "编辑时间")
    @TableField("mod_time")
    private Date modTime;

    @Schema(description = "是否删除标志(0:否 1:是)")
    @TableField("del_flag")
    private Byte delFlag;

    @Schema(description = "删除人id")
    @TableField("del_id")
    private Long delId;

    @Schema(description = "删除时间")
    @TableField("del_time")
    private Date delTime;

    @Schema(description = "创建人姓名")
    @TableField("create_name ")
    private String createName;

    @Schema(description = "修改人姓名")
    @TableField("mod_name")
    private String modName;
}
