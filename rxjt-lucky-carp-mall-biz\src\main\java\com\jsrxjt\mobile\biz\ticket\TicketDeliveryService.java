package com.jsrxjt.mobile.biz.ticket;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.ticket.GlobalTicketResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryDetailResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryResponseDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryDetailRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryRequestDTO;
import com.jsrxjt.mobile.api.ticket.TicketShopListResponseDTO;

public interface TicketDeliveryService {
    PageDTO<TicketDeliveryResponseDTO> getTicketDelivery(TicketDeliveryRequestDTO  request);

    TicketDeliveryDetailResponseDTO getTicketDeliveryById(TicketDeliveryDetailRequestDTO request);

    void delTicketDelivery(TicketDeliveryDetailRequestDTO request);

    /**
     * 分页获取门店券列表
     * @param request 请求参数
     * @return 返回结果
     */
    PageDTO<TicketShopListResponseDTO> getShopTicketList(TicketDeliveryRequestDTO request);


    /**
     * 分页获取全球购优惠券列表
     * @param request 请求参数
     * @return 返回结果
     */
    PageDTO<GlobalTicketResponseDTO> getGlobalTicketList(TicketDeliveryRequestDTO request);

}
