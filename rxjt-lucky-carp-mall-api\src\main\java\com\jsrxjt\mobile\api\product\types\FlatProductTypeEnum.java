package com.jsrxjt.mobile.api.product.types;

/**
 * 扁平化产品类型枚举
 *      *  productType * 100 + subType
 *      * 1xx-卡券类型
 *      *  101 普通卡券 102 视频直充 103 品诺卡券 104 品诺直充
 *      * 2xx-套餐类型 201 套餐
 *      * 3xx-应用类型
 *      *  301 普通应用 304 扫码提货
 *      * 4xx-支付宝/苏西话费类型
 *      * 402 支付宝红包 403 苏西话费
 *      ...
 *      path 对应详情接口的path
 * <AUTHOR>
 * @since 2025/5/16
 **/
public enum FlatProductTypeEnum {
    // 卡券类型
    NORMAL_COUPON(101, "普通卡券", "mobile-web-coupon/v1/coupon/info"),
    VIDEO_DIRECT_RECHARGE(102, "视频直充", ""),
    PINO_COUPON(103, "品诺卡券", ""),
    PINO_DIRECT_RECHARGE(104, "品诺直充", ""),

    // 套餐类型 
    PACKAGE(201, "套餐", ""),

    // 应用类型
    NORMAL_APP(301, "普通应用", ""),
    //展码付
    SCAN_PICKUP_APP(304, "扫码提货", ""),
    //首页提货
    SCAN_OFFLINE_APP(306, "线下扫码支付应用", ""),

    // 支付宝/苏西话费类型
    ALIPAY_RED_PACKET(402, "支付宝红包", ""),
    SUXI_PHONE_RECHARGE(403, "苏西话费", "");

    private final Integer type;
    private final String name;
    private final String path;

    FlatProductTypeEnum(Integer type, String name, String path) {
        this.type = type;
        this.name = name;
        this.path = path;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getPath() {
        return path;
    }

    /**
     * 根据类型获取枚举
     * @param type 产品类型
     * @return 产品类型枚举
     */
    public static FlatProductTypeEnum getByType(Integer type) {
        for (FlatProductTypeEnum value : FlatProductTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据类型获取名称
     * @param type 产品类型
     * @return 产品名称
     */
    public static String getNameByType(Integer type) {
        for (FlatProductTypeEnum value : FlatProductTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return "";
    }

    /**
     * 根据类型获取路径
     * @param type 产品类型
     * @return 产品路径
     */
    public static String getPathByType(Integer type) {
        for (FlatProductTypeEnum value : FlatProductTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getPath();
            }
        }
        return "";
    }

    
}
