<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jsrxjt</groupId>
    <artifactId>rxjt-lucky-carp-mall-common-bom</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <common.verion>${project.version}</common.verion>
        <spring-boot.version>2.7.8</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <log4j2.version>2.17.1</log4j2.version>
        <spring.checkstyle.plugin>0.0.34</spring.checkstyle.plugin>
        <fastjson.version>2.0.47</fastjson.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <mysql.version>8.0.31</mysql.version>
        <seata.version>1.5.2</seata.version>
        <excel.version>1.2.6</excel.version>
        <asm.version>7.1</asm.version>
        <ali.oss.version>3.17.4</ali.oss.version>
        <sms.version>2.0.2</sms.version>
        <jaxb.version>2.3.5</jaxb.version>
        <hutool.version>5.8.12</hutool.version>
        <mica.version>2.7.4</mica.version>
        <sentinel.version>1.8.4</sentinel.version>
        <commons.client.version>3.1</commons.client.version>
        <apache.commons.version>3.12.0</apache.commons.version>
        <commons.validator.version>1.7</commons.validator.version>
        <redisson.version>3.20.0</redisson.version>
        <supply.version>3.2-SNAPSHOT</supply.version>
        <ali.opensearch.version>4.0.0</ali.opensearch.version>
        <pinyin.version>1.1.8</pinyin.version>
        <rocketmq.client.version>5.0.4</rocketmq.client.version>
        <guava.version>31.0.1-jre</guava.version>
        <okhttp3.version>4.11.0</okhttp3.version>
        <green20220302.version>1.0.3</green20220302.version>
        <alipay.sdk.version>4.40.112.ALL</alipay.sdk.version>
        <xxljob.version>2.3.1</xxljob.version>
        <skywalking-toolkit.version>8.6.0</skywalking-toolkit.version>
        <sharding-jdbc.version>5.4.0</sharding-jdbc.version>
        <snakeyaml.version>1.33</snakeyaml.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <imadcn.idwork.version>1.6.0</imadcn.idwork.version>
        <easy.es.version>2.1.0</easy.es.version>
        <coupon.version>1.0.6</coupon.version>
        <sa-token.version>1.41.0</sa-token.version>
        <cosid.version>2.12.5</cosid.version>
        <geodesy.version>1.1.3</geodesy.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.jsrxjt</groupId>
                <artifactId>rxjt-lucky-carp-mall-common-core</artifactId>
                <version>${common.verion}</version>
            </dependency>
            <dependency>
                <groupId>com.jsrxjt</groupId>
                <artifactId>rxjt-lucky-carp-mall-common-mybatis</artifactId>
                <version>${common.verion}</version>
            </dependency>

            <dependency>
                <groupId>com.jsrxjt</groupId>
                <artifactId>rxjt-lucky-carp-mall-common-adapter</artifactId>
                <version>${common.verion}</version>
            </dependency>

            <dependency>
                <groupId>com.jsrxjt</groupId>
                <artifactId>rxjt-lucky-carp-mall-common-shardingjdbc</artifactId>
                <version>${common.verion}</version>
            </dependency>
            <dependency>
                <groupId>com.jsrxjt</groupId>
                <artifactId>rxjt-lucky-carp-mall-common-es</artifactId>
                <version>${common.verion}</version>
            </dependency>
            <!--springdoc -->
            <!--http client-->
            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>${commons.client.version}</version>
            </dependency>
            <!--fastjson 版本-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  阿里云短信下发 -->
            <dependency>
                <groupId>io.springboot.sms</groupId>
                <artifactId>aliyun-sms-spring-boot-starter</artifactId>
                <version>${sms.version}</version>
            </dependency>
            <!--  seata kryo 序列化-->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-serializer-kryo</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <!--orm 相关-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--hutool bom 工具类-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--mica bom 工具类-->
            <dependency>
                <groupId>net.dreamlu</groupId>
                <artifactId>mica-bom</artifactId>
                <version>${mica.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--web 模块-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${apache.commons.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-validator</groupId>
                <artifactId>commons-validator</artifactId>
                <version>${commons.validator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.opensearch</groupId>
                <artifactId>aliyun-sdk-opensearch</artifactId>
                <version>${ali.opensearch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${ali.oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.stuxuhai</groupId>
                <artifactId>jpinyin</artifactId>
                <version>${pinyin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client-java</artifactId>
                <version>${rocketmq.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>green20220302</artifactId>
                <version>${green20220302.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxljob.version}</version>
            </dependency>

            <!-- skywalking 整合 logback -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking-toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking-toolkit.version}</version>
            </dependency>
            <!--shardingsphere sharding-jdbc组件-->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core</artifactId>
                <version>${sharding-jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-cluster-mode-repository-zookeeper</artifactId>
                <version>${sharding-jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imadcn.framework</groupId>
                <artifactId>idworker</artifactId>
                <version>${imadcn.idwork.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy.es.version}</version>
            </dependency>

            <dependency>
                <groupId>com.coupon</groupId>
                <artifactId>coupon-v3</artifactId>
                <version>${coupon.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- Sa-Token 整合 RedisTemplate -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-template</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>me.ahoo.cosid</groupId>
                <artifactId>cosid-spring-redis</artifactId>
                <version>${cosid.version}</version>
            </dependency>
            <dependency>
                <groupId>me.ahoo.cosid</groupId>
                <artifactId>cosid-spring-boot-starter</artifactId>
                <version>${cosid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.gavaghan</groupId>
                <artifactId>geodesy</artifactId>
                <version>${geodesy.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!--代码格式插件，默认使用spring 规则-->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring.checkstyle.plugin}</version>
            </plugin>
        </plugins>
    </build>
    
    

</project>