package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.*;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.api.enums.*;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.scanPay.response.HomeScanCodeResponseDTO;
import com.jsrxjt.mobile.biz.homeScanPay.service.PickScanCaseService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.ThirdPickOrderInfoBuilder;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformChannelGateway;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayCodeRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayRefundRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayResultRequest;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayCodeResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayResultResponse;
import com.jsrxjt.mobile.domain.pickplatform.response.PickPlatformPayStatusResultResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

import static com.jsrxjt.common.core.constant.RedisKeyConstants.*;

/**
 * @Description: 扫码提货领域层服务
 * @Author: ywt
 * @Date: 2025-05-19 10:24
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PickScanCaseServiceImpl implements PickScanCaseService {
    private final PickPlatformChannelGateway pickPlatformChannelGateway;
    private final RedisUtil redisUtil;
    private final AppGoodsRepository appGoodsRepository;
    private final OrderCaseService orderCaseService;
    private final ThirdPickOrderInfoBuilder thirdPickOrderInfoBuilder;
    private final CustomerRepository customerRepository;

    protected static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    private static final int orderExpireTime = 1 * 60;
    @Override
    public PickPaymentCodeResponseDTO getPaymentCode(PickPaymentCodeRequestDTO requestDTO) {
        PickPaymentCodeResponseDTO responseDTO = new PickPaymentCodeResponseDTO();
        PickPlatformPayCodeRequest request = new PickPlatformPayCodeRequest();
        request.setNonce(requestDTO.getNonce());
        request.setTimestamp(requestDTO.getTimestamp());
        request.setThirdId(requestDTO.getThirdId());
        request.setUserCode(String.valueOf(requestDTO.getUserId()));
        PickPlatformPayCodeResponse payCodeResponse = pickPlatformChannelGateway.getPaymentCode(request);
        if (Objects.nonNull(payCodeResponse)) {
            responseDTO.setPaymentCode(payCodeResponse.getPayment_code());
        }
        return responseDTO;
    }

    //创建订单(外部系统调用)
    @Override
    public PickChannelOrderResponseDTO createChannelOrder(PickChannelOrderRequestDTO requestDTO) {
        String code = requestDTO.getPayment_code();
        String offScanCode = String.format(OFF_SCAN_CODE,code);
        String dtoStr = redisUtil.get(offScanCode);
        if (StrUtil.isBlank(dtoStr)) {
            log.error("接口：getThirdVipOrder,支付码已过期,请重新获取:{}", code);
            throw new BizException("支付码已过期");
        }
        HomeScanCodeResponseDTO dto = JSONUtil.toBean(dtoStr, HomeScanCodeResponseDTO.class);
        Long customerId = dto.getCustomerId();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null) {
            log.error("接口：getThirdVipOrder,未找到会员ID为{}的会员信息", customerId);
        }
        // 保存到redis（幂等）key 用户+卡号
        String key = String.format(OFF_PRE_ORDER, customerId, requestDTO.getOrder_no());
        String s = redisUtil.get(key);
        if (StrUtil.isNotBlank(s)) {
            log.error("接口：getThirdVipOrder,pos订单已存在,请勿重复提交:{}", code);
            throw new BizException("pos订单已存在");
        }
        //获取应用详情信息
        Long appSpuId = dto.getAppId();
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(appSpuId);
        if(appGoodsEntity == null){
            log.info("接口：createChannelOrder,未找到应用信息");
            throw new BizException("未找到应用信息");
        }
        CreateOrderDTO createOrderDTO = saveOrderInfo(requestDTO, customerEntity, appGoodsEntity);
        String expiredTime = requestDTO.getExpired_time();
        DateTime parse = DateUtil.parse(expiredTime, formatter);
        createOrderDTO.setPayExpireTimestamp(parse.getTime()/1000);

        OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, thirdPickOrderInfoBuilder);
        //转换字段保存redis
        String jsonStr = JSONUtil.toJsonStr(orderInfoEntity);
        log.info("提货中台展码付key：{},订单信息：{}",key,jsonStr);
        long between = DateUtil.between(new Date(), parse.toJdkDate(), DateUnit.SECOND);
        if(between <= 0){
            log.info("接口：createChannelOrder,订单已过期");
            throw new BizException("订单已过期");
        }
        //保存redis 默认1分钟过期
        redisUtil.set(key,jsonStr,orderExpireTime);
        PickChannelOrderResponseDTO responseDTO = new PickChannelOrderResponseDTO(String.valueOf(orderInfoEntity.getOrderNo()));
        return responseDTO;
    }

    /**
     * 订单变更
     *
     * @param requestDTO
     */
    @Override
    public BaseResponse updateChannelOrder(PickChannelOrderUpdateRequestDTO requestDTO) {
        return null;
    }

    /**
     * 支付状态查询 (外部系统调用)
     *
     * @param requestDTO
     */
    @Override
    public PickPlatformPayStatusResultResponse getChannelPayStatus(PickPaymentRefundRequestDTO requestDTO) {
        return null;
    }

    private CreateOrderDTO saveOrderInfo(PickChannelOrderRequestDTO requestDTO,
                                         CustomerEntity customerEntity,
                                         AppGoodsEntity appGoodsEntity
    ) {
        CreateOrderDTO request = new CreateOrderDTO();
        request.setCustomerId(customerEntity.getId());
        request.setCustomerMobile(customerEntity.getPhone());
        request.setProductType(ProductTypeEnum.APP.getType());
        request.setProductSpuId(appGoodsEntity.getAppId());
        request.setProductSkuId(appGoodsEntity.getAppId());
        request.setExternalOrderNo(requestDTO.getOrder_no());
        request.setThirdId(appGoodsEntity.getThirdId());
        request.setExternalShopId(requestDTO.getShop_id());
        request.setExternalShopUserId(requestDTO.getShop_user_id());
        request.setTradeNo(requestDTO.getTrade_no());
        request.setExternalAppProductPrice(new BigDecimal(requestDTO.getAmount()));
        return request;
    }

    /**
     * 订单退款(外部系统调用)
     *
     * @param requestDTO
     */
    @Override
    public PickChannelOrderRefundResponseDTO refundChannelOrder(PickPaymentRefundRequestDTO requestDTO) {
        PickChannelOrderRefundResponseDTO dto = new PickChannelOrderRefundResponseDTO();
        //处理订单退款信息
        //todo 处理订单支付表
        //todo 处理订单交易流水表
        //todo 处理操作日志表
        return dto;
    }


    /**
     * 退款状态查询(外部系统调用)
     *
     * @param requestDTO
     */
    @Override
    public PickOrderRefundStatusResponseDTO getRefundStatus(PickOrderRefundStatusRequestDTO requestDTO) {
      //todo 退款状态查询
        return null;
    }

    /**
     * 订单退款结果回告[幂等性]
     *
     * @param request
     */
    @Override
    public PickOrderRefundResultResponseDTO getPaymentOrderRefund(PickPlatformPayRefundRequest request) {
        PickOrderRefundResultResponseDTO responseDTO = new PickOrderRefundResultResponseDTO();
        try {
            PickPlatformPayResultResponse paymentOrderRefund = pickPlatformChannelGateway.getPaymentOrderRefund(request);
            if (Objects.nonNull(paymentOrderRefund)) {
                responseDTO.setRefundNo(paymentOrderRefund.getRefund_no());
                responseDTO.setTradeNo(paymentOrderRefund.getTrade_no());
                responseDTO.setOrderNo(paymentOrderRefund.getOrder_no());
            }
        } catch (Exception e) {
            log.info("接口：getPaymentOrderRefund,订单退款结果回告失败:{}",JSONUtil.toJsonStr(request));
            throw new RuntimeException(e);
        }
        return responseDTO;
    }

    /**
     * 订单支付结果回告
     *
     * @param requestDTO
     */
    @Override
    public PickPlatformPayResultResponse getPaymentOrderResult(PickPlatformPayResultRequestDTO requestDTO) {
        PickPlatformPayResultRequest request = BeanUtil.toBean(requestDTO, PickPlatformPayResultRequest.class);
        request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
        request.setNonce(IdUtil.simpleUUID());
        PickPlatformPayResultResponse paymentOrderResult = pickPlatformChannelGateway.getPaymentOrderResult(request);
        return paymentOrderResult;
    }

    /**
     *
     * @param requestDTO 外部订单
     * @param localUserRequestDTO 当前用户信息
     */
    private OrderInfoEntity saveOrderInfo(PickChannelOrderRequestDTO requestDTO,
                               PickLocalUserRequestDTO localUserRequestDTO,
                               AppGoodsEntity appGoodEntity

    ){
        OrderInfoEntity entity = new OrderInfoEntity();
        //订单编号
        entity.setOrderNo("");
        entity.setCustomerId(Long.parseLong(requestDTO.getUser_code()));
        entity.setThirdId(requestDTO.getThird_id());
        entity.setExternalShopId(requestDTO.getShop_id());
        entity.setExternalShopUserId(requestDTO.getShop_user_id());
        entity.setExternalOrderNo(requestDTO.getOrder_no());
        entity.setTradeNo(requestDTO.getTrade_no());
        String type = requestDTO.getType();
        PickTypeToChannelEnum byType = PickTypeToChannelEnum.getByType(type);
        if(byType !=null){
            entity.setOrderChannel(byType.getChannel());
        }
        //默认保留2位
        BigDecimal amount = new BigDecimal(requestDTO.getAmount());
        entity.setPaymentAmount(amount);
        //商品原售价总价
        entity.setTotalSellAmount(amount);
        //优惠总金额
        entity.setTotalDiscountAmount(BigDecimal.ZERO);
        //订单金额
        entity.setOrderAmount(amount);

        entity.setCreateTime(DateUtil.parseLocalDateTime(requestDTO.getCreated_time()));
        entity.setModTime(LocalDateTime.now());
        int seconds = DateUtil.parse(requestDTO.getExpired_time(), DatePattern.NORM_DATETIME_FORMATTER).getSeconds();
        entity.setPayExpireTimestamp((long)seconds);

        entity.setCustomerMobile(localUserRequestDTO.getPhone());
        entity.setLongitude(new BigDecimal(localUserRequestDTO.getLongitude()));
        entity.setLatitude(new BigDecimal(localUserRequestDTO.getLatitude()));
        //应用id
        entity.setProductSkuId(appGoodEntity.getAppId());
        entity.setProductName(appGoodEntity.getAppName());
        entity.setFlatProductType(FlatProductTypeEnum.SCAN_PICKUP_APP.getType());
        //订单类型 订单类型: 1-卡券 2-普通应用 3-红包/充值 4扫码付应用'
        entity.setOrderType(OrderTypeEnum.SCAN_APP.getType());
        entity.setOrderStatus(OrderStatusEnum.PENDING_PAYMENT.getCode());
        entity.setOrderChannel(OrderChannelEnum.DISTRIBUTION_CENTER.getCode());
        entity.setChannelId(appGoodEntity.getChannelId());
        entity.setBrandId(appGoodEntity.getBrandId());
        //外部订单状态
       // entity.setExternalStatus();
        //支付状态
        entity.setPaymentStatus(PaymentStatusEnum.UNPAID.getCode());
        //todo 保存
        return null;
    }

    //保存订单支付信息
    private Long saveOrderPaymentInfo (PickChannelOrderRequestDTO requestDTO,
                           OrderInfoEntity orderInfoEntity

    ){
        return null;
    }

    private PickChannelOrderDetailResponseDTO setDetailOrder(PickChannelOrderRequestDTO requestDTO,long orderId){
        PickChannelOrderDetailResponseDTO dto = new PickChannelOrderDetailResponseDTO();
        dto.setCustomerId(Long.parseLong(requestDTO.getUser_code()));
        dto.setOrderId(orderId);
        dto.setCode(requestDTO.getPayment_code());
        dto.setThirdId(requestDTO.getThird_id());
        dto.setExternalShopId(requestDTO.getShop_id());
        dto.setExternalShopUserId(requestDTO.getShop_user_id());
        dto.setExternalOrderNo(requestDTO.getOrder_no());
        dto.setTradeNo(requestDTO.getTrade_no());
        dto.setRate(requestDTO.getRate());
        String type = requestDTO.getType();
        PickTypeToChannelEnum byType = PickTypeToChannelEnum.getByType(type);
        if(byType !=null){
            dto.setOrderChannel(byType.getChannel());
        }
        //默认保留2位
        BigDecimal amount = new BigDecimal(requestDTO.getAmount());
        dto.setPaymentAmount(amount);
        dto.setOrderCreateTime(DateUtil.parse(requestDTO.getCreated_time(), DatePattern.NORM_DATETIME_FORMATTER));
        dto.setPayExpireTime(DateUtil.parse(requestDTO.getExpired_time(), DatePattern.NORM_DATETIME_FORMATTER));
        //提货券支付（无用可去掉）
       //  dto.setPayType("thpay");
        return dto;
    }

}
