package com.jsrxjt.adapter.distribution.mq.consumer;

import cn.hutool.core.lang.UUID;
import lombok.SneakyThrows;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collections;


/**
 * 装修页面消息消费者配置
 * <AUTHOR>
 * @date 2025/08/26
 */
@Configuration
@RefreshScope
public class ScanPayWssConsumerConfig {


  @SneakyThrows
  @Bean(destroyMethod = "close")
  public PushConsumer skuReleasePlanPushConsumer(ClientConfiguration clientConfiguration,
    ScanPayWssMessageListener scanPayWssMessageListener) {
    FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
    String uniqueConsumerGroup = "scan_pay_wss_broadcast_" + getInstanceId();
    String topic = "scan_pay_wss_topic";
    return ClientServiceProvider.loadService().newPushConsumerBuilder()
        .setClientConfiguration(clientConfiguration)
        .setConsumerGroup(uniqueConsumerGroup)
        .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
        .setMessageListener(scanPayWssMessageListener)
        .build();
  }

  // 获取实例唯一标识
  private String getInstanceId() {
      return UUID.randomUUID().toString().substring(0, 8);
  }
}