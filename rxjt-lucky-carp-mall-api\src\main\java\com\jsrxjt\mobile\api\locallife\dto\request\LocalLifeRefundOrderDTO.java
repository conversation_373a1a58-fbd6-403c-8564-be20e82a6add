package com.jsrxjt.mobile.api.locallife.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/19 18:09
 */
@Data
@Schema(description = "本地生活退款请求参数")
public class LocalLifeRefundOrderDTO {
    @Schema(description = "福鲤圈订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "福鲤圈订单号不能为空")
    private String orderNo;

    @Schema(description = "本地生活退款请求订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "本地生活退款请求订单号不能为空")
    private String refundOrderNo;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "退款金额不能为空")
    private BigDecimal refundAmount;

    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @NotBlank(message = "签名不能为空")
    private String signature;
}
