package com.jsrxjt.mobile.api.module.types;

import java.util.HashMap;
import java.util.Map;

/**
 * 组件内容详情类型
 *
 * <AUTHOR>
 * @date 2025/04/22
 */
public enum ModuleDetailType {

    SON_TAB(10, "子tab栏"),

    SON_LOCAL_LIFE_TAB(11, "本地生活tab栏"),

    SON_GLOBAL_TAB(12, "全球购tab栏"),

    COUPON(100, "普通卡券"),

    PACKAGE_COUPON(200, "组合套餐卡券"),

    APP(300, "普通应用"),

    ALIPAY_APP_SUXI(400, "支付宝红包应用/苏西话费"),

    ADVERTISEMENT(1000, "广告"),

    SINGLE_IMAGE(1100, "单图片"),

    SLIDER_IMAGE(1200, "轮播图"),

    SINGLE_INFORMATION(1300, "单资讯"),

    MULTIPLE_INFORMATION(1400, "多资讯"),

    LOCAL_LIFE(1500, "本地生活"),

    GLOBAL_GOODS(1600, "瑞祥全球购"),

    ACTIVITY_PAGE(1700, "活动页面"),

    CUSTOMIZE_LINK(1800, "自定义链接"),

    PLATFORM_CATEGORY(1900, "平台分类");

    private Integer code;

    private String description;

    ModuleDetailType(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    private static final Map<Integer, ModuleDetailType> CODE_MAP = new HashMap<>();

    static {
        for (ModuleDetailType detailType : ModuleDetailType.values()) {
            CODE_MAP.put(detailType.getCode(), detailType);
        }
    }

    public static ModuleDetailType getByCode(int code) {
        return CODE_MAP.get(code);
    }

}
