package com.jsrxjt.mobile.api.scanPay.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 卡号dto
 * @Author: ZY
 * @Date: 20250530
 */
@Data
@Schema(description = "用户密码验证")
public class UserCheckPassRequestDTO {
    @Schema(description = "用户id")
    @NotBlank(message = "用户id不能为空")
    private Long customerId;

    @Schema(description = "密码 MD5加密")
    private String payPassword;

}
