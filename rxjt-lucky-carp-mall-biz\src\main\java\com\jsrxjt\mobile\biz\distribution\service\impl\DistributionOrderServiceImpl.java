package com.jsrxjt.mobile.biz.distribution.service.impl;

import com.jsrxjt.mobile.api.distribution.DistChannelType;
import com.jsrxjt.mobile.api.distribution.dto.request.DistributionOrderPayNotifyDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.DistributionCreateOrderResponseDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.biz.distribution.service.DistributionOrderService;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.gateway.distribution.UnifiedDistributionApi;
import com.jsrxjt.mobile.domain.gateway.distribution.request.DistPaidNotifyRequest;
import com.jsrxjt.mobile.domain.gateway.distribution.response.DistPaidNotifyResponse;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.service.strategy.OrderInfoBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Date 2025/7/24 17:58
 */
@Service
@RequiredArgsConstructor
public class DistributionOrderServiceImpl implements DistributionOrderService {

    private final OrderCaseService orderCaseService;

    private final UnifiedDistributionApi unifiedDistributionApi;

    @Override
    public DistributionCreateOrderResponseDTO distributionCreateOrder(CreateOrderDTO createOrderDTO, OrderInfoBuilder orderInfoBuilder) {
        DistributionCreateOrderResponseDTO responseDTO = new DistributionCreateOrderResponseDTO();
        OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO, orderInfoBuilder);
        responseDTO.setThirdOrderNo(orderInfoEntity.getOrderNo());
        return responseDTO;
    }

    @Override
    public void distributionOrderPayNotify(DistributionOrderPayNotifyDTO dto) {
        LocalDateTime payTime = LocalDateTime.parse(dto.getPayTime(),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        DistPaidNotifyRequest request = DistPaidNotifyRequest
                .builder()
                .channelType(DistChannelType.getByCode(dto.getDistributionType()))
                .distOrderNo(dto.getOrderNo())
                .distTradeNo(dto.getTradeNo())
                .tradeAmount(dto.getTradeAmount())
                .payTime(payTime)
                .build();
        DistPaidNotifyResponse distPaidNotifyResponse = unifiedDistributionApi.paidNotify(request);
    }
}
