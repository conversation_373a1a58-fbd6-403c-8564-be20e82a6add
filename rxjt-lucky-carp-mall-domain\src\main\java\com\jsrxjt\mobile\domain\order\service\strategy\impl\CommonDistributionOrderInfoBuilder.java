package com.jsrxjt.mobile.domain.order.service.strategy.impl;

import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 通用分销应用三方推单样例订单信息构建器
 *
 * <AUTHOR>
 * @Date 2025/7/24 17:58
 */
@Component
@Slf4j
public class CommonDistributionOrderInfoBuilder extends DefaultOrderInfoBuilder {

    public CommonDistributionOrderInfoBuilder(BusinessIdGenerator businessIdGenerator, ConfigRepository configRepository) {
        super(businessIdGenerator, configRepository);
    }

    @Override
    public void supplementOrderInfo(OrderInfoEntity orderInfo, CreateOrderDTO request) {
        log.info("补充第三方推单订单信息，订单号：{}", orderInfo.getOrderNo());
        // 设置特定的订单渠道
        orderInfo.setOrderChannel(OrderChannelEnum.DISTRIBUTION_CENTER.getCode());
        log.info("第三方推单订单信息补充完成，外部订单号：{}", orderInfo.getExternalOrderNo());
    }
} 