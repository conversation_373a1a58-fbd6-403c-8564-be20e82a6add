package com.jsrxjt.mobile.biz.order;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.order.dto.request.OrderListRequestDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderDetailResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.OrderListResponseDTO;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.service.strategy.OrderInfoBuilder;

/**
 * 订单业务接口
 * 
 * <AUTHOR>
 * @description
 * @since 2025/6/11
 **/
public interface OrderCaseService {
  /**
   * 提交订单（使用默认构建器）
   *
   * @param request 创建订单请求参数
   * @return 创建成功返回订单实体，创建失败抛出异常
   */
  OrderInfoEntity submitOrder(CreateOrderDTO request);

  /**
   * 提交订单（指定构建器）
   * 
   * @param request 创建订单请求参数
   * @param builder 订单信息构建器
   * @ return 创建成功返回订单实体，创建失败抛出异常
   */
  OrderInfoEntity submitOrder(CreateOrderDTO request, OrderInfoBuilder builder);

  /**
   * 取消订单
   *
   * @param orderNo 订单号
   * @param customerId 用户ID
   */
  void cancelOrder(String orderNo,Long customerId);

  /**
   * 系统取消订单
   *
   * @param orderNo 订单号
   */
  void cancelOrderBySystem(String orderNo);

  /**
   * 分页查询用户订单列表
   *
   * @param customerId 客户ID
   * @param request 查询请求参数
   * @return 订单列表分页数据
   */
  PageDTO<OrderListResponseDTO> pageOrderList(Long customerId, OrderListRequestDTO request);

  /**
   * 获取订单详情
   * 
   * @param customerId 客户ID
   * @param orderNo 订单号
   * @return 订单详情
   */
  OrderDetailResponseDTO getOrderDetail(Long customerId, String orderNo);

  /**
   * 删除订单（设置为不显示状态）
   * 
   * @param orderNo 订单号
   * @param customerId 客户ID
   */
  void markOrderAsHidden(String orderNo, Long customerId);
}
