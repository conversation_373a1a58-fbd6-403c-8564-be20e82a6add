package com.jsrxjt.mobile.biz.ticket.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.ticket.GlobalTicketResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryDetailResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketDeliveryResponseDTO;
import com.jsrxjt.mobile.api.ticket.TicketShopListResponseDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryDetailRequestDTO;
import com.jsrxjt.mobile.api.ticket.request.TicketDeliveryRequestDTO;
import com.jsrxjt.mobile.biz.ticket.TicketDeliveryService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.gateway.FuliquanShopTicketGateway;
import com.jsrxjt.mobile.domain.customer.gateway.request.ShopTicketListRequestDTO;
import com.jsrxjt.mobile.domain.customer.gateway.response.ShopTicketListResponseDTO;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.global.gateway.GlobalGateWay;
import com.jsrxjt.mobile.domain.global.request.GlobalTicketListRequest;
import com.jsrxjt.mobile.domain.global.response.GlobalTicketResponse;
import com.jsrxjt.mobile.domain.ticket.entity.TicketDeliveryEntity;
import com.jsrxjt.mobile.domain.ticket.repository.TicketDeliveryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TicketDeliveryServiceImpl implements TicketDeliveryService {

    /**
     * 全球购券类型 1现金券 2满减券
     */
    public static final int GLOBAL_TICKET_TYPE_CASH = 1;
    public static final int GLOBAL_TICKET_TYPE_DISCOUNT = 2;

    private final TicketDeliveryRepository ticketDeliveryRepository;
    private final FuliquanShopTicketGateway fuliquanShopTicketGateway;
    private final CustomerRepository customerRepository;

    private final GlobalGateWay globalGateWay;
    /**
     * @param request
     * @return
     */
    @Override
    public PageDTO<TicketDeliveryResponseDTO> getTicketDelivery(TicketDeliveryRequestDTO request) {
       long coustomerId = StpUtil.getLoginIdAsLong();
       // long coustomerId = 111l;
        PageDTO<TicketDeliveryEntity> ticketDelivery = ticketDeliveryRepository.getTicketDeliveryByUserId(coustomerId, request.getPage(), request.getSize());
        if(ticketDelivery.getTotal()==0){
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }
        List<TicketDeliveryResponseDTO> responses = new ArrayList<>();
        for (TicketDeliveryEntity ticketDeliveryEntity : ticketDelivery.getRecords()) {
            TicketDeliveryResponseDTO bean = BeanUtil.toBean(ticketDeliveryEntity, TicketDeliveryResponseDTO.class);
            responses.add( bean);
        }
        return PageDTO.build(responses,ticketDelivery.getTotal(),ticketDelivery.getSize(),ticketDelivery.getCurrent());
    }

    /**
     * @param request
     * @return
     */
    @Override
    public TicketDeliveryDetailResponseDTO getTicketDeliveryById(TicketDeliveryDetailRequestDTO request) {
        TicketDeliveryEntity ticketDeliveryEntity = ticketDeliveryRepository.getTicketDeliveryById(request.getId());
        if(ticketDeliveryEntity!=null){
            TicketDeliveryDetailResponseDTO bean = BeanUtil.toBean(ticketDeliveryEntity, TicketDeliveryDetailResponseDTO.class);
            return bean;
        }
        return null;
    }

    /**
     * @param request
     */
    @Override
    public void delTicketDelivery(TicketDeliveryDetailRequestDTO request) {
        ticketDeliveryRepository.delTicketDelivery(request.getId());
    }

    /**
     * @param request
     * @return
     */
    @Override
    public PageDTO<TicketShopListResponseDTO> getShopTicketList(TicketDeliveryRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        //判断是否已经领取
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if(customerEntity == null || customerEntity.getDelFlag() == 1 || customerEntity.getStatus() != 1){
            throw new BizException("该会员账号不存在或注销");
        }
        ShopTicketListRequestDTO requestDTO = new ShopTicketListRequestDTO();
        requestDTO.setNonce(IdUtil.fastSimpleUUID());
        requestDTO.setTimestamp(Instant.now().getEpochSecond());
        requestDTO.setVipid(customerEntity.getVipId());
        requestDTO.setPage(request.getPage());
        requestDTO.setSize(request.getSize());

        PageDTO<ShopTicketListResponseDTO> shopTicketList = fuliquanShopTicketGateway.getShopTicketList(requestDTO);
        if(shopTicketList.getTotal()==0){
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }
        List<TicketShopListResponseDTO> responses = new ArrayList<>();
        shopTicketList.getRecords().forEach(shopTicketListResponseDTO -> {
            TicketShopListResponseDTO bean = new TicketShopListResponseDTO();
            bean.setId(shopTicketListResponseDTO.getId());
            bean.setTicketName(shopTicketListResponseDTO.getTitle());
            bean.setAmount(shopTicketListResponseDTO.getValue());
            bean.setTicketCode(shopTicketListResponseDTO.getNo());
            bean.setStartTime(shopTicketListResponseDTO.getStartTime());
            bean.setEndTime(shopTicketListResponseDTO.getEndTime());
            bean.setSummary(shopTicketListResponseDTO.getSummary());
            bean.setTicketStatus(Integer.valueOf(shopTicketListResponseDTO.getTab()));
            bean.setRule(shopTicketListResponseDTO.getRule());
            bean.setLogo(shopTicketListResponseDTO.getLogo());
            responses.add(bean);
        });
        return PageDTO.build(responses,shopTicketList.getTotal(),shopTicketList.getSize(),shopTicketList.getCurrent());
    }

    @Override
    public PageDTO<GlobalTicketResponseDTO> getGlobalTicketList(TicketDeliveryRequestDTO request) {
        Long customerId = StpUtil.getLoginIdAsLong();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if(customerEntity == null || customerEntity.getDelFlag() == 1 || customerEntity.getStatus() != 1){
            throw new BizException("该会员账号不存在或注销");
        }
        GlobalTicketListRequest globalTicketListRequest = new GlobalTicketListRequest();
        globalTicketListRequest.setUnionId(customerEntity.getUnionid());
        globalTicketListRequest.setToPage(String.valueOf(request.getPage()));
        globalTicketListRequest.setPageRows(String.valueOf(request.getSize()));
        PageDTO<GlobalTicketResponse> globalTicketList = globalGateWay.getTicketList(globalTicketListRequest);
        if(globalTicketList == null || globalTicketList.getTotal()==0){
            return PageDTO.emptyBuild(request.getPage(), request.getSize());
        }
        List<GlobalTicketResponseDTO> responses = globalTicketList.getRecords().stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
        return PageDTO.build(responses,globalTicketList.getTotal(),globalTicketList.getSize(),globalTicketList.getCurrent());
    }

    /**
     * 转换GlobalTicketResponse为DTO对象
     */
    private GlobalTicketResponseDTO convertToResponseDTO(GlobalTicketResponse globalTicketResponse) {
        GlobalTicketResponseDTO dto = new GlobalTicketResponseDTO();
        dto.setId(globalTicketResponse.getTicketId());
        dto.setTicketName(globalTicketResponse.getLhqName());
        dto.setAmount(globalTicketResponse.getReduceValue());
        dto.setTicketCode(globalTicketResponse.getTicketCode());
        dto.setStartTime(globalTicketResponse.getStartTime());
        dto.setEndTime(globalTicketResponse.getEndTime());
        dto.setTicketStatus(globalTicketResponse.getTicketStatus());
        if (globalTicketResponse.getLhqType() != null) {
            if (globalTicketResponse.getLhqType() == GLOBAL_TICKET_TYPE_CASH){
                dto.setSummary(globalTicketResponse.getReduceValue() + "元无门槛券");
                dto.setTicketType("无门槛券");
            } else if (globalTicketResponse.getLhqType() == GLOBAL_TICKET_TYPE_DISCOUNT){
                dto.setSummary("满" + globalTicketResponse.getCondition1() + "减" + globalTicketResponse.getReduceValue() + "元");
                dto.setTicketType("满减券");
            }
        }
        return dto;
    }
}
