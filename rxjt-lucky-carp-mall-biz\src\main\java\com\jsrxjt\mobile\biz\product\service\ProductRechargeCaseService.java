package com.jsrxjt.mobile.biz.product.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.product.dto.request.AccountRechargeRecordRequestDTO;
import com.jsrxjt.mobile.api.product.dto.request.RechargeAccountHistoryRequestDTO;
import com.jsrxjt.mobile.api.coupon.dto.response.CouponSkusResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.RechargeAccountHistoryResponseDTO;
import com.jsrxjt.mobile.api.product.dto.response.RechargeRecordResponse;

import java.util.List;

/**
 * @Description: 产品充值服务接口
 * @Author: ywt
 * @Date: 2025-08-07 11:34
 * @Version: 1.0
 */
public interface ProductRechargeCaseService {
    List<RechargeAccountHistoryResponseDTO> getHisRechargeAccounts(RechargeAccountHistoryRequestDTO requestDTO);
    PageDTO<RechargeRecordResponse> getRechargeRecords(AccountRechargeRecordRequestDTO requestDTO);
}
