package com.jsrxjt.mobile.infra.order.repository;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.domain.order.query.OrderListQuery;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.infra.order.persistent.mapper.OrderItemMapper;
import com.jsrxjt.mobile.infra.order.persistent.mapper.OrderMapper;
import com.jsrxjt.mobile.infra.order.persistent.mapper.SubSkuOrderMapper;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderItemPO;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderPO;
import com.jsrxjt.mobile.infra.order.persistent.po.OrderWithItemsPO;
import com.jsrxjt.mobile.infra.order.persistent.po.SubSkuOrderPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单仓储实现类
 *
 * <AUTHOR> Fengping
 * @since 2025/6/14
 **/
@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderRepositoryImpl implements OrderRepository {

    private final OrderMapper orderMapper;
    private final OrderItemMapper orderItemMapper;
    private final SubSkuOrderMapper subSkuOrderMapper;
    private final RedisUtil redisUtil;

    @Override
    @Transactional
    public void saveOrder(OrderInfoEntity order) {
        OrderPO orderPO = new OrderPO();
        BeanUtils.copyProperties(order, orderPO);
        int insert = orderMapper.insert(orderPO);
        if (insert <= 0) {
            throw new BizException("订单保存失败");
        }

        List<OrderItemPO> orderItems = order.getOrderItems().stream().map(item -> {
            OrderItemPO itemPO = new OrderItemPO();
            BeanUtils.copyProperties(item, itemPO);
            return itemPO;
        }).toList();

        int insert1 = orderItemMapper.insertAllBatch(orderItems);
        if (insert1 <= 0) {
            throw new BizException("订单项保存失败");
        }

        // 保存子SKU订单（套餐商品拆分后的子订单）
        order.getOrderItems().forEach(item -> {
            if (item.getSubSkuOrders() != null && !item.getSubSkuOrders().isEmpty()) {
                List<SubSkuOrderPO> subSkuOrderPOs = item.getSubSkuOrders().stream().map(subSkuOrder -> {
                    SubSkuOrderPO subSkuOrderPO = new SubSkuOrderPO();
                    BeanUtils.copyProperties(subSkuOrder, subSkuOrderPO);
                    return subSkuOrderPO;
                }).toList();

                int insertSubSku = subSkuOrderMapper.insertAllBatch(subSkuOrderPOs);
                if (insertSubSku <= 0) {
                    throw new BizException("子SKU订单保存失败");
                }

                log.info("子SKU订单保存成功，订单号：{}，子订单数量：{}", order.getOrderNo(), subSkuOrderPOs.size());
            }
        });
        if (!CollectionUtils.isEmpty(order.getGiftTickets())) {
            // 缓存赠券信息
            redisUtil.set(RedisKeyConstants.ORDER_GIFT_TICKET_KEY + order.getOrderNo(), JSON.toJSONString(order.getGiftTickets()),86400);
        }


    }

    @Override
    public OrderInfoEntity findByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getOrderNo, orderNo);
        OrderPO orderPO = orderMapper.selectOne(queryWrapper);

        if (orderPO == null) {
            return null;
        }

        return convertToEntity(orderPO);
    }

    @Override
    public void updateOrder(OrderInfoEntity order) {
        OrderPO orderPO = new OrderPO();
        BeanUtils.copyProperties(order, orderPO);

        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getId, orderPO.getId())
                .eq(OrderPO::getCustomerId, orderPO.getCustomerId());
        int update = orderMapper.update(orderPO, queryWrapper);
        if (update <= 0) {
            throw new BizException("订单更新失败");
        }
    }

    @Override
    public void updateOrderByExternalOrderNo(OrderInfoEntity order) {
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderStatus(order.getOrderStatus());

        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getExternalOrderNo, order.getExternalOrderNo());
        int update = orderMapper.update(orderPO, queryWrapper);
        if (update <= 0) {
            throw new BizException("订单更新失败");
        }
    }

    @Override
    public void updateOrderCheckCurrentStatus(OrderInfoEntity order, PaymentStatusEnum currentPaymentStatus, OrderStatusEnum currentOrderStatus) {
        OrderPO orderPO = new OrderPO();
        BeanUtils.copyProperties(order, orderPO);

        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getId, orderPO.getId())
                .eq(OrderPO::getCustomerId, orderPO.getCustomerId())
                .eq(OrderPO::getPaymentStatus, currentPaymentStatus.getCode())
                .eq(OrderPO::getOrderStatus, currentOrderStatus.getCode());

        int update = orderMapper.update(orderPO, queryWrapper);
        if (update <= 0) {
            throw new BizException("订单状态已变更，更新失败");
        }

        log.info("订单状态检查更新成功，订单ID：{}，当前支付状态：{}，当前订单状态：{}",
                order.getId(), currentPaymentStatus.getDescription(), currentOrderStatus.getValue());
    }

    /**
     * PO转换为Entity（包含订单项信息和子SKU订单信息）
     */
    private OrderInfoEntity convertToEntity(OrderPO orderPO) {
        OrderInfoEntity entity = new OrderInfoEntity();
        BeanUtils.copyProperties(orderPO, entity);

        // 查询订单项信息
        LambdaQueryWrapper<OrderItemPO> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper.eq(OrderItemPO::getOrderNo, orderPO.getOrderNo());
        List<OrderItemPO> orderItemPOs = orderItemMapper.selectList(itemQueryWrapper);
        if (orderItemPOs == null || orderItemPOs.isEmpty()) {
            throw new BizException("订单项信息查询失败");
        }
        List<OrderItemEntity> orderItems = orderItemPOs.stream().map(itemPO -> {
            OrderItemEntity itemEntity = new OrderItemEntity();
            BeanUtils.copyProperties(itemPO, itemEntity);

            // 如果是套餐类型商品，查询对应的子SKU订单
            if (itemEntity.getProductType() == 2) {
                LambdaQueryWrapper<SubSkuOrderPO> subSkuQueryWrapper = new LambdaQueryWrapper<>();
                subSkuQueryWrapper.eq(SubSkuOrderPO::getCustomerId, itemPO.getCustomerId())
                        .eq(SubSkuOrderPO::getOrderItemId, itemPO.getId());
                List<SubSkuOrderPO> subSkuOrderPOs = subSkuOrderMapper.selectList(subSkuQueryWrapper);

                if (subSkuOrderPOs != null && !subSkuOrderPOs.isEmpty()) {
                    List<SubSkuOrderEntity> subSkuOrders = subSkuOrderPOs.stream().map(subSkuPO -> {
                        SubSkuOrderEntity subSkuEntity = new SubSkuOrderEntity();
                        BeanUtils.copyProperties(subSkuPO, subSkuEntity);
                        return subSkuEntity;
                    }).toList();
                    itemEntity.setSubSkuOrders(subSkuOrders);
                }
            }

            return itemEntity;
        }).toList();
        entity.setOrderItems(orderItems);
        return entity;
    }

    @Override
    public PageDTO<OrderInfoEntity> findOrderListByPage(OrderListQuery query) {
        // 创建分页对象
        Page<OrderWithItemsPO> page = new Page<>(query.getPageNum(), query.getPageSize());

        // 构建查询条件 - 使用表别名避免字段歧义
        QueryWrapper<OrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("o.customer_id", query.getCustomerId());

        if (query.getOrderNo() != null) {
            queryWrapper.eq("o.order_no", query.getOrderNo());
        }
        if (query.getProductName() != null) {
            queryWrapper.like("oi.product_name", query.getProductName());
        }

        if (query.getOrderStatus() != null) {
            queryWrapper.eq("o.order_status", query.getOrderStatus());
        }

        // 创建时间范围查询（闭区间）
        if (query.getCreateTimeStart() != null) {
            queryWrapper.ge("o.create_time", query.getCreateTimeStart());
        }
        if (query.getCreateTimeEnd() != null) {
            queryWrapper.le("o.create_time", query.getCreateTimeEnd());
        }

        if (query.getIsShow() != null) {
            queryWrapper.eq("o.is_show", query.getIsShow());
        }

        // 商品分类ID查询（需要通过订单项关联）
        if (query.getFirstCategoryId() != null) {
            queryWrapper.eq("oi.first_category_id", query.getFirstCategoryId());
        }

        queryWrapper.orderByDesc("o.id");

        // 执行分页查询
        IPage<OrderWithItemsPO> resultPage = orderMapper.selectOrderListWithItemsPage(
                page, queryWrapper);

        // 转换为实体
        List<OrderInfoEntity> orderList = new ArrayList<>();
        if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
            for (OrderWithItemsPO orderWithItemsPO : resultPage.getRecords()) {
                OrderInfoEntity orderEntity = new OrderInfoEntity();
                BeanUtils.copyProperties(orderWithItemsPO, orderEntity);

                // 转换订单项
                if (orderWithItemsPO.getOrderItems() != null && !orderWithItemsPO.getOrderItems().isEmpty()) {
                    List<OrderItemEntity> orderItems = orderWithItemsPO.getOrderItems().stream()
                            .map(itemPO -> {
                                OrderItemEntity itemEntity = new OrderItemEntity();
                                BeanUtils.copyProperties(itemPO, itemEntity);
                                return itemEntity;
                            }).collect(Collectors.toList());
                    orderEntity.setOrderItems(orderItems);
                }

                orderList.add(orderEntity);
            }
        }

        // 构建分页结果
        return PageDTO.<OrderInfoEntity>builder()
                .records(orderList)
                .total(resultPage.getTotal())
                .current(resultPage.getCurrent())
                .size(resultPage.getSize())
                .pages(resultPage.getPages())
                .build();
    }

    @Override
    public void updateSubSkuOrder(SubSkuOrderEntity updateSubSku) {
        SubSkuOrderPO subSkuOrderPO = new SubSkuOrderPO();
        BeanUtils.copyProperties(updateSubSku, subSkuOrderPO);

        LambdaQueryWrapper<SubSkuOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubSkuOrderPO::getId, subSkuOrderPO.getId())
                .eq(SubSkuOrderPO::getCustomerId, subSkuOrderPO.getCustomerId());
        int update = subSkuOrderMapper.update(subSkuOrderPO, queryWrapper);
        if (update <= 0) {
            throw new BizException("子sku订单信息更新失败");
        }

    }

    @Override
    public int countOrderByStatus(Long customerId, OrderStatusEnum orderStatus) {
        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getCustomerId, customerId)
                .eq(OrderPO::getOrderStatus, orderStatus.getCode())
                .eq(OrderPO::getDelFlag, NumberUtils.INTEGER_ZERO);
        return Math.toIntExact(orderMapper.selectCount(queryWrapper));
    }

    @Override
    public PageDTO<OrderInfoEntity> findByFlatProductTypeAndDateRange(OrderListQuery query, Integer flatProductType, Long productSpuId) {
        Page<OrderWithItemsPO> page = new Page<>(query.getPageNum(), query.getPageSize());

        QueryWrapper<OrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("o.customer_id", query.getCustomerId())
                .eq(Objects.nonNull(productSpuId), "o.product_spu_id", productSpuId)
                .eq("o.flat_product_type", flatProductType)
                .ge("o.create_time", query.getCreateTimeStart())
                .le("o.create_time", query.getCreateTimeEnd())
                .eq("o.del_flag", NumberUtils.INTEGER_ZERO)
                .and(wrapper -> wrapper
                        .eq("o.delivery_status", 2)
                        .or()
                        .eq("o.delivery_status", 3))
                .orderByDesc("o.id");
        IPage<OrderWithItemsPO> resultPage = orderMapper.selectOrderListWithItemsPage(page, queryWrapper);
        List<OrderInfoEntity> orderList = new ArrayList<>();
        if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
            for (OrderWithItemsPO orderWithItemsPO : resultPage.getRecords()) {
                OrderInfoEntity orderEntity = new OrderInfoEntity();
                BeanUtils.copyProperties(orderWithItemsPO, orderEntity);
                // 转换订单项
                if (orderWithItemsPO.getOrderItems() != null && !orderWithItemsPO.getOrderItems().isEmpty()) {
                    List<OrderItemEntity> orderItems = orderWithItemsPO.getOrderItems().stream()
                            .map(itemPO -> {
                                OrderItemEntity itemEntity = new OrderItemEntity();
                                BeanUtils.copyProperties(itemPO, itemEntity);
                                return itemEntity;
                            }).collect(Collectors.toList());
                    orderEntity.setOrderItems(orderItems);
                }
                orderList.add(orderEntity);
            }
        }
        return PageDTO.<OrderInfoEntity>builder()
                .records(orderList)
                .total(resultPage.getTotal())
                .current(resultPage.getCurrent())
                .size(resultPage.getSize())
                .build();
    }

    @Override
    public String getLastRechargeAccount(Long customerId, OrderChannelEnum orderChannel) {
        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getCustomerId, customerId)
                .eq(OrderPO::getOrderStatus, OrderStatusEnum.TRADE_SUCCESS.getCode())
                .eq(OrderPO::getOrderChannel, orderChannel.getCode())
                .eq(OrderPO::getDelFlag, NumberUtils.INTEGER_ZERO)
                .orderByDesc(OrderPO::getCreateTime)
                .last("LIMIT 1");
        OrderPO orderPO = orderMapper.selectOne(queryWrapper);
        if (orderPO != null) {
            return orderPO.getRechargeAccount();
        }
        return null;
    }

    @Override
    public OrderInfoEntity findOrderByExternalOrderNoAndTradeNo(String externalOrderNo, String tradeNo) {
        LambdaQueryWrapper<OrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPO::getExternalOrderNo, externalOrderNo);
        queryWrapper.eq(OrderPO::getTradeNo, tradeNo);
        OrderPO orderPO = orderMapper.selectOne(queryWrapper);
        if (orderPO == null) {
            return null;
        }
        OrderInfoEntity orderEntity = new OrderInfoEntity();
        BeanUtils.copyProperties(orderPO, orderEntity);
        return orderEntity;
    }
}
