package com.jsrxjt.mobile.api.locallife.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/17 14:17
 */
@Data
@Schema(description = "本地生活下单请求参数")
public class LocalLifeCreateOrderDTO {

    @Schema(description = "本地生活侧订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "本地生活侧订单号不能为空")
    private String orderNo;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "支付金额不能为空")
    private BigDecimal tradeAmount;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    private String orderType;

    private String detailUrl;

    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @NotBlank(message = "签名不能为空")
    private String signature;
}
