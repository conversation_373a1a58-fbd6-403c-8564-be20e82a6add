package com.jsrxjt.mobile.infra.module.strategy.global;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.module.annotation.ModuleDetailTypeHandler;
import com.jsrxjt.mobile.api.module.types.ModuleDetailType;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.domain.module.entity.ModuleDetailEntity;
import com.jsrxjt.mobile.domain.module.service.ModuleDetailStrategy;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.infra.coupon.persistent.mapper.CouponGoodsMapper;
import com.jsrxjt.mobile.infra.coupon.persistent.mapper.CouponGoodsSkuMapper;
import com.jsrxjt.mobile.infra.coupon.persistent.po.CouponGoodsPO;
import com.jsrxjt.mobile.infra.coupon.persistent.po.CouponGoodsSkuPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 全球购商品策略
 * <AUTHOR>
 * @date 2025/07/30
 */
@ModuleDetailTypeHandler({ModuleDetailType.GLOBAL_GOODS})
@Component
@RequiredArgsConstructor
@Slf4j
public class ModuleGlobalDetailStrategyImpl extends ModuleDetailStrategy {

    private final RedisUtil redisUtil;

    @Override
    public ModuleDetailEntity updateModuleDetailEntityInfo(ModuleDetailEntity moduleDetailEntity) {
        if (StringUtils.isEmpty(moduleDetailEntity.getProductItemId())){
            log.error("未获取到全球购商品id");
            return moduleDetailEntity;
        }
        Long productItemId = Long.valueOf(moduleDetailEntity.getProductItemId());
        String key = RedisKeyConstants.PAGE_TAB_GLOBAL_GOODS;
        Set<String> golbalSet = redisUtil.zrange(key, 0, -1);
        if (CollectionUtil.isEmpty( golbalSet)){
            log.info("未获取到全球购缓存,商品不能售卖");
            moduleDetailEntity.setOnSale(false);
            return moduleDetailEntity;
        }
        Map<String, ModuleDetailEntity> detailEntityList = golbalSet.stream()
                .map(detailEntity -> JSONObject.parseObject(detailEntity, ModuleDetailEntity.class))
                .collect(Collectors.toMap(ModuleDetailEntity::getProductItemId, detailEntity -> detailEntity));
        if (!detailEntityList.containsKey(moduleDetailEntity.getProductItemId())){
            log.info("未获取到全球购产品,商品不能售卖 productItemId={}", productItemId);
            moduleDetailEntity.setOnSale(false);
            updateModuleDetailEntityStatusOrDel(moduleDetailEntity.getDetailId(), 0, 0);
            return moduleDetailEntity;
        }
        ModuleDetailEntity globalModuleDetailEntity =  detailEntityList.get(moduleDetailEntity.getProductItemId());
        moduleDetailEntity.setProductImgUrl(globalModuleDetailEntity.getProductImgUrl());
        return moduleDetailEntity;
    }

}
