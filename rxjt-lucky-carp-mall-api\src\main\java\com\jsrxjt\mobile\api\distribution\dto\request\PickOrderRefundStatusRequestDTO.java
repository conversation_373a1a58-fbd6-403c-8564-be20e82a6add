package com.jsrxjt.mobile.api.distribution.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jsrxjt.common.core.vo.SignRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PickOrderRefundStatusRequestDTO extends SignRequest {

    @Schema(description = "平台订单流水好")
    @JSONField(name = "order_no")
    private String order_no;

    @Schema(description = "平台交易流水号")
    @JSONField(name = "trade_no")
    private String trade_no;

    @Schema(description = "外部退款单号")
    @JSONField(name = "refund_sn")
    private String refund_sn;

    @Schema(description = "第三方id 默认1-瑞祥商户")
    @JSONField(name = "third_id")
    private String third_id;

    @Schema(description = "类型描述，例如：place-分销订单 yike-逸刻 watsons-屈臣氏 ")
    private String type;


}
