package com.jsrxjt.mobile.domain.order.service.impl;

import com.jsrxjt.common.core.constant.BusinessCode;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleApplyRequest;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderItemEntity;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.AfterSaleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 售后领域服务实现
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AfterSaleServiceImpl implements AfterSaleService {

    private final BusinessIdGenerator businessIdGenerator;

    private final AfterSaleRepository afterSaleRepository;

    private final OrderRepository orderRepository;

    @Override
    public AfterSaleEntity applyAfterSale(OrderInfoEntity order, AfterSaleApplyRequest request) {
        log.info("开始处理售后申请，订单号：{}", request.getOrderNo());
        // 1. 校验订单状态
        validateOrderStatusForAfterSale(order);
        
        // 2. 获取订单项（取第一个）
        OrderItemEntity orderItem = order.getOrderItems().get(0);
        
        // 3. 获取已存在的售后申请单
        List<AfterSaleEntity> existingAfterSales = getExistingAfterSales(request.getOrderNo());
        
        // 4. 计算可退款金额
        BigDecimal refundableAmount = calculateRefundableAmount(order, orderItem, request, existingAfterSales);
        
        // 5. 校验申请退款金额
        validateRefundAmount(request.getApplyRefundAmount(), refundableAmount);
        
        // 6. 构建售后实体
        AfterSaleEntity afterSaleEntity = buildAfterSaleEntity(order, orderItem, request, refundableAmount);
        log.info("售后申请构建完成，售后单号：{}", afterSaleEntity.getAfterSaleNo());
        return afterSaleEntity;
    }

    @Override
    public void syncOrderAfterSaleStatus(AfterSaleEntity afterSale) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(afterSale.getOrderId());
        updateOrder.setCustomerId(afterSale.getCustomerId());
        updateOrder.setAfterSaleStatus(afterSale.getAfterSaleStatus());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
    }

    private void validateOrderStatusForAfterSale(OrderInfoEntity order) {
        // 未支付订单才能申请售后
        if (Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.UNPAID.getCode())) { // 1-已支付
            throw new BizException("订单未支付，不能申请售后");
        }
        if (Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.FULL_REFUNDED.getCode())) {
            throw new BizException("订单已全额退款，不能申请售后");
        }

        // 只有进行中或交易成功的订单才能申请售后
        if (!Objects.equals(order.getOrderStatus(), OrderStatusEnum.IN_PROGRESS.getCode()) && // 10-进行中
            !Objects.equals(order.getOrderStatus(), OrderStatusEnum.TRADE_SUCCESS.getCode())) {  // 20-交易成功
            throw new BizException("订单状态不允许申请售后");
        }
    }

    /**
     * 获取已存在的售后申请单
     */
    private List<AfterSaleEntity> getExistingAfterSales(String orderNo) {
        return afterSaleRepository.findValidAfterSalesByOrderNo(orderNo);
    }

    /**
     * 计算可退款金额
     */
    private BigDecimal calculateRefundableAmount(OrderInfoEntity order, OrderItemEntity orderItem, 
                                                 AfterSaleApplyRequest request, List<AfterSaleEntity> existingAfterSales) {
        BigDecimal maxRefundableAmount;

        // 根据售后类型计算最大可退款金额
        if (Objects.equals(request.getAfterSaleType(), AfterSaleTypeEnum.PARTIAL_REFUND.getCode())) {
            // 部分退款：按比例计算最大可退款金额 = 订单支付金额 * 售后数量/订单项数量
            maxRefundableAmount = order.getPaymentAmount().multiply(
                    new BigDecimal(request.getAfterSaleQuantity()).divide(
                            new BigDecimal(orderItem.getQuantity()), 2, RoundingMode.HALF_UP));
        } else {
            // 全额退款：最大可退款金额 = 订单支付金额
            maxRefundableAmount = order.getPaymentAmount();
        }

        // 计算已退款总额
        BigDecimal totalRefundedAmount = calculateTotalRefundedAmount(existingAfterSales);
        
        // 计算剩余可退款金额
        BigDecimal remainAmount = order.getPaymentAmount().subtract(totalRefundedAmount);
        if (remainAmount.compareTo(BigDecimal.ZERO) < 0) {
            remainAmount = BigDecimal.ZERO;
        }

        // 返回较小值作为实际可退款金额
        return maxRefundableAmount.compareTo(remainAmount) > 0 ? remainAmount : maxRefundableAmount;
    }

    /**
     * 计算已退款总额
     */
    private BigDecimal calculateTotalRefundedAmount(List<AfterSaleEntity> existingAfterSales) {
        if (existingAfterSales == null || existingAfterSales.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return existingAfterSales.stream()
                .map(AfterSaleEntity::getRefundAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void validateRefundAmount(BigDecimal applyRefundAmount, BigDecimal refundableAmount) {
        if (applyRefundAmount.compareTo(refundableAmount) > 0) {
            log.warn("申请退款金额超过可退款的金额，申请金额：{} 可退款金额{}", applyRefundAmount,refundableAmount);
            throw new BizException("申请退款金额不能超过可退款的金额：" + refundableAmount);
        }
    }

    private AfterSaleEntity buildAfterSaleEntity(OrderInfoEntity order, OrderItemEntity orderItem,
                                                 AfterSaleApplyRequest request, BigDecimal refundableAmount) {
        
        AfterSaleEntity afterSale = new AfterSaleEntity();

        afterSale.setId(businessIdGenerator.generateId());
        
        // 生成售后单号
        afterSale.setAfterSaleNo(businessIdGenerator.generateDayBizId(
                BusinessCode.AFTER_SALE, String.valueOf(request.getCustomerId())));
        
        // 基本信息
        afterSale.setOrderId(order.getId());
        afterSale.setOrderNo(request.getOrderNo());
        afterSale.setOrderItemId(orderItem.getId());
        afterSale.setCustomerId(request.getCustomerId());
        afterSale.setCustomerMobile(order.getCustomerMobile());
        afterSale.setTradeNo(order.getTradeNo());
        
        // 售后信息
        //退款单号 暂时和售后单号一致
        afterSale.setRefundNo(afterSale.getAfterSaleNo());
        afterSale.setAfterSaleType(request.getAfterSaleType());
        afterSale.setOriginalOrderStatus(order.getOrderStatus());
        afterSale.setAfterSaleStatus(AfterSaleStatusEnum.PENDING_AUDIT.getCode());
        afterSale.setRefundStatus(RefundStatusEnum.NOT_REFUNDED.getCode()); // 0-未退款
        afterSale.setAfterSaleReason(request.getAfterSaleReason());
        afterSale.setRefundDescription(request.getRefundDescription());
        afterSale.setAfterSaleQuantity(request.getAfterSaleQuantity());
        
        // 金额信息
        afterSale.setApplyRefundAmount(request.getApplyRefundAmount());
        afterSale.setRefundableAmount(refundableAmount);
        afterSale.setRefundAmount(request.getApplyRefundAmount());
        
        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        afterSale.setApplyTime(now);
        afterSale.setCreateTime(now);
        afterSale.setModTime(now);
        
        return afterSale;
    }
}



