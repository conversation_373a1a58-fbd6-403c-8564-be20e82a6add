package com.jsrxjt.mobile.biz.order.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.order.dto.request.SettleProductDTO;
import com.jsrxjt.mobile.api.order.dto.response.GiftTicketResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.SettleResponseDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.biz.order.CalculatorCaseService;
import com.jsrxjt.mobile.biz.order.GiftTicketValidationService;
import com.jsrxjt.mobile.domain.inventory.service.SkuReleaseInventoryService;
import com.jsrxjt.mobile.domain.order.entity.OrderAmountResult;
import com.jsrxjt.mobile.domain.order.entity.PurchaseInfo;
import com.jsrxjt.mobile.domain.order.service.CalculateAmountService;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.product.service.ProductItemDomainService;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.product.service.validator.ProductInventoryValidationService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 产品结算服务实现
 * 
 * <AUTHOR> Fengping
 * @since 2025/6/25
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class CalculatorCaseServiceImpl implements CalculatorCaseService {

    private final ProductItemDomainService productItemDomainService;
    private final CalculateAmountService calculateAmountService;

    private final ProductInventoryValidationService productInventoryValidationService;

    private final SkuReleaseInventoryService skuReleaseInventoryService;

    private final ProductSkuSellRegionService productSkuSellRegionService;

    private final RegionRepository regionRepository;

    private final GiftTicketValidationService giftTicketValidationService;

    @Override
    public SettleResponseDTO calculateAmount(SettleProductDTO dto) {
        log.info("开始产品结算，客户ID：{}, 产品类型：{}, SPU ID：{}, SKU ID：{}, 数量：{}",
                dto.getCustomerId(), dto.getProductType(), dto.getProductSpuId(), dto.getProductSkuId(),
                dto.getQuantity());

        // 1. 参数校验
        validateSettleRequest(dto);

        // 2. 构建统一商品信息
        ProductItemId productItemId = ProductItemId.of(
                dto.getProductSpuId(),
                dto.getProductSkuId(),
                dto.getProductType());
        ProductItem productItem = buildProductItem(productItemId);

        // 3. 可售性校验
        validateProductAvailability(productItem);

        // 区域可售性
        RegionEntity currentRegion = regionRepository.getCurrentRegion(dto.getCustomerId());
        Integer regionId = currentRegion.getId();
        if (!productSkuSellRegionService.isSellableInRegion(dto.getProductSpuId(),dto.getProductSkuId(),
                productItem.getProductTypeEnum()
                , regionId)) {
            throw new BizException("商品在当前区域不可售");

        }
        // 4. 库存和放量校验
        productInventoryValidationService.validateInventory(productItem, dto.getQuantity());

        // 放量校验
        if (productItem.hasReleaseInventoryProp()) {
            skuReleaseInventoryService.checkAndGetSkuReleaseInventories(productItemId, regionId, dto.getQuantity());
        }



        // 5. 单月购买数量限制校验
        Integer monthlyPurchaseQuantity = calculateAmountService.getMonthlyPurchaseQuantity(dto.getCustomerId(),
                productItemId);
        validateMonthlyLimit(productItem, monthlyPurchaseQuantity, dto.getQuantity());

        // 6. 补充应用的商品价格（如果需要）
        supplementAppProductPrice(productItem, dto);

        // 7. 校验和处理赠送券信息
        List<GiftTicketInfo> giftTickets = giftTicketValidationService.validateAndProcessGiftTickets(
                productItemId, dto.getGiftTickets(), dto.getQuantity());
        List<GiftTicketResponseDTO> giftTicketsDTO = giftTickets.stream()
                .map(giftTicketInfo -> {
                    GiftTicketResponseDTO ticketResponseDTO = new GiftTicketResponseDTO();
                    ticketResponseDTO.setTicketId(giftTicketInfo.getTicketId());
                    ticketResponseDTO.setTicketName(giftTicketInfo.getTicketName());
                    ticketResponseDTO.setSpecPicUrl(giftTicketInfo.getSpecPicUrl());
                    ticketResponseDTO.setTicketNum(giftTicketInfo.getTicketNum());
                    return ticketResponseDTO;
                })
                .toList();

        // 8. 获取订单金额信息
        OrderAmountResult orderAmountResult = calculateOrderAmount(productItem, dto, regionId);

        // 9. 构建结算响应
        SettleResponseDTO response = buildSettleResponse(orderAmountResult, productItem, monthlyPurchaseQuantity, dto.getQuantity());
        response.setGiftTickets(giftTicketsDTO);

        log.info("产品结算完成，客户ID：{}, 订单金额：{}, 支付金额：{}",
                dto.getCustomerId(), response.getOrderAmount(), response.getPayAmount());

        return response;
    }

    /**
     * 参数校验
     */
    private void validateSettleRequest(SettleProductDTO dto) {
        if (dto.getProductType() == null) {
            throw new BizException("产品类型不能为空");
        }
        if (dto.getProductSpuId() == null || dto.getProductSpuId() <= 0) {
            throw new BizException("产品SPU ID不能为空");
        }
        if (dto.getQuantity() == null || dto.getQuantity() <= 0) {
            throw new BizException("购买数量必须大于0");
        }
    }

    /**
     * 构建统一商品信息
     */
    private ProductItem buildProductItem(ProductItemId productItemId) {

        try {
            return productItemDomainService.buildProductItem(productItemId);
        } catch (Exception e) {
            log.error("构建商品信息失败，productItemId：{}", productItemId, e);
            throw new BizException("商品信息不存在或已下架");
        }
    }

    /**
     * 可售性校验
     */
    private void validateProductAvailability(ProductItem productItem) {
        if (!productItem.isAvailable()) {
            throw new BizException("商品不可售");
        }
    }

    /**
     * 单月购买数量限制校验
     */
    private void validateMonthlyLimit(ProductItem productItem, Integer monthlyPurchaseQuantity, Integer quantity) {
        if (productItem.hasLimitNumPerMonth() && (monthlyPurchaseQuantity + quantity > productItem.getLimitNumPerMonth())) {
                throw new BizException("单月购买数量超出限制,限购" + productItem.getLimitNumPerMonth() + "张，本月已购买"
                        + monthlyPurchaseQuantity + "张");

        }

    }

    /**
     * 补充应用的商品价格
     */
    private void supplementAppProductPrice(ProductItem productItem, SettleProductDTO dto) {
        if (Objects.equals(productItem.getProductType(), ProductTypeEnum.APP.getType())
                && productItem.getPlatformPrice() == null) {
            productItem.setPlatformPrice(dto.getAppProductPrice());
        }
    }

    /**
     * 计算订单金额
     */
    private OrderAmountResult calculateOrderAmount(ProductItem productItem, SettleProductDTO dto, Integer regionId) {

        PurchaseInfo purchaseInfo = PurchaseInfo.of(
                productItem,
                dto.getQuantity(),
                regionId,
                dto.getCustomerId());

        return calculateAmountService.calculateOrderAmount(purchaseInfo);
    }

    /**
     * 构建结算响应
     */
    private SettleResponseDTO buildSettleResponse(OrderAmountResult orderAmountResult, ProductItem productItem,
            Integer monthlyPurchaseQuantity,Integer quantity) {
        SettleResponseDTO response = new SettleResponseDTO();

        // 原价销售价金额
        response.setTotalSellAmount(orderAmountResult.getTotalSellAmount());

        // 加点手续费
        response.setTotalServiceFee(orderAmountResult.getTotalServiceFee());
        response.setSingleGoodsServiceFee(orderAmountResult.getSingleGoodsServiceFee());
        response.setServiceFeeRate(orderAmountResult.getServiceFeeRate());

        // 超额相关
        response.setTotalExceedFee(orderAmountResult.getTotalExceedFee());
        response.setExceedAmount(orderAmountResult.getExceedAmount());
        response.setExceedFee(orderAmountResult.getExceedFee());
        response.setExceedPercentage(orderAmountResult.getExceedPercentage());
        response.setIsExceedAllowed(orderAmountResult.getIsExceedAllowed());

        // 订单金额
        response.setOrderAmount(orderAmountResult.getOrderAmount());
        response.setPayAmount(orderAmountResult.getPayAmount());

        // 限额和已购金额
        response.setLimitAmount(orderAmountResult.getLimitAmount());
        response.setPurchasedAmount(orderAmountResult.getPurchasedAmount());

        // 优惠相关
        response.setTotalDiscountAmount(orderAmountResult.getTotalDiscountAmount());
        response.setSingleGoodsDiscountAmount(orderAmountResult.getSingleGoodsDiscountAmount());

        // 折扣信息从商品的促销信息中获取
        if (productItem.getPromotionSkuInfo() != null && productItem.getPromotionSkuInfo().getDiscount() != null) {
            response.setDiscount(productItem.getPromotionSkuInfo().getDiscount());
        }

        // 购买数量相关
        response.setMonthlyPurchaseQuantity(monthlyPurchaseQuantity);
        response.setSkuLimitNumPerMonth(productItem.getLimitNumPerMonth());

        SettleResponseDTO.ProductInfo productInfo = buildProductInfo(productItem, quantity);
        response.setProductInfo(productInfo);
        return response;
    }

    private SettleResponseDTO.ProductInfo buildProductInfo(ProductItem productItem, Integer quantity) {
        SettleResponseDTO.ProductInfo productInfo = new SettleResponseDTO.ProductInfo();
        productInfo.setSpuId(productItem.getSpuId());
        productInfo.setSkuId(productItem.getSkuId());
        productInfo.setProductName(productItem.getProductName());
        productInfo.setProductLogo(productItem.getProductLogo());
        productInfo.setProductType(productItem.getProductType());
        productInfo.setFlatProductType(productItem.getFlatProductType());
        productInfo.setQuantity(quantity);
        productInfo.setFaceAmount(productItem.getFaceAmount());
        productInfo.setPlatformPrice(productItem.getPlatformPrice());

        // 套餐产品：设置子产品信息
        if (Objects.equals(productItem.getProductType(), ProductTypeEnum.PACKAGE.getType())) {
            productInfo.setSubProductList(buildPackageSubProductList(productItem));
        }
        return productInfo;
    }

    /**
     * 构建套餐子产品信息列表
     */
    private List<SettleResponseDTO.PackageSubProductInfo> buildPackageSubProductList(ProductItem productItem) {
        List<PackageSubSkuEntity> subSkus = productItem.getPackageSubSkus();
        if (subSkus == null || subSkus.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为响应DTO
        return subSkus.stream()
                .map(subSku -> {
                    SettleResponseDTO.PackageSubProductInfo subProductInfo = new SettleResponseDTO.PackageSubProductInfo();
                    subProductInfo.setAmountName(subSku.getAmountName());
                    subProductInfo.setPackageCouponNum(subSku.getPackageCouponNum());
                    subProductInfo.setAmount(subSku.getAmount());
                    return subProductInfo;
                })
                .toList();
    }
}
