package com.jsrxjt.mobile.domain.order.service.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description: 展码付推单订单信息构建
 * @Author: shenyue
 * @Date: 2023/9/27
 */
@Component
@Slf4j
public class ThirdHomeScanOrderInfoBuilder extends DefaultOrderInfoBuilder {

    public ThirdHomeScanOrderInfoBuilder(BusinessIdGenerator businessIdGenerator, ConfigRepository configRepository) {
        super(businessIdGenerator, configRepository);
    }
    
    @Override
    public void supplementOrderInfo(OrderInfoEntity orderInfo, CreateOrderDTO request) {
        log.info("补充第三方推单订单信息，订单号：{}", orderInfo.getOrderNo());
        
        // 补充第三方特有信息
        if (StrUtil.isNotBlank(request.getExternalOrderNo())) {
            orderInfo.setExternalOrderNo(request.getExternalOrderNo());
        }
        
        if (StrUtil.isNotBlank(request.getThirdId())) {
            orderInfo.setThirdId(request.getThirdId());
        }
        
        if (StrUtil.isNotBlank(request.getExternalShopId())) {
            orderInfo.setExternalShopId(request.getExternalShopId());
        }
        
        if (StrUtil.isNotBlank(request.getExternalShopUserId())) {
            orderInfo.setExternalShopUserId(request.getExternalShopUserId());
        }
        
        if (StrUtil.isNotBlank(request.getTradeNo())) {
            orderInfo.setTradeNo(request.getTradeNo());
        }
        if (StrUtil.isNotBlank(request.getRemark())) {
            orderInfo.setTradeNo(request.getTradeNo());
        }
        if(request.getPayExpireTimestamp() != null) {
            orderInfo.setPayExpireTimestamp(request.getPayExpireTimestamp());
        }
        if(StrUtil.isNotBlank(request.getRemark())) {
            orderInfo.setRemark(request.getRemark());
        }
        
        // 设置特定的订单渠道
        orderInfo.setOrderChannel(OrderChannelEnum.OFFLINE_SCAN.getCode());
        
        log.info("第三方推单订单信息补充完成，外部订单号：{}", orderInfo.getExternalOrderNo());
    }
} 