package com.jsrxjt.mobile.domain.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 子SKU订单实体类
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class SubSkuOrderEntity {
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 父订单项ID
     */
    private Long orderItemId;
    
    /**
     * 父订单号
     */
    private String orderNo;
    
    /**
     * 子sku订单号
     */
    private String subOrderNo;
    
    /**
     * 产品类型 2 套餐
     */
    private Integer productType;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * spuId
     */
    private Long spuId;
    
    /**
     * skuId
     */
    private Long skuId;
    
    /**
     * 子skuId
     */
    private Long subSkuId;
    
    /**
     * 卡管等第三方数据的id
     */
    private String outerId;
    
    /**
     * 面值名称
     */
    private String faceName;
    
    /**
     * 面值
     */
    private BigDecimal faceAmount;

    /**
     * 子SKU图片
     */
    private String subSkuImg;
    
    /**
     * 记录的成本价
     */
    private BigDecimal costPrice;
    
    /**
     * 与第三方结算价
     */
    private BigDecimal settlePrice;
    
    /**
     * 充值账号
     */
    private String rechargeAccount;
    
    /**
     * 外部订单号(去卡管下单对应卡管的订单号)
     */
    private String externalOrderNo;
    
    /**
     * 系统卡券类型：1:普通卡券 3:品诺
     */
    private Integer couponType;
    
    /**
     * 子sku的购买数量(套餐的购买数量*套装子商品数量)
     */
    private Integer quantity;
    
    /**
     * 卡券类型 101:平台自发券 201:卡密+卡号 202:卡号或卡密 203:卡号+卡密+校验码 301:链接类 302:链接+验证码 303:卡号+短链接+验证码 304:卡号+短链接
     */
    private Integer outCheckType;
    
    /**
     * 自定义核销类型 1:面值+兑换码 2:面值+兑换码+一维二维码 3:面值+卡号+一维二维码 4:面值+卡号卡密 5:面值+卡号卡密+兑换码 6:面值+卡号卡密+一维二维码 7:面值+卡密+一维二维码 8:面值+链接 9:自发券(面值+提货券形式)
     */
    private Integer checkType;
    
    /**
     * 子发货状态 0-待发货 1-发货中 2-已发货 3-发货失败
     */
    private Integer subSkuDeliveryStatus;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime modTime;
    
    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer delFlag;
}