package com.jsrxjt.mobile.biz.payment.service.impl;

import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleOperationTypeEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.RefundStatusEnum;
import com.jsrxjt.mobile.api.payment.dto.request.PaymentCallbackRequestDTO;
import com.jsrxjt.mobile.api.payment.dto.response.PaymentCallbackHandlerDTO;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.payment.service.PaymentCallbackCaseService;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleEntity;
import com.jsrxjt.mobile.domain.order.entity.AfterSaleLogEntity;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.AfterSaleLogService;
import com.jsrxjt.mobile.domain.order.service.AfterSaleService;
import com.jsrxjt.mobile.domain.payment.messaging.PaymentSuccessMessageProducer;
import com.jsrxjt.mobile.domain.payment.strategy.PaymentCallbackHandler;
import com.jsrxjt.mobile.domain.payment.types.PaymentSuccessMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付回调业务服务实现（简化版 - 只更新订单表）
 * 
 * <AUTHOR> Fengping
 * @since 2025/6/17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentCallbackCaseServiceImpl implements PaymentCallbackCaseService {

    private final List<PaymentCallbackHandler> callbackHandlers;
    private final AfterSaleService afterSaleService;
    private final AfterSaleLogService afterSaleLogService;
    private final AfterSaleLogRepository afterSaleLogRepository;
    private final OrderRepository orderRepository;
    private final AfterSaleRepository afterSaleRepository;
    private final DistributedLock distributedLock;
    private final PaymentSuccessMessageProducer paymentSuccessMessageProducer;


    private static final String PAYMENT_LOCK_PREFIX = "payment_callback_lock:";

    @Override
    public void handlePaymentCallback(PaymentCallbackRequestDTO request) {
        log.info("接收到支付回调，开始处理支付渠道:{}的通知", request.getPaymentChannel());

        // 1. 预处理回调 验证签名，获取回调参数
        PaymentCallbackHandlerDTO callbackDTO = handleNotify(request);
        String orderNo = callbackDTO.getOrderNo();
        String lockKey = PAYMENT_LOCK_PREFIX + orderNo;

        log.info("接收到支付回调，预处理成功，订单号：{}，支付渠道：{}，交易号：{}",
                orderNo, request.getPaymentChannel(), callbackDTO.getTradeNo());

        // 加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 15, TimeUnit.SECONDS);
            if (!lockAcquired) {
                throw new BizException("获取支付回调锁失败，订单号：" + orderNo);
            }

            // 2. 查询订单信息
            OrderInfoEntity orderInfo = orderRepository.findByOrderNo(orderNo);
            if (orderInfo == null) {
                throw new BizException("订单不存在，订单号：" + orderNo);
            }

            // 3. 检查订单状态，实现幂等（这里不抛异常，直接忽略后面的处理）
            // 超时取消如果收到回调 也可以反转状态，避免超时和支付并发的情况
            if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(orderInfo.getOrderStatus())
                    && !OrderStatusEnum.TIMEOUT_CANCEL.getCode().equals(orderInfo.getOrderStatus())) {
                log.info("订单状态不是待支付，幂等处理，订单号：{}，当前状态：{}",
                        orderNo, orderInfo.getOrderStatus());
                return; // 幂等处理，直接返回
            }

            // 4. 过滤非支付成功状态
            if (!Objects.equals("SUCCESS", callbackDTO.getPayStatus())) {
                log.info("当前支付状态是：{} 不是支付成功，直接返回",callbackDTO.getPayStatus());
                return;
            }


            // 5. 校验支付金额
            if (!validatePaymentAmount(callbackDTO, orderInfo)) {
                throw new BizException(String.format("支付金额校验失败，订单号：%s，回调金额：%s，订单金额：%s",
                        orderNo, callbackDTO.getPaymentAmount(), orderInfo.getPaymentAmount()));
            }

            // 5. 过滤非支付成功状态
            if (!Objects.equals("SUCCESS", callbackDTO.getPayStatus())) {
                log.info("当前支付状态是：{} 不是支付成功，直接返回",callbackDTO.getPayStatus());
                return;
            }

            // 6. 更新订单支付信息
            updateOrderPaymentInfo(orderInfo, callbackDTO);


            // 7. 发送支付成功消息
            sendPaymentSuccessMessage(orderInfo, callbackDTO);

            log.info("支付回调处理成功，订单号：{}", orderNo);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("获取分布式锁被中断，订单号：" + orderNo);
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    @Override
    public void handleRefundCallback(PaymentCallbackRequestDTO request) {
        log.info("接收到退款回调，开始处理支付渠道:{}的通知", request.getPaymentChannel());

        // 1. 预处理回调 验证签名，获取回调参数
        PaymentCallbackHandlerDTO callbackDTO = handleRefundNotify(request);
        String orderNo = callbackDTO.getOrderNo();
        String refundNo = callbackDTO.getRefundNo();
        String lockKey = PAYMENT_LOCK_PREFIX + "refund:" + orderNo + ":" + refundNo;

        log.info("接收到退款回调，预处理成功，订单号：{}，退款单号：{}，支付渠道：{}，交易号：{}",
                orderNo, refundNo, request.getPaymentChannel(), callbackDTO.getTradeNo());

        // 加分布式锁
        boolean lockAcquired = false;
        try {
            lockAcquired = distributedLock.tryLock(lockKey, 10, TimeUnit.SECONDS);
            if (!lockAcquired) {
                throw new BizException("获取退款回调锁失败，订单号：" + orderNo + "，退款单号：" + refundNo);
            }

            // 2. 查询订单信息和售后单信息
            OrderInfoEntity orderInfo = orderRepository.findByOrderNo(orderNo);
            if (orderInfo == null) {
                throw new BizException("订单不存在，订单号：" + orderNo);
            }

            AfterSaleEntity afterSale = afterSaleRepository.findByAfterSaleNoAndCustomerId(refundNo, orderInfo.getCustomerId());
            if (afterSale == null) {
                throw new BizException("售后单不存在，订单号：" + orderNo + "，退款单号：" + refundNo);
            }

            // 3. 检查售后单状态，售后单不是待退款或退款中
            if(!RefundStatusEnum.NOT_REFUNDED.getCode().equals(afterSale.getRefundStatus())
                    && !RefundStatusEnum.REFUNDING.getCode().equals(afterSale.getRefundStatus())) {
                log.info("售后单状态不是待退款或退款中，幂等处理，订单号：{}，退款单号：{}，当前状态：{}",
                        orderNo, refundNo, afterSale.getAfterSaleStatus());
                return;
            }



            // 4. 校验退款金额
            if (!validateRefundAmount(callbackDTO, afterSale)) {
                throw new BizException(String.format("退款金额校验失败，订单号：%s，退款单号：%s，回调金额：%s，售后金额：%s",
                        orderNo, refundNo, callbackDTO.getRefundAmount(), afterSale.getRefundAmount()));
            }

            // 5. 过滤非退款成功状态
            if ("NONE".equals(callbackDTO.getRefundStatus())) {
                log.info("当前退款状态是：{} 不是退款成功，直接返回", callbackDTO.getRefundStatus());
                return;
            }

            // 6. 更新售后单退款信息
            updateAfterSaleRefundInfo(afterSale, callbackDTO);

            // 7. 记录售后日志
            AfterSaleLogEntity afterSaleLog = afterSaleLogService.createAfterSaleLog(
                    afterSale,
                    AfterSaleOperationTypeEnum.REFUND_SUCCESS.getCode(),
                    "退款成功，售后完成",
                    "系统",
                    1L);
            afterSaleLogRepository.save(afterSaleLog);

            // 8. 同步更新订单售后状态
            afterSale.setAfterSaleStatus(AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode());
            afterSaleService.syncOrderAfterSaleStatus(afterSale);


            log.info("退款回调处理成功，订单号：{}，退款单号：{}", orderNo, refundNo);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("获取分布式锁被中断，订单号：" + orderNo + "，退款单号：" + refundNo);
        } finally {
            if (lockAcquired) {
                distributedLock.unLock(lockKey);
            }
        }
    }

    /**
     * 预处理通知 - 验证签名和解析参数
     */
    private PaymentCallbackHandlerDTO handleNotify(PaymentCallbackRequestDTO request) {
        Map<String, PaymentCallbackHandler> handlerMap = callbackHandlers.stream()
                .collect(Collectors.toMap(
                        handler -> handler.supportedChannel().getCode(),
                        Function.identity()));
        if(log.isDebugEnabled()) {
            log.debug("支付渠道处理映射：{}", handlerMap);
        }

        PaymentCallbackHandler handler = handlerMap.get(request.getPaymentChannel().getCode());
        if (handler == null) {
            log.error("不支持的支付渠道：{}", request.getPaymentChannel());
            throw new BizException("不支持的支付渠道：" + request.getPaymentChannel());
        }

        return handler.handleNotify(request);
    }

    /**
     * 校验支付金额
     */
    private boolean validatePaymentAmount(PaymentCallbackHandlerDTO handlerDTO, OrderInfoEntity orderInfo) {
        BigDecimal callbackAmount = handlerDTO.getPaymentAmount();
        BigDecimal orderAmount = orderInfo.getPaymentAmount();

        return callbackAmount.compareTo(orderAmount) == 0;
    }

    /**
     * 更新订单支付信息
     */
    private void updateOrderPaymentInfo(OrderInfoEntity orderInfo, PaymentCallbackHandlerDTO request) {
        OrderInfoEntity updateParam = new OrderInfoEntity();
        // 设置查询条件
        updateParam.setId(orderInfo.getId());
        updateParam.setCustomerId(orderInfo.getCustomerId());

        // 更新订单状态为待发货
        updateParam.setOrderStatus(OrderStatusEnum.IN_PROGRESS.getCode());
        updateParam.setPaymentStatus(PaymentStatusEnum.PAID.getCode());
        updateParam.setDeliveryStatus(DeliveryStatusEnum.UNDELIVERED.getCode().intValue());

        // 更新支付相关信息
        updateParam.setTradeNo(request.getTradeNo());
        updateParam.setPaymentTime(request.getPaymentTime());
        updateParam.setModTime(LocalDateTime.now());
        OrderStatusEnum orderStatus = OrderStatusEnum.getByCode(orderInfo.getOrderStatus());
        orderRepository.updateOrderCheckCurrentStatus(updateParam,PaymentStatusEnum.UNPAID,orderStatus);
        log.info("订单支付信息更新成功，订单号：{}，状态：{}，交易号：{}",
                orderInfo.getOrderNo(), OrderStatusEnum.IN_PROGRESS.getCode(), request.getTradeNo());
    }

    /**
     * 发送支付成功消息
     */
    private void sendPaymentSuccessMessage(OrderInfoEntity orderInfo, PaymentCallbackHandlerDTO callbackDTO) {
        // 构建支付成功消息
        PaymentSuccessMessage message = new PaymentSuccessMessage(orderInfo);

        // 发送到消息队列
        paymentSuccessMessageProducer.send(message);

        log.info("支付成功消息发送完成，订单号：{}，客户ID：{}，交易号：{}",
                orderInfo.getOrderNo(), orderInfo.getCustomerId(), callbackDTO.getTradeNo());
    }

    /**
     * 预处理退款通知 - 验证签名和解析参数
     */
    private PaymentCallbackHandlerDTO handleRefundNotify(PaymentCallbackRequestDTO request) {
        Map<String, PaymentCallbackHandler> handlerMap = callbackHandlers.stream()
                .collect(Collectors.toMap(
                        handler -> handler.supportedChannel().getCode(),
                        Function.identity()));

        PaymentCallbackHandler handler = handlerMap.get(request.getPaymentChannel().getCode());
        if (handler == null) {
            log.error("不支持退款渠道：{}", request.getPaymentChannel());
            throw new BizException("不支持的退款渠道：" + request.getPaymentChannel());
        }

        return handler.handleRefundNotify(request);
    }

    /**
     * 校验退款金额
     */
    private boolean validateRefundAmount(PaymentCallbackHandlerDTO handlerDTO, AfterSaleEntity afterSale) {
        BigDecimal callbackAmount = handlerDTO.getRefundAmount();
        BigDecimal afterSaleAmount = afterSale.getRefundAmount();

        return callbackAmount.compareTo(afterSaleAmount) == 0;
    }

    /**
     * 更新售后单退款信息
     */
    private void updateAfterSaleRefundInfo(AfterSaleEntity afterSale, PaymentCallbackHandlerDTO callbackDTO) {
        AfterSaleEntity updateParam = new AfterSaleEntity();
        updateParam.setId(afterSale.getId());
        updateParam.setCustomerId(afterSale.getCustomerId());
        // 更新退款状态为成功
        updateParam.setRefundStatus(RefundStatusEnum.REFUND_SUCCESS.getCode());
        updateParam.setRefundTime(callbackDTO.getRefundTime());
        // 更新售后单状态为售后完成
        updateParam.setAfterSaleStatus(AfterSaleStatusEnum.AFTER_SALE_COMPLETED.getCode());
        afterSaleRepository.update(updateParam);
        
        log.info("售后单退款信息更新成功，订单号：{}，退款单号：{} 更新的售后状态 {} 更新的退款状态{}",
                afterSale.getOrderNo(), afterSale.getAfterSaleNo(),updateParam.getAfterSaleStatus(),updateParam.getRefundStatus());
    }

}
