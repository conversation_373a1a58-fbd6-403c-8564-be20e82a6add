package com.jsrxjt.mobile.api.distribution;

/**
 * 分销渠道类型枚举类
 * 
 * <AUTHOR>
 * @since 2025/3/24
 **/
public enum DistChannelType {
    /**
     * 美团
     */
    MEITUAN,

    /**
     * 叮咚买菜
     */
    DINGDONG,

    /**
     * M会员店
     */
    MVIP,

    /**
     * 大润发
     */
    RTMART,

    /**
     * 中免日上
     */
    CDFSUNRISE,
    /**
     * 团油
     */
    TUANYOU,

    /**
     * 西橙影视
     */
    XICHENG,
    /**
     * 视听
     */
    SHITING,
    /**
     * 永辉超市
     */
    YONGHUI,
    /**
     * 卫岗
     */
    WEIGANG,

    /**
     * 食行生鲜
     */
    SHIXING,
    /**
     * 同程商旅
     */
    TONGCHENG,

    /**
     * 清美
     */
    QINGMEI,
    /**
     * 水韵江苏
     */
    SHUIYUN,
    /**
     * 物美
     */
    WUMEI,
    /**
     * 物美线下
     */
    WUMEI_OFFLINE,

    /**
     * 本地生活
     */
    LOCALLIFE;

    /**
     * 通过编码获取分销渠道类型
     *
     * @param code 渠道编码
     * @return 分销渠道类型
     */
    public static DistChannelType getByCode(String code) {
        for (DistChannelType channelType : DistChannelType.values()) {
            if (channelType.name().equals(code)) {
                return channelType;
            }
        }
        return null;
    }

}
