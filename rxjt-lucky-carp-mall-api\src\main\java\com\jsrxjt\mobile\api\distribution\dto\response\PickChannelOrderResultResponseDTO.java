package com.jsrxjt.mobile.api.distribution.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 支付结果通知
 * @Author: zy
 * @Date: 2025-06-04 10:14
 * @Version: 1.0
 */
@Data
@Schema(description = "支付结果通知")
public class PickChannelOrderResultResponseDTO {

    @Schema(description = "是否支付成功")
    private Byte isResult;

    @Schema(description = "平台订单流水号")
    private String orderNo;

    @Schema(description = "支付交易流水号")
    private String tradeNo;

}
