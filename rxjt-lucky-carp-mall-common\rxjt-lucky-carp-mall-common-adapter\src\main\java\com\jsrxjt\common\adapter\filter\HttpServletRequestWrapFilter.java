package com.jsrxjt.common.adapter.filter;

import com.jsrxjt.common.adapter.wrap.ReReadHttpServletRequestWrapper;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 *  RequestBodyWrapFilter
 *  重新封装HttpServletRequest,解决只能一次读取请求流的问题
 * <AUTHOR> Fengping
 * 2023/3/14 15:58
 * 
 **/
public class HttpServletRequestWrapFilter implements Filter {


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        ServletRequest requestWrapper = null;
        if (request instanceof HttpServletRequest httpServletRequest) {
            String contentType = httpServletRequest.getContentType() == null ? "" : httpServletRequest.getContentType();
            // 如果不是文件上传
            if ( !contentType.equals(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                // 重新生成ServletRequest  这个新的 ServletRequest 获取流时会将流的数据重写进流里面
                requestWrapper = new ReReadHttpServletRequestWrapper((HttpServletRequest) request);
            }
        }
        if (requestWrapper == null) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(requestWrapper, response);
        }


    }

}
