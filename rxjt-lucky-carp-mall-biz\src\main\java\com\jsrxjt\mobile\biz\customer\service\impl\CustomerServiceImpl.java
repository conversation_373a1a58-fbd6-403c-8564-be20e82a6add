package com.jsrxjt.mobile.biz.customer.service.impl;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.CommonUtils;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.advertisement.dto.response.AdvertisementInfoDTO;
import com.jsrxjt.mobile.api.advertisement.types.AdvertisementTypeEnum;
import com.jsrxjt.mobile.api.config.types.SystemConfigTypeEnum;
import com.jsrxjt.mobile.api.contentcenter.types.ContentcenterTypeEnum;
import com.jsrxjt.mobile.api.customer.request.*;
import com.jsrxjt.mobile.api.customer.response.*;
import com.jsrxjt.mobile.api.customer.types.CustomerCardTypeEnum;
import com.jsrxjt.mobile.api.customer.types.CustomerLoginTypeEnum;
import com.jsrxjt.mobile.api.customer.types.CustomerPhoneChangeMethod;
import com.jsrxjt.mobile.api.customer.types.VerificationCodeSendTypeEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.sms.SmsSendService;
import com.jsrxjt.mobile.domain.advertisement.entity.AdvertisementEntity;
import com.jsrxjt.mobile.domain.advertisement.service.AdvertisementService;
import com.jsrxjt.mobile.domain.config.repository.ConfigRepository;
import com.jsrxjt.mobile.domain.contentcenter.service.ContentRegionService;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.entity.CustomerPaySortEntity;
import com.jsrxjt.mobile.domain.customer.entity.CustomerPhoneChangeRecordEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerPaySortRepository;
import com.jsrxjt.mobile.domain.customer.repository.CustomerPhoneChangeRecordRepository;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.customer.repository.PaymentMethodRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.message.repository.MessageDetailRepository;
import com.jsrxjt.mobile.domain.message.repository.MessageServeRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.payment.gateway.OnlinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.CardInfoRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.CheckFindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.request.FindCardBalanceRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.CheckFindCardBalanceResponse;
import com.jsrxjt.mobile.domain.payment.gateway.response.FindCardBalanceResponse;
import com.jsrxjt.mobile.domain.riskcontrol.entity.ProductRiskControlCompanyEntity;
import com.jsrxjt.mobile.domain.riskcontrol.repository.ProductRiskControlCompanyRepository;
import com.jsrxjt.mobile.domain.rxmember.gateway.RxMemberGateway;
import com.jsrxjt.mobile.domain.rxmember.request.RxMemberRequest;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberCardDetail;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberCardResponse;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberLoginResponse;
import com.jsrxjt.mobile.domain.rxmember.response.RxMemberResponse;
import com.jsrxjt.mobile.domain.thirdcard.gateway.ThirdCardGateWay;
import com.jsrxjt.mobile.domain.wechat.gateway.WechatGateway;
import com.jsrxjt.mobile.domain.wechat.request.WechatLoginRequest;
import com.jsrxjt.mobile.domain.wechat.request.WechatUserInfoRequest;
import com.jsrxjt.mobile.domain.wechat.response.WechatLoginResponse;
import com.jsrxjt.mobile.domain.wechat.response.WechatUserInfoResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl implements CustomerService {

    /**
     * 验证码有效期 300s
     */
    private static final int VERIFICATION_CODE_EXPIRE_TIME = 300;

    /**
     * 卡号密码查询卡余额次数
     */
    private static final int CARD_BALANCE_DAILY_QUERY_MAX_COUNT = 3;

    @Value("${flq.user.default.avatar}")
    private String userDefaultAvatar;

    @Value("${flq.user.default.name}")
    private String userDefaultName;

    private final WechatGateway wechatGateway;

    private final RxMemberGateway rxMemberGateway;

    private final BusinessIdGenerator businessIdGenerator;

    private final CustomerRepository customerRepository;

    private final CustomerPaySortRepository customerPaySortRepository;

    private final ThirdCardGateWay thirdCardGateWay;

    private final ProductRiskControlCompanyRepository productRiskControlCompanyRepository;

    private final PaymentMethodRepository paymentMethodRepository;

    private final RedisUtil redisUtil;

    private final SmsSendService smsSendService;

    private final ConfigRepository configRepository;

    private final CustomerPhoneChangeRecordRepository customerPhoneChangeRecordRepository;

    private final MessageServeRepository messageServeRepository;

    private final MessageDetailRepository messageDetailRepository;

    private final OrderRepository orderRepository;

    private final AfterSaleRepository afterSaleRepository;

    private final AdvertisementService advertisementService;

    private final ContentRegionService contentRegionService;

    private final OnlinePaymentGateway onlinePaymentGateway;

    private final OrderDeliveryRepository orderDeliveryRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerResponse login(CustomerLoginRequest request) {
        switch (request.getLoginType()) {
            case 1:
                return wechatMiniProgramLogin(request);
            case 2:
                return wechatAppLogin(request);
            case 3:
                return phoneLogin(request);
            default:
                throw new BizException("不支持的登录方式");
        }
    }

    @Override
    public void sendVerificationCode(CustomerSendVerificationCodeRequest request) {
        // 根据不同类型获取手机号并验证
        String phone = getPhoneByCodeType(request);
        // 生成并发送验证码
        sendCodeToPhone(phone);
    }

    private String getPhoneByCodeType(CustomerSendVerificationCodeRequest request) {
        VerificationCodeSendTypeEnum codeType;
        try {
            codeType = VerificationCodeSendTypeEnum.fromCode(request.getCodeType());
        } catch (IllegalArgumentException e) {
            throw new BizException("不支持的验证码类型");
        }
        return switch (codeType) {
            case REGISTER -> validateAndGetRegisterPhone(request.getPhone());
            case LOGIN -> validateAndGetLoginPhone(request.getPhone());
            case CHANGE_INFO -> validateAndGetChangeInfoPhone(request.getCustomerId());
            case CHANGE_PHONE -> validateAndGetChangePhone(request.getPhone(), request.getCustomerId());
            case DELETE_ACCOUNT -> validateAndGetDeleteAccountPhone(request.getCustomerId());
        };
    }

    private String validateAndGetRegisterPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            throw new BizException("用户手机号码不能为空");
        }
        return phone;
    }

    private String validateAndGetLoginPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            throw new BizException("手机号码不能为空");
        }
        CustomerEntity customer = customerRepository.selectCustomerByMobile(phone);
        if (customer == null) {
            throw new BizException("该手机号未注册，请使用微信登录");
        }
        return customer.getPhone();
    }

    private String validateAndGetChangeInfoPhone(Long customerId) {
        if (customerId == null){
            throw new BizException("用户id不能为空");
        }
        CustomerEntity customer = customerRepository.selectCustomerById(customerId);
        if (customer == null) {
            throw new BizException("用户不存在");
        }
        return customer.getPhone();
    }

    private String validateAndGetChangePhone(String phone, Long customerId) {
        if (StringUtils.isBlank(phone)) {
            throw new BizException("用户手机号码不能为空");
        }
        if (customerId == null){
            throw new BizException("用户id不能为空");
        }
        // 校验用户手机号码更换次数
        String modPhoneMaxCountStr = configRepository.getValueByType(SystemConfigTypeEnum.MOD_PHONE_MAX_COUNT_TYPE);
        int modPhoneMaxCount = Integer.parseInt(modPhoneMaxCountStr);
        if (modPhoneMaxCount > 0) {
            long changePhoneCount = customerPhoneChangeRecordRepository.getPhoneChangeRecordCount(customerId);
            if (changePhoneCount >= modPhoneMaxCount) {
                throw new BizException("用户一年内手机号码更换次数已达上限");
            }
        }
        CustomerEntity customer = customerRepository.selectCustomerByMobile(phone);
        if (customer != null) {
            throw new BizException("手机号码已存在");
        }
        return phone;
    }

    private String validateAndGetDeleteAccountPhone(Long customerId) {
        if (customerId == null){
            throw new BizException("用户id不能为空");
        }
        CustomerEntity customer = customerRepository.selectCustomerById(customerId);
        if (customer == null) {
            throw new BizException(Status.USER_NOT_EXIST);
        }
        String deleteAccountMaxCountStr = configRepository.getValueByType(SystemConfigTypeEnum.DELETE_USER_MAX_COUNT_TYPE);
        int deleteAccountMaxCount = Integer.parseInt(deleteAccountMaxCountStr);
        if (deleteAccountMaxCount > 0) {
            long deleteAccountCount = customerRepository.getDeleteAccountCountInCurrentYear(customer.getUnionid());
            if (deleteAccountCount >= deleteAccountMaxCount) {
                throw new BizException(Status.OUT_MAX_DELETE_ACCOUNT_COUNT);
            }
        }
        return customer.getPhone();
    }

    private void sendCodeToPhone(String phone) {
        String redisKey = RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + phone;
        if (redisUtil.exists(redisKey)) {
            log.warn("验证码发送过于频繁，手机号: {}", phone);
            throw new BizException("请勿频繁发送验证码");
        }
        String verificationCode = generateVerificationCode(6);
        log.info("发送验证码，手机号码：{}, 验证码：{}", phone,  verificationCode);

        boolean sendResult = smsSendService.sendVerifyCode(phone, verificationCode);
        if (!sendResult) {
            log.error("验证码发送失败，手机号: {}", phone);
            throw new BizException("验证码发送失败");
        }
        redisUtil.set(redisKey, verificationCode, VERIFICATION_CODE_EXPIRE_TIME);
    }

    /**
     * 生成验证码
     * @param length
     * @return
     */
    private String generateVerificationCode(int length) {
        StringBuilder code = new StringBuilder();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }


    /**
     * 校验用户是否允许注册
     * @param unionid
     * @return
     */
    private void checkIsAllowedRegister(String unionid) {
        Date lastLoginOffTime = customerRepository.getLatestDeleteAccountTime(unionid);
        if (lastLoginOffTime != null){
            String reRegisterIntervalStr = configRepository.getValueByType(SystemConfigTypeEnum.REREGISTER_TIME_INTERVAL_TYPE);
            int interval = Integer.parseInt(reRegisterIntervalStr);
            if (interval > 0){
                if (lastLoginOffTime.after(DateUtils.addDays(new Date(), -interval))){
                    throw new BizException(Status.ERROR_REREGISTER_INTERVAL);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerResponse bindPhone(CustomerBindPhoneRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        if (customerEntity == null) {
            throw new BizException("该手机号未注册，请使用微信登录");
        }
        if (StringUtils.isNotBlank(customerEntity.getPhone())) {
            throw new BizException("该用户已绑定手机号码");
        }
        String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
        log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
        if (!request.getVerificationCode().equals(code)) {
            throw new BizException("短信验证码不一致");
        }
        customerEntity.setPhone(request.getPhone());
        customerRepository.updateCustomer(customerEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
        return buildCustomerResponse(customerEntity);
    }

    @Override
    public CustomerInfoResponse getCustomerInfo(Long customerId) {
        CustomerEntity customerEntity = getCustomerById(customerId);
        CustomerInfoResponse customerInfoResponse = new CustomerInfoResponse();
        BeanUtils.copyProperties(customerEntity,customerInfoResponse);
        // 获取用户卡包数量
        customerInfoResponse.setCouponNum(orderDeliveryRepository.getCustomerCouponPackageNum(customerId));
        // 获取用户优惠券数量
        // 未读消息数量 (客服消息数量+系统消息数量)
        customerInfoResponse.setUnReadMsgNum(messageServeRepository.countServeMsg(customerId) + messageDetailRepository.countSysMsg(customerId));
        //待付款订单数量
        customerInfoResponse.setPendingPaymentOrderNum(orderRepository.countOrderByStatus(customerId, OrderStatusEnum.PENDING_PAYMENT));
        //进行中订单数量
        customerInfoResponse.setInProcessOrderNum(orderRepository.countOrderByStatus(customerId, OrderStatusEnum.IN_PROGRESS));
        // 退款/售后订单数量 1-待审核 20-审核通过
        customerInfoResponse.setAfterSaleOrderNum(afterSaleRepository.countAfterSaleByStatus(customerId));
        return customerInfoResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCustomerInfo(CustomerEditRequest request) {
        CustomerEntity customerEntity = getCustomerById(request.getCustomerId());
        if (customerEntity == null) {
            throw new BizException("用户不存在");
        }
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        switch (request.getEditType()) {
            case 1 -> {
                if (StringUtils.isBlank(request.getUserUrl())) {
                    throw new BizException("请选择头像");
                }
                updateEntity.setUserUrl(request.getUserUrl());
            }
            case 2 -> {
                if (StringUtils.isBlank(request.getUserName())) {
                    throw new BizException("请输入用户昵称");
                }
                updateEntity.setUserName(request.getUserName());
            }
            case 3 ->
                // 性别可以设置为null
                updateEntity.setSex(request.getSex());
            case 4 -> {
                if (StringUtils.isBlank(request.getPhone()) || StringUtils.isBlank(request.getVerificationCode())) {
                    throw new BizException("手机号码或验证码不能为空");
                }
                String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
                log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
                if (!request.getVerificationCode().equals(code)) {
                    throw new BizException("短信验证码不一致");
                }
                updateEntity.setPhone(request.getPhone());
                redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhone());
            }
            case 5 -> {
                if (customerEntity.getBirthday() != null) {
                    throw new BizException("用户生日信息已存在，不支持编辑操作");
                } else {
                    updateEntity.setBirthday(request.getBirthday());
                }
            }
            default -> throw new BizException("不支持的编辑类型");
        }
        updateEntity.setModTime(new Date());
        updateEntity.setModId(customerEntity.getId());
        customerRepository.updateCustomerInfoById(updateEntity);
        if (request.getEditType() == 4){
            CustomerPhoneChangeRecordEntity customerPhoneChangeRecordEntity = new CustomerPhoneChangeRecordEntity();
            customerPhoneChangeRecordEntity.setCustomerId(customerEntity.getId());
            customerPhoneChangeRecordEntity.setOriginalPhone(customerEntity.getPhone());
            customerPhoneChangeRecordEntity.setNewPhone(request.getPhone());
            customerPhoneChangeRecordEntity.setChangeMethod(CustomerPhoneChangeMethod.USER_ACTIVE.getCode());
            customerPhoneChangeRecordEntity.setCreateTime(new Date());
            customerPhoneChangeRecordEntity.setCreateId(customerEntity.getId());
            customerPhoneChangeRecordRepository.savePhoneChangeRecord(customerPhoneChangeRecordEntity);
        }
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrModifyPassword(CustomerPasswordEditRequest request) {
        CustomerEntity customerEntity = getCustomerById(StpUtil.getLoginIdAsLong());
        if (customerEntity == null) {
            throw new BizException("用户不存在");
        }
        if (!CommonUtils.checkPassword(request.getNewPassword())){
            throw new BizException(Status.ERROR_PASSWORD_FORMAT);
        }
        if (!StringUtils.isEmpty(customerEntity.getPassword())){
            String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
            log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
            if (!request.getVerificationCode().equals(code)) {
                throw new BizException("短信验证码不一致");
            }
        }
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        updateEntity.setPassword(new BCryptPasswordEncoder().encode(request.getNewPassword()));
        updateEntity.setModId(customerEntity.getId());
        updateEntity.setModTime(new Date());
        customerRepository.updateCustomer(updateEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
    }

    @Override
    public void setFreePassword(CustomerSetFreePasswordRequest request) {
        CustomerEntity customerEntity = getCustomerById(request.getCustomerId());
        if (StringUtils.isEmpty(customerEntity.getPassword())){
            throw new BizException(Status.PAYMENT_PASSWORD_NOT_SET);
        }
        if (request.getOpenFreePayment() == 1 && StringUtils.isEmpty(request.getPassword())){
            throw new BizException("请输入支付密码");
        }
        // 校验用户支付密码
        if (request.getOpenFreePayment() == 1 && !new BCryptPasswordEncoder().matches(request.getPassword(), customerEntity.getPassword())) {
            throw new BizException(Status.PASSWORD_ERROR);
        }
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        updateEntity.setOpenPasswordFreePayment(request.getOpenFreePayment());
        updateEntity.setModId(customerEntity.getId());
        updateEntity.setModTime(new Date());
        customerRepository.updateCustomer(updateEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
    }

    private CustomerResponse wechatMiniProgramLogin(CustomerLoginRequest request) {
        if (StringUtils.isBlank(request.getWxCode())) {
            throw new BizException("微信code不能为空");
        }
        WechatLoginRequest wechatLoginRequest = new WechatLoginRequest();
        wechatLoginRequest.setWxCode(request.getWxCode());
        WechatLoginResponse wechatLoginResponse = wechatGateway.miniLogin(wechatLoginRequest);
        if (wechatLoginResponse == null){
            throw new BizException("微信小程序登录失败");
        }
        CustomerEntity customerEntity = processCustomer(wechatLoginResponse,CustomerLoginTypeEnum.MINI_LOGIN.getType());
        return buildCustomerResponse(customerEntity);
    }


    private CustomerResponse wechatAppLogin(CustomerLoginRequest request) {
        if (StringUtils.isBlank(request.getWxCode())) {
            throw new BizException("微信code不能为空");
        }
        WechatLoginRequest wechatLoginRequest = new WechatLoginRequest();
        wechatLoginRequest.setWxCode(request.getWxCode());
        WechatLoginResponse wechatLoginResponse = wechatGateway.appLogin(wechatLoginRequest);
        if (wechatLoginResponse == null){
            throw new BizException("微信app授权登录失败");
        }
        CustomerEntity customerEntity = processCustomer(wechatLoginResponse,CustomerLoginTypeEnum.APP_LOGIN.getType());
        return buildCustomerResponse(customerEntity);
    }

    private CustomerResponse phoneLogin(CustomerLoginRequest request) {
        if (StringUtils.isBlank(request.getPhoneCode()) || StringUtils.isBlank(request.getVerificationCode())) {
            throw new BizException("手机号码或验证码不能为空");
        }
        String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhoneCode());
        log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
        if (!request.getVerificationCode().equals(code)) {
            throw new BizException(Status.ERROR_VALIDATE_CODE);
        }
        CustomerEntity customerEntity = customerRepository.selectCustomerByMobile(request.getPhoneCode());
        if (customerEntity == null){
            throw new BizException("用户未注册，请使用微信登录");
        }
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setUnionid(customerEntity.getUnionid());
        if (StringUtils.isEmpty(customerEntity.getOpenid())){
            rxMemberRequest.setOpenid(customerEntity.getMiniOpenid());
        } else {
            rxMemberRequest.setOpenid(customerEntity.getOpenid());
        }
        RxMemberResponse rxMemberResponse = rxMemberGateway.login(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()){
            throw new BizException("登录失败");
        }
        customerEntity.setLoginTime(new Date());
        customerRepository.updateLoginInfo(customerEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + request.getPhoneCode());
        return buildCustomerResponse(customerEntity);
    }


    private CustomerEntity processCustomer(WechatLoginResponse wechatLoginResponse, int loginType){
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setUnionid(wechatLoginResponse.getUnionId());
        rxMemberRequest.setOpenid(wechatLoginResponse.getOpenId());
        RxMemberResponse rxMemberResponse = rxMemberGateway.login(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()){
            throw new BizException("登录失败");
        }
        RxMemberLoginResponse rxMemberLoginResponse = (RxMemberLoginResponse) rxMemberResponse.getData();
        CustomerEntity customerEntity = customerRepository.selectCustomerByUnionId(wechatLoginResponse.getUnionId());
        if (customerEntity == null){
            // 校验用户是否能再次注册
            checkIsAllowedRegister(wechatLoginResponse.getUnionId());
            customerEntity = new CustomerEntity();
            customerEntity.setId(businessIdGenerator.generateId());
            customerEntity.setVipId(Long.valueOf(rxMemberLoginResponse.getVipId()));
            customerEntity.setUnionid(rxMemberRequest.getUnionid());
            customerEntity.setStatus(NumberUtils.INTEGER_ONE);
            customerEntity.setLoginNum(NumberUtils.LONG_ONE);
            customerEntity.setLoginTime(new Date());
            customerEntity.setPhone(rxMemberLoginResponse.getMobile());
            if (loginType == CustomerLoginTypeEnum.APP_LOGIN.getType()){
                // 获取微信用户信息
                WechatUserInfoRequest userInfoRequest = new WechatUserInfoRequest();
                userInfoRequest.setAccessToken(wechatLoginResponse.getAccessToken());
                userInfoRequest.setOpenId(wechatLoginResponse.getOpenId());
                userInfoRequest.setAccessToken(wechatLoginResponse.getAccessToken());
                WechatUserInfoResponse userInfoResponse = wechatGateway.getAppUserInfo(userInfoRequest);
                if (userInfoResponse == null || StringUtils.isBlank(userInfoResponse.getNickName())) {
                    throw new BizException("获取微信用户信息失败");
                }
                customerEntity.setUserName(userInfoResponse.getNickName());
                customerEntity.setUserUrl(userInfoResponse.getHeadImgUrl());
                customerEntity.setSex(userInfoResponse.getSex());
                customerEntity.setOpenid(wechatLoginResponse.getOpenId());
            } else if (loginType == CustomerLoginTypeEnum.MINI_LOGIN.getType()){
                customerEntity.setMiniOpenid(wechatLoginResponse.getOpenId());
                customerEntity.setUserName(userDefaultName);
                customerEntity.setUserUrl(userDefaultAvatar);
            }
            customerRepository.saveCustomer(customerEntity);
        } else {
            boolean needUpdate = false;
            CustomerEntity updateEntity = new CustomerEntity();
            updateEntity.setId(customerEntity.getId());
            if (loginType == CustomerLoginTypeEnum.APP_LOGIN.getType() && customerEntity.getOpenid() == null){
                customerEntity.setOpenid(wechatLoginResponse.getOpenId());
                updateEntity.setOpenid(wechatLoginResponse.getOpenId());
                needUpdate = true;
            } else if (loginType == CustomerLoginTypeEnum.MINI_LOGIN.getType() && customerEntity.getMiniOpenid() == null){
                customerEntity.setMiniOpenid(wechatLoginResponse.getOpenId());
                updateEntity.setMiniOpenid(wechatLoginResponse.getOpenId());
                needUpdate = true;
            }
            if (needUpdate) {
                customerRepository.updateCustomer(updateEntity);
            }
            customerEntity.setLoginTime(new Date());
            customerRepository.updateLoginInfo(customerEntity);
        }
        return customerEntity;
    }

    private CustomerEntity getCustomerByUnionId(String unionId) {
        String cacheKey = RedisKeyConstants.CUSTOMER_INFO_UNIONID + unionId;
        String customerJson = redisUtil.get(cacheKey);
        if (StringUtils.isBlank(customerJson)){
            return customerRepository.selectCustomerByUnionId(unionId);
        } else {
            return JSONObject.parseObject(customerJson, CustomerEntity.class);
        }
    }

    @Override
    public CustomerEntity getCustomerById(Long customerId) {
        String cacheKey = RedisKeyConstants.CUSTOMER_INFO + customerId;
        String customerJson = redisUtil.get(cacheKey);
        if (StringUtils.isBlank(customerJson)){
            CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
            redisUtil.set(cacheKey, JSONObject.toJSONString(customerEntity), true);
            return customerEntity;
        } else {
            return JSONObject.parseObject(customerJson, CustomerEntity.class);
        }
    }

    /**
     * 计算到次日零点的秒数
     */
    private long calculateExpireSecondsToMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        return Duration.between(now, midnight).getSeconds();
    }

    @Override
    public CardBalanceResponse getBalanceByCardNoAndPassword(CardBalanceQueryRequest request) {
        long customerId = StpUtil.getLoginIdAsLong();
        String cacheKey = RedisKeyConstants.CARD_BALANCE_DAILY_QUERY_COUNT + customerId;
        Long newCount = redisUtil.incrBy(cacheKey, 1);
        if (newCount != null && newCount == 1) {
            // 如果是第一次查询，设置过期时间到次日零点
            long expireSeconds = calculateExpireSecondsToMidnight();
            redisUtil.expire(cacheKey, (int) expireSeconds);
        }
        // 每日查询次数不得超过3次  只算成功的次数
        if (newCount != null && newCount > CARD_BALANCE_DAILY_QUERY_MAX_COUNT) {
            throw new BizException(Status.OUT_MAX_QUERY_CARD_BALANCE_COUNT);
        }
        try {
            CheckFindCardBalanceRequest checkFindCardBalanceRequest = new CheckFindCardBalanceRequest();
            checkFindCardBalanceRequest.setCardNo(request.getCardNo());
            checkFindCardBalanceRequest.setPassword(request.getPassword());
            CheckFindCardBalanceResponse checkFindCardBalanceResponse = onlinePaymentGateway.findBalanceByCardNoAndPassword(checkFindCardBalanceRequest);
            if (checkFindCardBalanceResponse == null || checkFindCardBalanceResponse.getBalance() == null){
                // 查询失败，减少计数
                redisUtil.strdecrBy(cacheKey, 1);
                throw new BizException(Status.CARD_BALANCE_QUERY_FAIL);
            }
            CardBalanceResponse cardBalanceResponse = new CardBalanceResponse();
            cardBalanceResponse.setCardNo(request.getCardNo());
            cardBalanceResponse.setValidAmount(new BigDecimal(checkFindCardBalanceResponse.getBalance()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            return cardBalanceResponse;
        } catch (Exception e) {
            // 查询失败，减少计数
            redisUtil.strdecrBy(cacheKey, 1);
            throw new BizException(Status.CARD_BALANCE_QUERY_FAIL);
        }
    }

    private CustomerResponse buildCustomerResponse(CustomerEntity customerEntity) {
        CustomerResponse customerResponse = new CustomerResponse();
        customerResponse.setCustomerId(customerEntity.getId());
        if (customerEntity.getPhone() == null){
            // 用户未绑定手机号码，手机号码绑定成功之后才能登录成功
            return customerResponse;
        }
        StpUtil.login(customerEntity.getId());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        customerResponse.setUnionid(customerEntity.getUnionid());
        customerResponse.setPhone(customerEntity.getPhone());
        customerResponse.setOpenid(customerEntity.getOpenid());
        customerResponse.setMiniOpenid(customerEntity.getMiniOpenid());
        customerResponse.setToken(tokenInfo.getTokenValue());
        return customerResponse;
    }

    private void cacheCustomer(CustomerEntity customer) {
        String cacheKey = RedisKeyConstants.CUSTOMER_INFO_UNIONID + customer.getUnionid();
        redisUtil.set(cacheKey, JSON.toJSONString(customer), true);
    }

    @Override
    public List<CustomerCardResponse> getCardList(CustomerCardRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        rxMemberRequest.setUnionid(customerEntity.getUnionid());
        if (request.getCardType() == null){
            rxMemberRequest.setCardType(CustomerCardTypeEnum.getAllRxMemberCardTypes());
        } else {
            rxMemberRequest.setCardType(String.valueOf(CustomerCardTypeEnum.getRxMemberCardTypeByType(request.getCardType())));
        }
        RxMemberResponse rxMemberResponse = rxMemberGateway.getCardList(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()){
            throw new BizException("获取卡列表失败");
        }
        return processCustomerCardList((List<RxMemberCardResponse>) rxMemberResponse.getData());
    }

    private List<CustomerCardResponse> processCustomerCardList(List<RxMemberCardResponse> rxMemberCardResponseList) {
        if (rxMemberCardResponseList == null || rxMemberCardResponseList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String,List<RxMemberCardDetail>> map = rxMemberCardResponseList.stream()
                .collect(Collectors.toMap(RxMemberCardResponse::getCardType, RxMemberCardResponse::getNoList));
        List<CustomerCardResponse> cardResponseList = new ArrayList<>();
        for(String key : map.keySet()){
            List<RxMemberCardDetail> list = map.getOrDefault(key, Collections.emptyList());
            if (key.equals(String.valueOf(CustomerCardTypeEnum.BUSINESS_CARD.getRxMemberCardType()))) {
                processBusinessCard(list, cardResponseList);
            } else {
                CustomerCardResponse cardResponse = new CustomerCardResponse();
                cardResponse.setCardType(Integer.valueOf(key));
                cardResponse.setCardNum(list.size());
                cardResponse.setCardInfoList(mapToCardDetailResponses(list));
                cardResponseList.add(cardResponse);
            }
        }
        return findCardBalance(cardResponseList);
    }

    private void processBusinessCard(List<RxMemberCardDetail> list, List<CustomerCardResponse> cardResponseList) {
        Map<String, List<RxMemberCardDetail>> businessCardMap = list.stream()
                .collect(Collectors.groupingBy(RxMemberCardDetail::getCardSubType));

        List<CustomerCardDetailResponse> redCardDetailResponses = new ArrayList<>();
        List<CustomerCardDetailResponse> businessCardDetailResponses = new ArrayList<>();

        for (String businessCardKey : businessCardMap.keySet()) {
            List<RxMemberCardDetail> subList = businessCardMap.getOrDefault(businessCardKey, Collections.emptyList());

            if (businessCardKey.equals(String.valueOf(CustomerCardTypeEnum.RED_CARD.getType()))) {
                redCardDetailResponses.addAll(mapToCardDetailResponses(subList));
            } else {
                businessCardDetailResponses.addAll(mapToCardDetailResponses(subList));
            }
        }
        if (!redCardDetailResponses.isEmpty()) {
            CustomerCardResponse cardResponse = new CustomerCardResponse();
            cardResponse.setCardType(CustomerCardTypeEnum.RED_CARD.getType());
            cardResponse.setCardNum(redCardDetailResponses.size());
            cardResponse.setCardInfoList(redCardDetailResponses);
            cardResponseList.add(cardResponse);
        }
        if (!businessCardDetailResponses.isEmpty()) {
            CustomerCardResponse cardResponse = new CustomerCardResponse();
            cardResponse.setCardType(CustomerCardTypeEnum.BUSINESS_CARD.getType());
            cardResponse.setCardNum(businessCardDetailResponses.size());
            cardResponse.setCardInfoList(businessCardDetailResponses);
            cardResponseList.add(cardResponse);
        }
    }

    private List<CustomerCardResponse> findCardBalance(List<CustomerCardResponse> cardResponseList) {
        List<CardInfoRequest> cardInfoRequestList = cardResponseList.stream()
                .flatMap(item -> {
                    String cardTypeCode = CustomerCardTypeEnum.getCardTypeCodeByType(item.getCardType());
                    return item.getCardInfoList().stream().map(cardDetail -> {
                        CardInfoRequest request = new CardInfoRequest();
                        request.setCardNo(cardDetail.getCardNo());
                        request.setCardType(cardTypeCode);
                        return request;
                    });
                })
                .toList();
        FindCardBalanceRequest findCardBalanceRequest = new FindCardBalanceRequest();
        // 个人中心查询卡余额缓存
        findCardBalanceRequest.setIsCache(true);
        findCardBalanceRequest.setCards(cardInfoRequestList);
        List<FindCardBalanceResponse> findCardBalanceResponses = onlinePaymentGateway.findBalance(findCardBalanceRequest);

        Map<String, CustomerCardDetailResponse> cardDetailMap = new HashMap<>();
        for (CustomerCardResponse cardResponse : cardResponseList) {
            for (CustomerCardDetailResponse cardDetailResponse : cardResponse.getCardInfoList()) {
                cardDetailMap.put(cardDetailResponse.getCardNo(), cardDetailResponse);
            }
        }

        findCardBalanceResponses.forEach(item -> {
            CustomerCardDetailResponse cardDetailResponse = cardDetailMap.get(item.getCardNo());
            if (cardDetailResponse != null && item.getBalance() != null) {
                cardDetailResponse.setValidAmount(new BigDecimal(item.getBalance()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
        });
        return cardResponseList;
    }
    private List<CustomerCardDetailResponse> mapToCardDetailResponses(List<RxMemberCardDetail> details) {
        return details.stream()
                .map(item -> {
                    CustomerCardDetailResponse response = new CustomerCardDetailResponse();
                    response.setCardNo(item.getCardNo());
                    response.setIsZero(item.getIsZero());
                    response.setValidAmount(BigDecimal.ZERO);
                    response.setCreateTime(item.getCreateTime());
                    return response;
                })
                .toList();
    }

    @Override
    public CustomerCheckLoginOffResponse checkIsAllowedLoginOff(CustomerRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        RxMemberResponse rxMemberResponse = rxMemberGateway.checkIsAllowedLoginOff(rxMemberRequest);
        CustomerCheckLoginOffResponse customerCheckLoginOffResponse = new CustomerCheckLoginOffResponse();
        if (!rxMemberResponse.isSuccess()){
            customerCheckLoginOffResponse.setAllowed(false);
            customerCheckLoginOffResponse.setErrMsg(rxMemberResponse.getErrorMessage());
        } else {
            customerCheckLoginOffResponse.setAllowed(true);
        }
        return customerCheckLoginOffResponse;
    }

    @Override
    public void deleteAccount(CustomerDeleteAccountRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        if (customerEntity == null){
            throw new BizException(Status.USER_NOT_EXIST);
        }
        String code = redisUtil.get(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
        log.info("短信验证码, code={}, verificationCode={}", code, request.getVerificationCode());
        if (!request.getVerificationCode().equals(code)) {
            throw new BizException("短信验证码不一致");
        }
        // 校验账户余额是否都为0  待处理
        CustomerEntity updateEntity = new CustomerEntity();
        updateEntity.setId(customerEntity.getId());
        updateEntity.setStatus(NumberUtils.INTEGER_TWO);
        updateEntity.setDelFlag(NumberUtils.INTEGER_ONE);
        updateEntity.setModTime(new Date());
        updateEntity.setModId(customerEntity.getId());
        customerRepository.deleteCustomer(updateEntity);
        CustomerPhoneChangeRecordEntity customerPhoneChangeRecordEntity = new CustomerPhoneChangeRecordEntity();
        customerPhoneChangeRecordEntity.setCustomerId(customerEntity.getId());
        customerPhoneChangeRecordEntity.setOriginalPhone(customerEntity.getPhone());
        customerPhoneChangeRecordEntity.setChangeMethod(CustomerPhoneChangeMethod.USER_CANCEL.getCode());
        customerPhoneChangeRecordEntity.setCreateTime(new Date());
        customerPhoneChangeRecordEntity.setCreateId(customerEntity.getId());
        customerPhoneChangeRecordRepository.savePhoneChangeRecord(customerPhoneChangeRecordEntity);
        redisUtil.delete(RedisKeyConstants.CUSTOMER_VERIFICATION_CODE + customerEntity.getPhone());
        redisUtil.delete(RedisKeyConstants.CUSTOMER_INFO + customerEntity.getId());
        StpUtil.logout(customerEntity.getId());
    }

    @Override
    public void bindCard(CustomerBindCardRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        rxMemberRequest.setNo(request.getCardNo());
        // TODO 密码加密
        rxMemberRequest.setPassword(request.getPassword());
        rxMemberRequest.setCvv(request.getCvv());
        RxMemberResponse rxMemberResponse = rxMemberGateway.addCard(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()){
            throw new RuntimeException(rxMemberResponse.getErrorMessage());
        }
        // 到卡系统查询该卡属于的企业,并与风控企业匹配更新至用户表
        try {
            String customerNameByCardNo = thirdCardGateWay.getCustomerNameByCardNo(request.getCardNo());
            if (StringUtils.isNotBlank(customerNameByCardNo)){
                ProductRiskControlCompanyEntity riskControlCompany = productRiskControlCompanyRepository.getRiskControlCompanyByName(customerNameByCardNo);
                if (ObjectUtil.isNotEmpty(riskControlCompany)) {
                    CustomerEntity updateCustomerEntity = new CustomerEntity();
                    updateCustomerEntity.setId(customerEntity.getId());
                    updateCustomerEntity.setCompanyId(riskControlCompany.getId());
                    customerRepository.updateCustomer(updateCustomerEntity);
                }
            }
        } catch (Exception e) {
            log.error("根据卡号未匹配到卡所属企业");
        }
    }

    @Override
    public void unbindCard(CustomerUnbindCardRequest request) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        RxMemberRequest rxMemberRequest = new RxMemberRequest();
        rxMemberRequest.setVipId(String.valueOf(customerEntity.getVipId()));
        rxMemberRequest.setNo(request.getCardNo());
        RxMemberResponse rxMemberResponse = rxMemberGateway.deleteCard(rxMemberRequest);
        if (!rxMemberResponse.isSuccess()){
            throw new RuntimeException(rxMemberResponse.getErrorMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setPaySort(CustomerSetPaySortRequest request) {
        String cacheKey = RedisKeyConstants.CUSTOMER_PAY_SORT + request.getCustomerId();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(request.getCustomerId());
        CustomerPaySortResponse response = new CustomerPaySortResponse();
        response.setCustomerId(request.getCustomerId());
        if (request.getUseDefault()){
            if (!Objects.equals(customerEntity.getUseDefaultPaySort(), NumberUtils.INTEGER_ONE)){
                CustomerEntity updateEntity = new CustomerEntity();
                updateEntity.setId(customerEntity.getId());
                updateEntity.setUseDefaultPaySort(NumberUtils.INTEGER_ONE);
                customerRepository.updateCustomer(updateEntity);
            }
            CustomerPaySortEntity paySortEntity = customerPaySortRepository.getCustomerPaySortByCustomerId(request.getCustomerId());
            if (paySortEntity != null){
                customerPaySortRepository.deleteCustomerPaySort(paySortEntity.getId());
            }
            response.setUseDefault(true);
            response.setPaySortList(getDefaultPaySort());
            redisUtil.set(cacheKey, JSONUtil.toJsonStr(response), true);
        } else {
            if (!Objects.equals(customerEntity.getUseDefaultPaySort(), NumberUtils.INTEGER_ZERO)){
                CustomerEntity updateEntity = new CustomerEntity();
                updateEntity.setId(customerEntity.getId());
                updateEntity.setUseDefaultPaySort(NumberUtils.INTEGER_ZERO);
                customerRepository.updateCustomer(updateEntity);
            }
            CustomerPaySortEntity paySortEntity = customerPaySortRepository.getCustomerPaySortByCustomerId(request.getCustomerId());
            if (paySortEntity != null){
                paySortEntity.setPaySort(String.join(",", request.getPaySortList()));
                paySortEntity.setModTime(new Date());
                customerPaySortRepository.updateCustomerPaySort(paySortEntity);
            } else {
                paySortEntity = new CustomerPaySortEntity();
                paySortEntity.setCustomerId(request.getCustomerId());
                paySortEntity.setDelFlag(NumberUtils.INTEGER_ZERO);
                paySortEntity.setPaySort(String.join(",", request.getPaySortList()));
                customerPaySortRepository.saveCustomerPaySort(paySortEntity);
            }
            response.setUseDefault(false);
            response.setPaySortList(request.getPaySortList());
            redisUtil.set(cacheKey, JSONUtil.toJsonStr(response), true);
        }
    }

    /**
     * 获取用户支付顺序
     * @param customerId
     * @return
     */
    @Override
    public CustomerPaySortResponse getPaySort(Long customerId) {
        String cacheKey = RedisKeyConstants.CUSTOMER_PAY_SORT + customerId;
        String cacheValue = redisUtil.get(cacheKey);
        CustomerPaySortResponse response = new CustomerPaySortResponse();
        if(StrUtil.isNotBlank(cacheValue)) {
            response = JSONObject.parseObject(cacheValue, CustomerPaySortResponse.class);
        } else {
            CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
            response.setCustomerId(customerId);
            if (Objects.equals(customerEntity.getUseDefaultPaySort(), NumberUtils.INTEGER_ONE)){
                response.setUseDefault(true);
                response.setPaySortList(getDefaultPaySort());
            } else {
                CustomerPaySortEntity customerPaySortEntity = customerPaySortRepository.getCustomerPaySortByCustomerId(customerId);
                List<String> paySortList = Arrays.stream(customerPaySortEntity.getPaySort().split(",")).map(String::trim).toList();
                response.setUseDefault(false);
                response.setPaySortList(paySortList);
            }
            redisUtil.set(cacheKey, JSONUtil.toJsonStr(response), true);
        }
        return response;
    }

    @Override
    public List<AdvertisementInfoDTO> getPersonalCenterAd(Integer regionId) {
        //广告
        List<AdvertisementEntity> adList = advertisementService.getAdvertisementList(AdvertisementTypeEnum.ADV_USER_CENTER.getCode(), null);
        if (CollectionUtil.isNotEmpty(adList)) {
            List<AdvertisementInfoDTO> advertisementInfoDTOList = new ArrayList<>();
            adList.forEach(item -> {
                if (item.getIsNationwide() == 1
                        || contentRegionService.isOnlineInRegion(item.getId(), ContentcenterTypeEnum.CONTENT_ADV.getCode(), regionId)) {
                    AdvertisementInfoDTO advertisementInfoDTO = new AdvertisementInfoDTO();
                    BeanUtils.copyProperties(item, advertisementInfoDTO);
                    advertisementInfoDTOList.add(advertisementInfoDTO);
                }
            });
            return advertisementInfoDTOList;
        }
        return null;
    }

    @Override
    public Boolean checkCustomerHasPayPassword(Long customerId) {
        CustomerEntity customerEntity = getCustomerById(customerId);
        return !StringUtils.isEmpty(customerEntity.getPassword());
    }

    @Override
    public LoginOffConfigResponse getLoginOffConfig() {
        String maxDeleteAccountCount = configRepository.getValueByType(SystemConfigTypeEnum.DELETE_USER_MAX_COUNT_TYPE);
        String reRegisterInterval = configRepository.getValueByType(SystemConfigTypeEnum.REREGISTER_TIME_INTERVAL_TYPE);
        LoginOffConfigResponse response = new LoginOffConfigResponse();
        response.setMaxDeleteAccountCount(Integer.parseInt(maxDeleteAccountCount));
        response.setReRegisterInterval(Integer.parseInt(reRegisterInterval));
        return response;
    }

    /**
     * 获取平台默认支付顺序  默认顺序为：提货凭证、黑金、工会凭证、白金、瑞祥红卡、商联卡
     * @return
     */
    private  List<String> getDefaultPaySort(){
        String defaultPaySortValue = redisUtil.get(RedisKeyConstants.DEFAULT_PAY_SORT);
        if(StrUtil.isNotBlank(defaultPaySortValue)) {
            return JSONObject.parseObject(defaultPaySortValue, List.class);
        } else {
            return paymentMethodRepository.getDefaultPaySortList();
        }
    }
}
