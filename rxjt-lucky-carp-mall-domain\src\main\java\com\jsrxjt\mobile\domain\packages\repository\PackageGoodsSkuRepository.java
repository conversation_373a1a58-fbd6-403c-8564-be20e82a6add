package com.jsrxjt.mobile.domain.packages.repository;

import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;

import java.util.List;

/**
 * @Description: 套餐sku服务
 * @Author: ywt
 * @Date: 2025-05-10 13:46
 * @Version: 1.0
 */
public interface PackageGoodsSkuRepository {
    /**
     * 获取sku
     */
    List<PackageGoodsSkuEntity> getPackageGoodsSku(Long packageSpuId);

    List<PackageSubSkuEntity> getPackageSubSku(Long packageSkuId);

    /**
     * 批量更新状态
     */
    void updatePackageGoodsSkuStatus(List<PackageGoodsSkuJobEntity> entityList);

    /**
     * 原子性增加套餐SKU销量
     *
     * @param packageSkuId 套餐SKU ID
     * @param quantity     增加的数量
     * @return 更新的行数
     */
    int increaseSoldNum(Long packageSkuId, Integer quantity);
}
