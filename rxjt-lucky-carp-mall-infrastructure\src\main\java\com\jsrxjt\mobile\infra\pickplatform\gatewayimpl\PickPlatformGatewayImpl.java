package com.jsrxjt.mobile.infra.pickplatform.gatewayimpl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformCardCodeRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickCardItemRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformPayInfoRequest;
import com.jsrxjt.mobile.domain.pickplatform.request.PickPlatformShopRequest;
import com.jsrxjt.mobile.domain.pickplatform.gateway.PickPlatformGateway;
import com.jsrxjt.mobile.domain.gateway.http.HttpClientGateway;
import com.jsrxjt.mobile.domain.pickplatform.response.*;
import com.jsrxjt.mobile.infra.pickplatform.config.PickPlatformConfig;
import com.jsrxjt.mobile.infra.pickplatform.util.RxMemberSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 提货分销平台
 * @Author: ywt
 * @Date: 2025-05-07 17:19
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PickPlatformGatewayImpl implements PickPlatformGateway {
    protected static final String CODE = "code";
    protected static final String DATA = "data";
    protected static final Integer SUCCESS_CODE = 0;
    protected static final String MESSAGE_KEY = "message";
    protected final HttpClientGateway httpClientGateway;
    private final PickPlatformConfig pickPlatformConfig;

    @Override
    public PickPlatformShopDataResponse getShopList(PickPlatformShopRequest paramEntity) {
        PickPlatformShopDataResponse dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("app_id", pickPlatformConfig.getAppId());
        requestMap.put("nonce", paramEntity.getNonce());
        requestMap.put("timestamp", paramEntity.getTimeStamp());

        requestMap.put("city_code", paramEntity.getCity_code());
        requestMap.put("longitude", paramEntity.getLongitude());
        requestMap.put("latitude", paramEntity.getLatitude());
        requestMap.put("productId", paramEntity.getProductId());
        if (StringUtils.isNotEmpty(paramEntity.getKeyword())) {
            requestMap.put("keyword", paramEntity.getKeyword());
        }
        if (Objects.nonNull(paramEntity.getPage()) || Objects.nonNull(paramEntity.getPageSize())) {
            requestMap.put("page", paramEntity.getPage());
            requestMap.put("pageSize", paramEntity.getPageSize());
        }
        String requestSign = RxMemberSignUtil.getSign(requestMap, pickPlatformConfig.getAppsecret());
        requestMap.put("sign", requestSign);
        try {
            String shopStr = httpClientGateway.doPost(pickPlatformConfig.getDomain() + pickPlatformConfig.getShopList(),
                    requestMap, pickPlatformConfig.getConnectTimeout(), pickPlatformConfig.getReadTimeout());
            JSONObject result = JSON.parseObject(shopStr);
            if (Objects.equals(result.getInteger(CODE), SUCCESS_CODE) && Objects.nonNull(result.get(DATA))) {
                dataResponse = JSON.parseObject(JSON.toJSONString(result.get(DATA)), PickPlatformShopDataResponse.class);
            }
        } catch (Exception e) {
            log.error("提货分销中台-获取门店接口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }

    @Override
    public PickPlatformCardCodeResponse getCardCode(PickPlatformCardCodeRequest paramEntity) {
        PickPlatformCardCodeResponse dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("app_id", pickPlatformConfig.getAppId());
        requestMap.put("nonce", paramEntity.getNonce());
        requestMap.put("timestamp", paramEntity.getTimestamp());
        requestMap.put("checkNo", paramEntity.getCheckNo());
        requestMap.put("userNo", paramEntity.getUserNo());
        String requestSign = RxMemberSignUtil.getSign(requestMap, pickPlatformConfig.getAppsecret());
        requestMap.put("sign", requestSign);
        try {
            String shopStr = httpClientGateway.doPost(pickPlatformConfig.getDomain() + pickPlatformConfig.getCardCode(),
                    requestMap, pickPlatformConfig.getConnectTimeout(), pickPlatformConfig.getReadTimeout());
            JSONObject result = JSON.parseObject(shopStr);
            if (Objects.equals(result.getInteger(CODE), SUCCESS_CODE) && Objects.nonNull(result.get(DATA))) {
                dataResponse = JSON.parseObject(JSON.toJSONString(result.get(DATA)), PickPlatformCardCodeResponse.class);
            }
        } catch (Exception e) {
            log.error("提货分销中台-获取动态码异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }

    /**
     * 获取卡信息
     *
     * @param paramEntity
     */
    @Override
    public PickCardItemResponse getCardInfo(PickCardItemRequest paramEntity) {
        PickCardItemResponse dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("app_id", pickPlatformConfig.getAppId());
        requestMap.put("nonce", paramEntity.getNonce());
        requestMap.put("timestamp", paramEntity.getTimestamp());
        requestMap.put("checkNo", paramEntity.getCheckNo());
        String requestSign = RxMemberSignUtil.getSign(requestMap, pickPlatformConfig.getAppsecret());
        requestMap.put("sign", requestSign);
        try {
            String shopStr = httpClientGateway.doPost(pickPlatformConfig.getDomain() + pickPlatformConfig.getCardInfo(),
                    requestMap, pickPlatformConfig.getConnectTimeout(), pickPlatformConfig.getReadTimeout());
            JSONObject result = JSON.parseObject(shopStr);
            if (Objects.equals(result.getInteger(CODE), SUCCESS_CODE) && Objects.nonNull(result.get(DATA))) {
                dataResponse = JSONUtil.toBean(JSON.toJSONString(result.get(DATA)), PickCardItemResponse.class);
            }
        } catch (Exception e) {
            log.error("提货分销中台-获取卡信息异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }

    /**
     * 获取卡支付信息
     *
     * @param paramEntity
     */
    @Override
    public PickPlatformPayInfoPageResponse getCardPayInfo(PickPlatformPayInfoRequest paramEntity) {
        PickPlatformPayInfoPageResponse dataResponse = null;
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("app_id", pickPlatformConfig.getAppId());
        requestMap.put("nonce", RandomUtil.randomString(16));
        requestMap.put("timestamp", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        requestMap.put("userNo", paramEntity.getUserNo());
        requestMap.put("productId", paramEntity.getProductId());
        requestMap.put("page", paramEntity.getPage());
        requestMap.put("pageSize", paramEntity.getPageSize());
        requestMap.put("pagination", paramEntity.getPagination());
        String requestSign = RxMemberSignUtil.getSign(requestMap, pickPlatformConfig.getAppsecret());
        requestMap.put("sign", requestSign);
        try {
            String shopStr = httpClientGateway.doPost(pickPlatformConfig.getDomain() + pickPlatformConfig.getCardPayInfo(),
                    requestMap, pickPlatformConfig.getConnectTimeout(), pickPlatformConfig.getReadTimeout());
            JSONObject result = JSON.parseObject(shopStr);
            if (Objects.equals(result.getInteger(CODE), SUCCESS_CODE) && Objects.nonNull(result.get(DATA))) {
                String jsonString = JSON.toJSONString(result.get(DATA));
                dataResponse = JSONUtil.toBean(jsonString, PickPlatformPayInfoPageResponse.class);
            }
        } catch (Exception e) {
            log.error("提货分销中台-获取卡支付异常: {}", e.getMessage());
            e.printStackTrace();
        }
        return dataResponse;
    }
}
