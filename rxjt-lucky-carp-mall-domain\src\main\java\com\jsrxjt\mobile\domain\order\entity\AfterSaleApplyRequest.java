package com.jsrxjt.mobile.domain.order.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 申请售后请求值对象
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
public class AfterSaleApplyRequest {
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 售后类型
     */
    private Integer afterSaleType;
    
    /**
     * 售后数量
     */
    private Integer afterSaleQuantity;
    
    /**
     * 申请退款金额
     */
    private BigDecimal applyRefundAmount;
    
    /**
     * 售后原因
     */
    private String afterSaleReason;
    
    /**
     * 退款描述
     */
    private String refundDescription;
    
    /**
     * 客户备注
     */
    private String customerRemark;
}