package com.jsrxjt.mobile.api.riskcontrol.dto.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/25 17:59
 */
@Data
public class ProductRiskControlResponse {

    private Long customerId;

    /**
     * 1、当前商品或用户未在风控策略中
     * 2、当前商品或用户在风控策略中且在黑名单中
     * 3、当前商品或用户在风控策略中但不在黑名单中
     */
    private Integer canPlaceOrderFlag;
    /**
     * 产品skuId或应用ID
     */
    private String productSkuId;

    /**
     * 手续费百分比
     */
    private BigDecimal commissionFee;

    /**
     * 每人每日限额
     */
    private BigDecimal skuAmountPerDay;

    /**
     * 每人每月限额
     */
    private BigDecimal skuAmountPerMonth;
    /**
     * 建议零售价
     */
    private BigDecimal suggestPrice;

    /**
     * spu每月限售总额
     */
    private BigDecimal spuAmountPerMonth;

    /**
     * 支付方式
     */
    private String payType;

}
