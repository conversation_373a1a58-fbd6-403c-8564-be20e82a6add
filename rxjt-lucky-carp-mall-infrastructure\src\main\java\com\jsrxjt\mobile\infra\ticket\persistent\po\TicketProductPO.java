package com.jsrxjt.mobile.infra.ticket.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 优惠券与卡券关联表
 * @Author: ywt
 * @Date: 2025-08-15 15:39
 * @Version: 1.0
 */
@Data
@TableName("ticket_product_relation")
public class TicketProductPO {
    @Schema(description = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(description = "卡券skuid")
    @TableField("product_sku_id")
    private Long productSkuId;
    @Schema(description = "产品类型：1卡券 2套餐")
    @TableField("product_type")
    private Integer productType;
    @Schema(description = "优惠券id")
    @TableField("ticket_id")
    private Long ticketId;
    @Schema(description = "状态 1启用 0禁用")
    @TableField("status")
    private Integer status;
    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "创建人id")
    @TableField("create_id")
    private Long createId;

    @Schema(description = "编辑人id")
    @TableField("mod_id")
    private Long modId;

    @Schema(description = "编辑时间")
    @TableField("mod_time")
    private Date modTime;

    @Schema(description = "是否删除标志(0:否 1:是)")
    @TableField("del_flag")
    private Byte delFlag;

    @Schema(description = "删除人id")
    @TableField("del_id")
    private Long delId;

    @Schema(description = "删除时间")
    @TableField("del_time")
    private Date delTime;

    @Schema(description = "创建人姓名")
    @TableField("create_name ")
    private String createName;

    @Schema(description = "修改人姓名")
    @TableField("mod_name")
    private String modName;
}
