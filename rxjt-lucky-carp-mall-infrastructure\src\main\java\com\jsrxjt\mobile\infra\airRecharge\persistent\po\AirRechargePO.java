package com.jsrxjt.mobile.infra.airRecharge.persistent.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 空充信息表
 * @Author: ywt
 * @Date: 2025-08-26 15:22
 * @Version: 1.0
 */
@Data
public class AirRechargePO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 待充值金额
     */
    private BigDecimal price;

    /**
     * 真实已经成功充值的金额
     */
    private BigDecimal truePrice;

    /**
     * 充值卡号
     */
    private String cardNo;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 状态 1 待处理 2 执行中 3 已完成 4 失败 5 撤销中 6 撤销成功
     */
    private Integer status;

    /**
     * 关联的批次ID
     */
    private Integer batchRechargeId;

    /**
     * 1 新用户 0 老用户
     */
    private Integer isNewreg;

    /**
     * 0 未弹出通知窗口 1 已弹出通知窗口
     */
    private Integer isMsg;

    /**
     * 0 非线下处理 1 线下处理
     */
    private Integer isUnderline;

    /**
     * 成功充值时的时间戳
     */
    private Integer successTime;

    /**
     * 充值成功后首次登录的时间
     */
    private Integer sucLoginTime;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 撤销订单号
     */
    private String revocationNo;

    /**
     * 撤销时间
     */
    private Integer revocationTime;

    /**
     * 是否需要签名 1:需要 2：不需要
     */
    private Integer needSign;

    /**
     * 是否已经签名 1：是 2：否
     */
    private Integer isWindows;

    /**
     * 签名用  公司ID
     */
    private Integer companyId;

    /**
     * 签名图片远程地址
     */
    private String signPic;

    /**
     * 充值类型，1 黑金主卡 2 白金主卡 3 提货凭证
     */
    private Integer rechargeType;
}
