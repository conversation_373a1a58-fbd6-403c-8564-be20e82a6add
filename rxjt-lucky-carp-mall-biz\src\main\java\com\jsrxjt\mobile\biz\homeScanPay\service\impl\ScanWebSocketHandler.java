package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.scanPay.response.ScanPayWssResponseDTO;
import com.jsrxjt.mobile.domain.scanpay.entity.ScanPayWssEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class ScanWebSocketHandler extends TextWebSocketHandler {

    //本地会话缓存 key = customerId_payCode
    private final Map<String, WebSocketSession> localSessions = new ConcurrentHashMap<>();

    private final RedisUtil redisUtil;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        try {
            String sessionId = session.getId();
            Long customerId = Long.valueOf((String) session.getAttributes().get("customerId"));
            String token = (String) session.getAttributes().get("token");
            String payCode = (String) session.getAttributes().get("payCode");

            if (customerId == null || token == null) {
                log.error("WebSocket连接异常: 认证信息不完整");
                closeSessionWithError(session, "认证信息不完整", CloseStatus.NOT_ACCEPTABLE);
                return;
            }
            String sessionKey = customerId + "_" + payCode;
            localSessions.put(sessionKey, session);
            storeSessionInfo(sessionId, customerId, payCode,  token);
            log.info("WebSocket连接建立成功: {}, 用户ID: {}", sessionId, customerId);
            //发送连接成功消息
            sendMessage(session, JSONObject.toJSONString(BaseResponse.succeed(ScanPayWssResponseDTO.builder()
                    .sessionId(sessionId)
                    .customerId(customerId)
                    .build())));
        } catch (Exception e) {
            log.error("WebSocket连接建立异常", e);
            closeSessionWithError(session, "连接建立异常", CloseStatus.SERVER_ERROR);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        log.error("WebSocket session{}获取到消息{}", session.getId(), payload);
    }

    public void sendMessageToCustomer(Long customerId, String payCode,String message) {
        String sessionKey = customerId + "_" + payCode;
        WebSocketSession session = localSessions.get(sessionKey);
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
            } catch (Exception e) {
                log.error("本地消息发送失败", e);
            }
        }
    }

    private void sendMessage(WebSocketSession session, String message){
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
            } catch (Exception e) {
                log.error("本地消息发送失败", e);
            }
        }
    }

    private void storeSessionInfo(String sessionId, Long customerId, String payCode, String token) {
        try {
            ScanPayWssEntity scanPayWssEntity = new ScanPayWssEntity();
            scanPayWssEntity.setCustomerId(customerId);
            scanPayWssEntity.setSessionId(sessionId);
            scanPayWssEntity.setToken(token);
            scanPayWssEntity.setPayCode(payCode);
            scanPayWssEntity.setCreateTime(System.currentTimeMillis());
            String key = RedisKeyConstants.WSS_SCAN_CUSTOMER.formatted(customerId, payCode);
            redisUtil.set(key, JSONObject.toJSONString(scanPayWssEntity), 30*60);
        } catch (Exception e) {
            log.error("存储会话信息失败", e);
        }
    }


    private void sendError(WebSocketSession session, String errorMessage) {
        sendMessage(session, JSONObject.toJSONString(BaseResponse.fail(CloseStatus.SERVER_ERROR.getCode(), errorMessage)));
    }

    private void closeSessionWithError(WebSocketSession session, String errorMessage, CloseStatus closeStatus) {
        try {
            sendError(session, errorMessage);
            session.close(closeStatus);
        } catch (Exception e) {
            log.error("关闭会话时发生错误", e);
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        Long customerId = (Long) session.getAttributes().get("customerId");
        String payCode = (String) session.getAttributes().get("payCode");
        String sessionKey = customerId + "_" + payCode;
        localSessions.remove(sessionKey);
        cleanupSessionInfo(sessionId, customerId, payCode);

        log.info("WebSocket连接关闭: {}, 用户: {}, 状态: {}", sessionId, customerId, status);
    }

    private void cleanupSessionInfo(String sessionId, Long customerId,  String payCode) {
        try {
            redisUtil.delete(RedisKeyConstants.WSS_SCAN_CUSTOMER.formatted(customerId, payCode));
        } catch (Exception e) {
            log.error("清理会话信息失败", e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket传输错误: {}", session.getId(), exception);
    }
}
