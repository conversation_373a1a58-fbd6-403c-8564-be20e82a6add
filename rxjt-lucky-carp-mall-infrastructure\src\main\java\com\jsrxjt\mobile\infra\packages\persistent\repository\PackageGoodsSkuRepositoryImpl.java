package com.jsrxjt.mobile.infra.packages.persistent.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jsrxjt.mobile.api.coupon.types.CouponStatus;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageGoodsSkuJobEntity;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;
import com.jsrxjt.mobile.domain.packages.repository.PackageGoodsSkuRepository;
import com.jsrxjt.mobile.infra.packages.persistent.mapper.PackageGoodsSkuMapper;
import com.jsrxjt.mobile.infra.packages.persistent.mapper.PackageSubSkuMapper;
import com.jsrxjt.mobile.infra.packages.persistent.po.PackageGoodsSkuPO;
import com.jsrxjt.mobile.infra.packages.persistent.po.PackageSubSkuPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * @Description: 套餐sku服务
 * @Author: ywt
 * @Date: 2025-05-10 13:47
 * @Version: 1.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class PackageGoodsSkuRepositoryImpl implements PackageGoodsSkuRepository {
    private final PackageGoodsSkuMapper packageGoodsSkuMapper;
    private final PackageSubSkuMapper packageSubSkuMapper;

    @Override
    @Cacheable(cacheNames = "package:sku:info", key = "#packageSpuId")
    public List<PackageGoodsSkuEntity> getPackageGoodsSku(Long packageSpuId) {
        List<PackageGoodsSkuPO> list = packageGoodsSkuMapper.selectList(new LambdaQueryWrapper<PackageGoodsSkuPO>()
                .eq(PackageGoodsSkuPO::getPackageSpuId, packageSpuId)
                .eq(PackageGoodsSkuPO::getPackageSkuStatus, CouponStatus.UP.getType())
                .orderByDesc(PackageGoodsSkuPO::getSort));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(list, PackageGoodsSkuEntity.class);
    }

    @Override
    @Cacheable(cacheNames = "package:sub:sku:info", key = "#packageSkuId")
    public List<PackageSubSkuEntity> getPackageSubSku(Long packageSkuId) {
        List<PackageSubSkuPO> list = packageSubSkuMapper.selectList(new LambdaQueryWrapper<PackageSubSkuPO>()
                .eq(PackageSubSkuPO::getPackageSkuId, packageSkuId));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(list, PackageSubSkuEntity.class);
    }

    /**
     * 批量更新状态
     *
     * @param entityList
     */
    @Override
    public void updatePackageGoodsSkuStatus(List<PackageGoodsSkuJobEntity> entityList) {
        packageGoodsSkuMapper.updatePackageGoodsSkuStatus(entityList);
    }

    @Override
    public int increaseSoldNum(Long packageSkuId, Integer quantity) {
        return packageGoodsSkuMapper.increaseSoldNum(packageSkuId, quantity);
    }
}
