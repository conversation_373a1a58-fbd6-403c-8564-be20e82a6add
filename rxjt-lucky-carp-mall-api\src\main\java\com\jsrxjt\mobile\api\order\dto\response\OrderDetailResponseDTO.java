package com.jsrxjt.mobile.api.order.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单详情响应DTO
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
@Getter
@Setter
@Schema(description = "订单详情响应DTO")
public class OrderDetailResponseDTO {
    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "充值账号:类型为直充/红包时不为空")
    private String rechargeAccount;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "品牌id")
    private Long brandId;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "订单状态: 0-待付款 10-进行中 20-交易成功 30-交易关闭 40-超时取消 41-手动取消")
    private Integer orderStatus;

    @Schema(description = "当前售后状态:0-未发生售后 1-待审核 20-审核通过 30-审核驳回 32-退款驳回 33-售后撤销 34-售后完成")
    private Integer afterSaleStatus;

    @Schema(description = "发货状态: 0-未发货/未充值  1-发货中/充值中 2-已发货/已充值 3-发货失败/充值失败")
    private Integer deliveryStatus;

    @Schema(description = "支付状态: 0-未支付 1-已支付 2-部分支付 3-部分退款中 4-部分退款完成 5-全额退款中 6-全额退款已完成")
    private Integer paymentStatus;

    @Schema(description = "支付失效时间戳(秒)")
    private Long payExpireTimestamp;

    @Schema(description = "支付交易号-支付中心提供")
    private String tradeNo;

    @Schema(description = "支付详情")
    private List<OrderPaymentDTO> paymentDetails;

    @Schema(description = "商品原售价总价")
    private BigDecimal totalSellAmount;

    @Schema(description = "优惠总金额")
    private BigDecimal totalDiscountAmount;

    @Schema(description = "订单金额(商品原售价总价 + 加点手续费 + 超额手续费 = 支付金额 + 已优惠金额)")
    private BigDecimal orderAmount;

    @Schema(description = "加点手续费")
    private BigDecimal totalServiceFee;

    @Schema(description = "加点手续费率(%)")
    private BigDecimal totalServiceFeeRate;

    @Schema(description = "总销售价超额手续费")
    private BigDecimal exceedFee;

    @Schema(description = "总销售价超额手续费率(%)")
    private BigDecimal exceedFeeRate;

    @Schema(description = "实际支付金额(商品原售价总价 + 加点手续费 + 超额手续费 - 已优惠金额)")
    private BigDecimal paymentAmount;

    @Schema(description = "限额金额(超出限额计算超额手续费)")
    private BigDecimal limitAmount;

    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    @Schema(description = "发货时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "取消原因")
    private String cancelReason;

    @Schema(description = "订单备注")
    private String remark;

    @Schema(description = "分销应用订单详情链接")
    private String orderDetailUrl;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "外部店铺id")
    private String externalShopId;

    @Schema(description = "第三方id")
    private String thirdId;

    @Schema(description = "外部门店员id")
    private String externalShopUserId;

    @Schema(description = "下单时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime modTime;

    @Schema(description = "支持的支付方式")
    private String payType;

    @Schema(description = "订单项列表")
    private List<OrderItemDetailDTO> orderItems;

    @Schema(description = "待赠券列表")
    private List<GiftTicketResponseDTO> giftTickets;

    /**
     * 订单项详情DTO - 参考OrderItemEntity结构
     */
    @Getter
    @Setter
    @Schema(description = "订单项详情DTO")
    public static class OrderItemDetailDTO {

        @Schema(description = "订单项ID")
        private Long id;

        @Schema(description = "订单编号")
        private String orderNo;

        @Schema(description = "商品ID")
        private Long productId;

        @Schema(description = "商品SKU ID")
        private Long productSkuId;

        @Schema(description = "商品名称")
        private String productName;

        @Schema(description = "商品图标")
        private String productLogo;

        @Schema(description = "商品类型")
        private Integer productType;

        @Schema(description = "扁平化产品类型")
        private Integer flatProductType;

        @Schema(description = "分类ID")
        private Long categoryId;

        @Schema(description = "品牌ID")
        private Long brandId;

        @Schema(description = "品牌名称")
        private String brandName;

        @Schema(description = "渠道ID")
        private Long channelId;

        @Schema(description = "渠道名称")
        private String channelName;

        @Schema(description = "销售价格")
        private BigDecimal sellPrice;

        @Schema(description = "成本价格")
        private BigDecimal costPrice;

        @Schema(description = "面额")
        private BigDecimal faceAmount;

        @Schema(description = "扩展信息 套餐或其他需要扩展的应用的补充信息")
        private String extraInfo;

        @Schema(description = "数量")
        private Integer quantity;

        @Schema(description = "手续费")
        private BigDecimal serviceFee;

        @Schema(description = "手续费率")
        private BigDecimal serviceFeeRate;

        @Schema(description = "外部商品ID")
        private String outGoodsId;

        @Schema(description = "应用标记")
        private String appFlag;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        @Schema(description = "修改时间")
        private LocalDateTime modTime;

        @Schema(description = "套餐子产品信息列表（仅套餐产品有值）")
        List<PackageSubProductInfo> subProductList;
    }

    @Getter
    @Setter
    @Schema(description = "订单支付信息对象")
    public static class OrderPaymentDTO {


        @Schema(description = "支付方式")
        private String paymentMethod;

        @Schema(description = "支付账号/卡号")
        private String accountNo;

        @Schema(description = "支付金额")
        private BigDecimal amount;

    }

    /**
     * 套餐子产品信息
     */
    @Data
    @Schema(description = "套餐子产品信息")
    public static class PackageSubProductInfo {

        @Schema(description = "面值名称")
        private String amountName;

        @Schema(description = "该套餐中的子sku数量")
        private Integer packageCouponNum;

        @Schema(description = "面值")
        private BigDecimal amount;

        @Schema(description = "套餐中的子sku图片")
        private String packageCouponImg;
    }
}