package com.jsrxjt.mobile.biz.homeScanPay.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jsrxjt.common.core.constant.Status;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.customer.response.CustomerDetailResponse;
import com.jsrxjt.mobile.api.distribution.dto.request.PickChannelAppRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.PickCodeRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.request.PickPaymentCodeRequestDTO;
import com.jsrxjt.mobile.api.distribution.dto.response.*;
import com.jsrxjt.mobile.api.order.dto.request.CreateOrderDTO;
import com.jsrxjt.mobile.api.product.types.FlatProductTypeEnum;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.scanPay.PosOrderRequestDTO;
import com.jsrxjt.mobile.api.scanPay.request.*;
import com.jsrxjt.mobile.api.scanPay.response.*;
import com.jsrxjt.mobile.biz.customer.service.CustomerService;
import com.jsrxjt.mobile.biz.homeScanPay.service.PickScanCaseService;
import com.jsrxjt.mobile.biz.homeScanPay.service.HomeScanPayService;
import com.jsrxjt.mobile.biz.lock.DistributedLock;
import com.jsrxjt.mobile.biz.order.OrderCaseService;
import com.jsrxjt.mobile.domain.app.entity.AppGoodsEntity;
import com.jsrxjt.mobile.domain.app.repository.AppGoodsRepository;
import com.jsrxjt.mobile.domain.customer.entity.CustomerEntity;
import com.jsrxjt.mobile.domain.customer.repository.CustomerRepository;
import com.jsrxjt.mobile.domain.gateway.id.BusinessIdGenerator;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.strategy.impl.ThirdHomeScanOrderInfoBuilder;
import com.jsrxjt.mobile.domain.payment.gateway.OfflinePaymentGateway;
import com.jsrxjt.mobile.domain.payment.gateway.request.OfflineCardSortRequest;
import com.jsrxjt.mobile.domain.payment.gateway.response.OfflineCardSortResponse;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.jsrxjt.common.core.constant.RedisKeyConstants.*;

/**
 * @Description:首页扫码付实现
 * @Author: zy
 * @Date: 20250530
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class HomeScanPayServiceImpl implements HomeScanPayService {

    private final CustomerRepository customerRepository;
    private final CustomerService customerService;
    private final AppGoodsRepository appGoodsRepository;
    private final RedisUtil redisUtil;
    private final PickScanCaseService pickScanCaseService;
    private final OrderCaseService orderCaseService;
//    private final MerchantGateway merchantGateway;
//    private final ConfigRepository configRepository;
    private final ProductSkuSellRegionService productSkuSellRegionService;
    private final DistributedLock distributedLock;
    private final OrderRepository orderRepository;
    private final RegionRepository regionRepository;
    private final BusinessIdGenerator businessIdGenerator;
    private final OfflinePaymentGateway offlinePaymentGateway;
    private final ThirdHomeScanOrderInfoBuilder thirdHomeScanOrderInfoBuilder;

    // 保存redis过期时间
    private static final int codeExpireTime = 10 * 60;
    private static final int orderExpireTime = 10 * 60;

    /**
     * 更具定位城市获取对应的展码付应用
     *
     * @param requestDTO
     */
    @Override
    public BaseResponse<List<PickChannelAppResponseDTO>> getChannelApp(PickChannelAppRequestDTO requestDTO) {
        List<PickChannelAppResponseDTO> channelApp = new ArrayList<>();
        Long customerId = StpUtil.getLoginIdAsLong();
      //  Long customerId = 191966495057921L;
        if(requestDTO.getRegionId()== null){
            RegionEntity regionEntity = regionRepository.getCurrentRegion(customerId);
            requestDTO.setRegionId(regionEntity.getId());
        }
        //获取所有扫码应用
        List<AppGoodsEntity> appGoodsList = appGoodsRepository.getAppGoodsListByTypes();
        if (CollectionUtil.isEmpty(appGoodsList)) {
            log.error("未找到扫码提货应用信息");
            return BaseResponse.fail(Status.NOT_FIND_PAY_CODE.getCode(), Status.NOT_FIND_PAY_CODE.getMessage());
        }
        PickChannelAppResponseDTO selectedApp = null;
        for (AppGoodsEntity appGoodsEntity : appGoodsList) {
            boolean sellableInRegion = productSkuSellRegionService.isSellableInRegion(appGoodsEntity.getAppId(), null, ProductTypeEnum.APP,requestDTO.getRegionId());
            if (sellableInRegion) {
                PickChannelAppResponseDTO appResponseDTO = BeanUtil.toBean(appGoodsEntity, PickChannelAppResponseDTO.class);
                appResponseDTO.setIsSelected(0);
                if (Objects.nonNull(requestDTO.getAppId()) && appGoodsEntity.getAppId().longValue() == requestDTO.getAppId().longValue()) {
                    appResponseDTO.setIsSelected(1);
                    selectedApp = appResponseDTO;
                }
                channelApp.add(appResponseDTO);
            }
        }
        if (CollectionUtil.isEmpty(channelApp)) {
            log.error("所在区域没有可用的扫码提货付款码");
            return BaseResponse.fail(Status.REGION_NOT_FIND_PAY_CODE.getCode(), Status.REGION_NOT_FIND_PAY_CODE.getMessage());
        }
        if (Objects.isNull(selectedApp) && CollectionUtil.isNotEmpty(channelApp)) {
            selectedApp = channelApp.get(0);
            selectedApp.setIsSelected(1);
        }
        //获取选中应用的付款码
        if (Objects.nonNull(selectedApp)) {
            OfflineScanCodeRequestDTO scanCodeRequestDTO = new OfflineScanCodeRequestDTO();
            scanCodeRequestDTO.setAppId(selectedApp.getAppId());
            HomeScanNumberResponseDTO scanNumberResponseDTO = getOfflineCode(scanCodeRequestDTO);
            selectedApp.setPayCodeRes(scanNumberResponseDTO);
        }
        return BaseResponse.succeed(channelApp);
    }

    /**
     * 根据code查询用户信息
     *
     * @param requestDTO
     * @return
     */
    @Override
    public CustomerDetailResponse getCustomerEntityByCode(PickCodeRequestDTO requestDTO) {
        String code = requestDTO.getCode();
        String offScanCode = String.format(OFF_SCAN_CODE,code);
        String s = redisUtil.get(offScanCode);
        if(StrUtil.isBlank(s)){
            log.info("未找到用户{}的付款码", code);
            throw BizException.SCAN_NOT_USER;
        }
        HomeScanCodeResponseDTO bean = JSONUtil.toBean(s, HomeScanCodeResponseDTO.class);
        CustomerEntity customerEntity = customerRepository.selectCustomerById(bean.getCustomerId());
        if(customerEntity == null){
            log.error("未找到用户{}信息", bean.getCustomerId());
        }
        return BeanUtil.toBean(customerEntity, CustomerDetailResponse.class);
    }


    @Override
    public HomeScanNumberResponseDTO getOfflineCode(OfflineScanCodeRequestDTO request) {
        HomeScanNumberResponseDTO homeCode = new HomeScanNumberResponseDTO();
       // Long customerId = StpUtil.getLoginIdAsLong();
        Long customerId = 191966495057921L;
        Long appId = request.getAppId();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        vaildUser(customerEntity);
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(appId);
        if(appGoodsEntity == null){
            log.error("未找到应用ID为{}的应用信息", appId);
            return null;
        }
        // 应用类型
        Integer type = appGoodsEntity.getType();
        if (type == null || type == 1) {
            log.error("该应用类型暂不支持");
            return null;
        }
        if (type == 4) {
            if (StrUtil.isBlank(appGoodsEntity.getThirdId())) {
                log.error("分销平台id不能为空");
                return null;
            }
        }
        // 是否开通免密支付
        Integer openPasswordFreePayment = customerEntity.getOpenPasswordFreePayment();
        homeCode.setOpenPasswordFreePayment(openPasswordFreePayment);
        homeCode.setScanPayToast(appGoodsEntity.getScanPayToast());
        //获取redis ttl时间
        String redisUserCode = String.format(OFF_SCAN_USER_APP, customerId, appGoodsEntity.getAppId());
        String scanCode = redisUtil.get(redisUserCode);
        if(StrUtil.isNotBlank(scanCode)){
            Long ttl = redisUtil.getTtl(redisUserCode);
            if(ttl>0){
                homeCode.setExpireCodeTime(ttl.intValue());
                homeCode.setCode(scanCode);
                return homeCode;
            }
        }
        String code = null;
        switch (type) {
            //首页提货码
            case 6:
                code = getHomeCode();
                break;
            //提货分销码
            case 4:
                code = getPickCode( appGoodsEntity.getThirdId(),customerId);
                break;
            default:
                break;
        }
        homeCode.setCode(code);
        homeCode.setExpireCodeTime(codeExpireTime);
        //放入缓存
        setRedisUtil(code,appGoodsEntity,customerId);
        return homeCode;
    }

    //获取首页提货码9519开头 区分新福鲤圈
    private String getHomeCode() {
        String code17 = businessIdGenerator.generateFixed17DigitId();
        String code = "9519"+code17;
        return code;
    }

    /**
     * 获取分销付款码
     *
     * @return
     */
    private String getPickCode( String thirdId,Long customerId) {
        PickPaymentCodeRequestDTO request = new PickPaymentCodeRequestDTO();
        request.setNonce(IdUtil.simpleUUID());
        request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
        request.setThirdId(thirdId);
        request.setUserId(customerId);
        PickPaymentCodeResponseDTO paymentCode = pickScanCaseService.getPaymentCode(request);
        return paymentCode.getPaymentCode();
    }

    private void setRedisUtil(String code, AppGoodsEntity appGoodsEntity,Long customerId) {
        // 放入redis保存用户信息 保存1分钟（1分钟后过期码过期）
        String redisUserCode = String.format(OFF_SCAN_USER_APP,customerId,appGoodsEntity.getAppId());
        redisUtil.set(redisUserCode, code, codeExpireTime);
        //缓存用户id
        String offScanCode = String.format(OFF_SCAN_CODE,code);
        HomeScanCodeResponseDTO responseDTO = new HomeScanCodeResponseDTO();
        responseDTO.setCode(code);
        responseDTO.setAppId(appGoodsEntity.getAppId());
        responseDTO.setCustomerId(customerId);
        redisUtil.set(offScanCode, JSONUtil.toJsonStr(responseDTO), codeExpireTime);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ThirdOrderInfoResponseDTO getCashierVipOrder(PosOrderRequestDTO requestDTO) {

        ThirdOrderInfoResponseDTO responseDTO = new ThirdOrderInfoResponseDTO();
        String code = requestDTO.getCode();
        String offScanCode = String.format(OFF_SCAN_CODE,code);
        String dtoStr = redisUtil.get(offScanCode);
        if (StrUtil.isBlank(dtoStr)) {
            log.error("接口：getThirdVipOrder,支付码已过期,请重新获取:{}", code);
            throw new BizException("支付码已过期");
        }
        HomeScanCodeResponseDTO dto = JSONUtil.toBean(dtoStr, HomeScanCodeResponseDTO.class);
        Long customerId = dto.getCustomerId();
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null) {
            log.error("接口：getThirdVipOrder,未找到会员ID为{}的会员信息", customerId);
        }
        String key = String.format(OFF_PRE_ORDER, customerId, requestDTO.getOrderNo());
        String s = redisUtil.get(key);
        if (StrUtil.isNotBlank(s)) {
            log.error("接口：getThirdVipOrder,pos订单已存在,请勿重复提交:{}", code);
            throw new BizException("pos订单已存在");
        }

        Long appSpuId = dto.getAppId();
        AppGoodsEntity appGoodsEntity = appGoodsRepository.findAppGoodsById(appSpuId);
        if (appGoodsEntity == null) {
            log.error("接口：getThirdVipOrder,未找到应用ID为{}的应用信息", appSpuId);
            throw new BizException("未找到应用信息");
        }
        //获取收银台卡支付顺序
        OfflineCardSortRequest request = new OfflineCardSortRequest(customerId);
        OfflineCardSortResponse offlineCardSort = offlinePaymentGateway.findOfflineCardSort(request);
        //创建订单
        CreateOrderDTO createOrderDTO = saveOrderInfo(requestDTO, customerEntity, appGoodsEntity);
        OrderInfoEntity orderInfoEntity = orderCaseService.submitOrder(createOrderDTO,thirdHomeScanOrderInfoBuilder);
        //订单信息保存redis

        String jsonStr = JSONUtil.toJsonStr(orderInfoEntity);
        redisUtil.set(key, jsonStr, orderExpireTime);
        //返回pos信息
        responseDTO.setCustomerResponse(BeanUtil.toBean(customerEntity, CustomerDetailResponse.class));
        responseDTO.setOrderNo(orderInfoEntity.getOrderNo());
        responseDTO.setCardSort(offlineCardSort.getCardSort());
        List<OfflineCardSortResponse.CardInfo> cardInfo = offlineCardSort.getCardInfo();
        responseDTO.setCardInfo(BeanUtil.copyToList(cardInfo, ThirdOrderInfoResponseDTO.CardInfo.class));
        return responseDTO;
    }

    /**
     * 收银台返回支付结果
     *
     * @param request
     */
    @Override
    public void getCashierPayResult(PollPayRefundRequestDTO request) {
        //todo 支付结果更新订单信息
        String outOrderId = request.getOutOrderId();
        String payResultKey = String.format(CASHIER_PAY_RESULT, outOrderId);
        redisUtil.set(payResultKey, JSONUtil.toJsonStr(request), orderExpireTime);
    }

    private CreateOrderDTO saveOrderInfo(PosOrderRequestDTO requestDTO,
                                         CustomerEntity customerEntity,
                                         AppGoodsEntity appGoodsEntity
    ) {
        CreateOrderDTO request = new CreateOrderDTO();
        request.setCustomerId(customerEntity.getId());
        request.setCustomerMobile(customerEntity.getPhone());
        request.setProductType(ProductTypeEnum.APP.getType());
        request.setProductSpuId(appGoodsEntity.getAppId());
        request.setProductSkuId(0l);
        request.setExternalOrderNo(requestDTO.getOrderNo());
        request.setThirdId(appGoodsEntity.getThirdId());
        request.setExternalShopId(requestDTO.getExternalShopId());
        request.setExternalShopUserId(requestDTO.getExternalShopUserId());
       // request.setTradeNo(requestDTO.getTradeNo());
        request.setExternalAppProductPrice(requestDTO.getPrice());
        request.setRemark(requestDTO.getCode());

        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PollVipOrderResponseDTO pollPreOrder(PollPrePayRequestDTO requestDTO) {
        String code = requestDTO.getCode();
        Long customerId = StpUtil.getLoginIdAsLong();
        String redisKey = String.format(OFF_PRE_ORDER, customerId, code);
        //加入分布式锁 需测试接口性能
        boolean lockAcquired = false;
        try {
            String orderJson = redisUtil.get(redisKey);
            if (StrUtil.isBlank(orderJson)) {
                return null;
            }
            //获取后删除缓存，防止二次获取
            redisUtil.delete(redisKey);
            lockAcquired = distributedLock.tryLock(redisKey, 1, TimeUnit.SECONDS);
            OrderInfoEntity dto = JSONUtil.toBean(orderJson, OrderInfoEntity.class);
            if (customerId != dto.getCustomerId()) {
                log.info("接口：pollPreOrder,用户id：{},会员卡号：{}订单用户id不一致", customerId, code);
                throw new BizException("订单超时");
            }
            //获取客户信息
            CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
            //验证用户信息
            vaildUser(customerEntity);
            //检测支付密码
            Integer isPass = checkSetPayPass(customerEntity);
            //检测是否开启免密
            Integer isOpenPay = checkSetOpenPay(customerEntity);
            //验证门店信息 收营台一验证
            //  MerchantShopDetailResponseDTO shopDetail = merchantGateway.getShopDetail(dto.getExternalShopId());
            //   vaildShop(shopDetail);
            //订单失效时间 秒
            Long payExpireTimestamp = dto.getPayExpireTimestamp();
            Date jdkDate = DateUtil.date(payExpireTimestamp * 1000).toJdkDate();
            long between = DateUtil.between(new Date(),jdkDate, DateUnit.SECOND);
            if (between > 0) {
                log.info("接口：pollPreOrder,用户id：{},会员卡号：{}订单已过期", customerId, code);
                throw new BizException("订单已过期");
            }
            PollVipOrderResponseDTO responseDTO = new PollVipOrderResponseDTO();
            responseDTO.setIsPayPass(isPass);
            responseDTO.setIsOpenPay(isOpenPay);
            responseDTO.setOrderInfo(BeanUtil.toBean(dto, PollOrderInfoResponseDTO.class));
            //开启免密支付后，直接调用支付接口
            if(isOpenPay==1 && dto.getOrderAmount().compareTo(new BigDecimal("200")) != 1){
               //todo 调用支付接口，直接支付
            }

            return responseDTO;
        } catch (BizException e) {
            log.error("接口：pollPreOrder,用户id：{},会员卡号：{}订单异常", customerId, code, e);
            throw e;
        } catch (InterruptedException e) {
            log.error("接口：pollPreOrder,用户id：{},会员卡号：{}订单异常", customerId, code, e);
            throw new BizException("订单异常");
        } finally {
            if (lockAcquired && distributedLock.isLock(redisKey)) {
                distributedLock.unLock(redisKey);
            }
        }
    }

    /**
     * 4-收银台订单轮询结果
     *
     * @param request
     */
    @Override
    public PollOrderRefundResultResponseDTO pollPaymentRefund(PollPayRefundRequestDTO request) {

        return null;
    }


    //验证门店信息
    private void vaildShop(MerchantShopDetailResponseDTO shopDetail) {
        MerchantShopBaseResponseDTO base = shopDetail.getBase();
        if (base == null) {
            log.error("门店基础信息为空");
            throw new BizException("门店基础信息为空");
        }
        String isThShop = base.getIs_th_shop();
        if (!"1".equals(isThShop)) {
            log.error("[pollPreOrder] 获取门店信息失败，门店不是提货券商户，门店id：{}", base.getId());
            throw new BizException("门店不是提货券商户");
        }
        String isThOpen = base.getIs_th_open();
        if (!"1".equals(isThOpen)) {
            log.error("[pollPreOrder] 获取门店信息失败，门店未开启提货券支付，门店id：{}", base.getId());
            throw new BizException("门店未开启提货券支付");
        }
        MerchantShopConfigResponseDTO config = shopDetail.getConfig();
        if (config == null) {
            log.error("[pollPreOrder] 获取门店信息失败，门店未配置支付参数，门店id：{}", base.getId());
            throw new BizException("门店未配置支付参数");
        }
        String wechatPay = config.getWechat_pay();
        if ("1".equals(wechatPay)) {
            //验证微信支付参数
        }

    }

    private void vaildUser(CustomerEntity customerEntity) {
        if (customerEntity == null) {
            log.info("会员不存在");
            throw new BizException("会员不存在");
        }
        if (customerEntity.getDelFlag() == 1) {
            log.info("会员已删除");
            throw new BizException("会员已删除");
        }
        if (customerEntity.getStatus() != 1) {
            log.info("会员状态异常");
            throw new BizException("会员状态异常");
        }
        if (customerEntity.getPhone() == null) {
            log.info("会员手机号不存在");
            throw new BizException("会员手机号不存在");
        }
        if (customerEntity.getLoginStatus() == null || customerEntity.getLoginStatus() != 1) {
            log.info("登陆状态异常");
            throw new BizException("登陆状态异常");
        }
    }

    /**
     * 4判断是否设置支付密码
     *
     * @param customerEntity
     * @return
     */

    private Integer checkSetPayPass(CustomerEntity customerEntity) {
        String password = customerEntity.getPassword();
        if (StrUtil.isNotBlank(password)) {
            return 1;
        }
        return 0;
    }

    private Integer checkSetOpenPay(CustomerEntity customerEntity) {
        Integer openPasswordFreePayment = customerEntity.getOpenPasswordFreePayment();
        if (openPasswordFreePayment == null || openPasswordFreePayment == 0) {
            return 0;
        }
        return 1;
    }


    @Override
    public Boolean checkPassword(UserCheckPassRequestDTO requestDTO) {
        //1验证用户是否存在
        CustomerEntity customerEntity = customerRepository.selectCustomerById(requestDTO.getCustomerId());
        if (customerEntity == null) {
            throw new BizException("会员不存在");
        }
        //2验证是否锁定
        Boolean isLock = checkUserIsLock(requestDTO.getCustomerId());
        if (isLock) {
            //todo 提示用户锁定和重试时间
            throw new BizException("会员已锁定,请稍后再试");
        }
        //3验证是否免密(开启免密无需验证密码)
        Integer openFreePay = checkUserIsOpenFreePay(requestDTO.getCustomerId());
        if (openFreePay == 1) {
            return true;
        }
        //4用户·密码验证（获取支付密码）
        String password = customerEntity.getPassword();
        String md5Password = DigestUtils.md2Hex(password);
        if (!password.equals(md5Password)) {
            //todo 记录错误次数和错误时间，最好保存日志
            //todo 输入错误过多需要提示锁定，锁定时间
            //todo 提示用户输入错误几次，最后一次提示用户(下次错误将被锁定)
            throw new BizException("支付密码输入错误, 请重新输入");
        }

        return true;
    }


    public Integer checkUserIsOpenFreePay(Long customerId) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null || customerEntity.getStatus() == 0) {
            return 0;
        }
        return 1;
    }

    @Override
    public Boolean checkUserIsLock(Long customerId) {
        CustomerEntity customerEntity = customerRepository.selectCustomerById(customerId);
        if (customerEntity == null) {
            throw new RuntimeException("会员不存在");
        }
        //todo  获取会员是否锁定
        return true;
    }

    @Override
    public HomePayResultResponseDTO homeScanPay(UserHomePayRequestDTO requestDTO) {
        String orderNo = requestDTO.getOrderNo();
        Long customerId = requestDTO.getCustomerId();
        UserCheckPassRequestDTO request = new UserCheckPassRequestDTO();
        request.setCustomerId(requestDTO.getCustomerId());
        request.setPayPassword(requestDTO.getPayPassword());
        //检测密码-错误次数
        checkPassword(request);
        CustomerEntity customerEntity = customerRepository.selectCustomerById(requestDTO.getCustomerId());
        //检测用户状态
        vaildUser(customerEntity);
        //验证订单状态
        OrderInfoEntity orderInfoEntity = orderRepository.findByOrderNo(orderNo);
        vailOrder(orderInfoEntity, customerId);
        Integer flatProductType = orderInfoEntity.getFlatProductType();
        if (flatProductType.equals(FlatProductTypeEnum.SCAN_OFFLINE_APP.getType())) {
            //todo 首页扫码支付
        } else if (flatProductType.equals(FlatProductTypeEnum.SCAN_PICKUP_APP.getType())) {
            //todo 1展码付
            //todo 2订单支付结果回告
            // pickScanCaseService.
        }

        //todo 付款成功 变更订单状态
        HomePayResultResponseDTO dto = new HomePayResultResponseDTO();
        dto.setOrderStatus(null);
        dto.setOrderNo(orderNo);
        return dto;

    }

    private void vailOrder(OrderInfoEntity orderInfoEntity, long customerId) {
        if (orderInfoEntity == null) {
            throw new BizException("订单不存在");
        }
        if (orderInfoEntity.getCustomerId() != customerId) {
            throw new BizException("订单不属于当前用户");
        }
        if (orderInfoEntity.getOrderStatus() != 0) {
            log.info("订单状态异常：{}", orderInfoEntity.getOrderNo());
            throw new BizException("订单状态异常");
        }
        Long payExpireTimestamp = orderInfoEntity.getPayExpireTimestamp();
        Date jdkDate = DateUtil.date(payExpireTimestamp * 1000).toJdkDate();
        long between = DateUtil.between(new Date(),jdkDate, DateUnit.SECOND);
        if (payExpireTimestamp == null || between > 0) {
            log.info("订单已过期：{}", orderInfoEntity.getOrderNo());
            throw new BizException("订单已过期");
        }
    }

}
