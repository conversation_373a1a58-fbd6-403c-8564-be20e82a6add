package com.jsrxjt.mobile.infra.order.persistent.po;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后单关联订单项查询结果PO
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
@Setter
public class AfterSaleWithItemPO {
    
    // 售后单信息
    private Long afterSaleId;
    private String afterSaleNo;
    private String orderNo;
    private Integer afterSaleType;
    private Integer afterSaleStatus;
    private Integer refundStatus;
    private BigDecimal refundAmount;
    private Integer afterSaleQuantity;
    private LocalDateTime createTime;
    
    // 订单项信息
    private String productName;
    private String brandName;
    private String productLogo;

    private BigDecimal sellPrice;

    private BigDecimal faceAmount;

    private Integer quantity;
}