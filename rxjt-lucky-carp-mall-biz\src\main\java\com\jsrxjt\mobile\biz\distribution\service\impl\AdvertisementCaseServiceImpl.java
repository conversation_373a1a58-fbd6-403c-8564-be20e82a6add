package com.jsrxjt.mobile.biz.distribution.service.impl;

import com.jsrxjt.mobile.api.distribution.dto.request.AdvertisementRequestDto;
import com.jsrxjt.mobile.biz.distribution.service.AdvertisementCaseService;
import com.jsrxjt.mobile.domain.advertisement.repository.AdvertisementRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Description: 广告服务
 * @Author: ywt
 * @Date: 2025-06-10 16:01
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class AdvertisementCaseServiceImpl implements AdvertisementCaseService {
    private final AdvertisementRepository advertisementRepository;

    @Override
    public boolean clickAdvertisement(AdvertisementRequestDto requestDTO) {
        return advertisementRepository.clickAdvertisement(requestDTO.getAdvId());
    }
}
