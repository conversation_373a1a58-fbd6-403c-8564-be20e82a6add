<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.packages.persistent.mapper.PackageGoodsSkuMapper">

    <update id="updatePackageGoodsSkuStatus" parameterType="List">
        <foreach collection="list" item="item">
            UPDATE package_sku
            SET package_sku_status = #{item.packageSkuStatus}
            WHERE id = #{item.id};
        </foreach>
    </update>

    <update id="increaseSoldNum">
        UPDATE package_sku
        SET sold_num = COALESCE(sold_num, 0) + #{quantity},
            mod_time = NOW()
        WHERE id = #{packageSkuId}
    </update>

</mapper>
