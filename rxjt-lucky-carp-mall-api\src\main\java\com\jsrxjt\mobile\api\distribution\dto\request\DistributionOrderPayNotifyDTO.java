package com.jsrxjt.mobile.api.distribution.dto.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/7/29 14:28
 */
@Data
public class DistributionOrderPayNotifyDTO {
    /**
     * 分销应用类型
     */
    private String distributionType;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 订单交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 支付完成时间
     */
    private String payTime;
}
