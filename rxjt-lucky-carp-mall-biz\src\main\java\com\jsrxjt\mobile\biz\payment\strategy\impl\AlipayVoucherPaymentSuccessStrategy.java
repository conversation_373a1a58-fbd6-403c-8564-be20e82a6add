package com.jsrxjt.mobile.biz.payment.strategy.impl;

import com.jsrxjt.mobile.api.enums.DeliveryStatusEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleOperationTypeEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleStatusEnum;
import com.jsrxjt.mobile.api.order.types.AfterSaleTypeEnum;
import com.jsrxjt.mobile.biz.payment.strategy.PaymentSuccessStrategy;
import com.jsrxjt.mobile.domain.alipay.gateway.AlipayVoucherGateway;
import com.jsrxjt.mobile.domain.alipay.request.AlipayVoucherSendRequest;
import com.jsrxjt.mobile.domain.alipay.response.AlipayVoucherSendResponse;
import com.jsrxjt.mobile.domain.order.entity.*;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleLogRepository;
import com.jsrxjt.mobile.domain.order.repository.AfterSaleRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderDeliveryRepository;
import com.jsrxjt.mobile.domain.order.repository.OrderRepository;
import com.jsrxjt.mobile.domain.order.service.AfterSaleLogService;
import com.jsrxjt.mobile.domain.order.service.AfterSaleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


/**
 * 支付宝红包支付成功处理策略（基于flatProductType）
 * 支持 flatProductType = 402 的类型
 *
 * <AUTHOR>
 * @Date 2025/8/5
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AlipayVoucherPaymentSuccessStrategy implements PaymentSuccessStrategy {

    private final OrderRepository orderRepository;

    private final OrderDeliveryRepository orderDeliveryRepository;

    private final AlipayVoucherGateway alipayVoucherGateway;

    private final AfterSaleService afterSaleService;

    private final AfterSaleLogService afterSaleLogService;

    private final AfterSaleRepository afterSaleRepository;

    private final AfterSaleLogRepository afterSaleLogRepository;


    @Override
    public boolean supports(Integer flatProductType) {
        // 仅支持 flatProductType == 402
        return flatProductType != null && flatProductType == 402;
    }

    @Override
    public void handle(OrderInfoEntity order) {
        log.info("开始处理支付宝红包订单支付成功，订单号：{}，扁平化产品类型：{}",
                order.getOrderNo(), order.getFlatProductType());

        // 1. 支付宝下单
        AlipayVoucherSendResponse alipayVoucherSendResponse = pushAlipayVoucherOrder(order);

        // 2. 下单成功-更新订单状态为已发货，并记录外部订单号
        //    下单失败-更新订单状态为充值失败，自动生成售后订单
        if (alipayVoucherSendResponse != null && StringUtils.isNotEmpty(alipayVoucherSendResponse.getOutVoucherId())){
            log.info("支付宝红包发放成功，订单号：{}，外部订单号：{}", order.getOrderNo(), alipayVoucherSendResponse.getOutOrderId());
            updateOrderToDelivering(order, alipayVoucherSendResponse.getOutOrderId());
            saveDeliveryInfo(order, alipayVoucherSendResponse);
        } else {
            log.info("支付宝红包发放失败，订单号：{}", order.getOrderNo());
            updateOrderToRechargeFailed(order);
            createAfterSaleForRechargeFailed(order);
        }
        log.info("支付宝红包订单支付成功处理完成，订单号：{}，外部订单号：{}",
                order.getOrderNo(), alipayVoucherSendResponse.getOutOrderId());
    }

    /**
     * 支付宝下单发券
     */
    private AlipayVoucherSendResponse pushAlipayVoucherOrder(OrderInfoEntity order) {
        AlipayVoucherSendRequest alipayVoucherSendRequest = new AlipayVoucherSendRequest();
        alipayVoucherSendRequest.setOrderNo(order.getOrderNo());
        alipayVoucherSendRequest.setOutActivityId(order.getOrderItems().get(0).getOutGoodsId());
        alipayVoucherSendRequest.setLogonId(order.getRechargeAccount());
        return alipayVoucherGateway.sendAlipayVoucher(alipayVoucherSendRequest);
    }

    /**
     * 更新订单状态为交易完成  发货状态为已发货
     */
    private void updateOrderToDelivering(OrderInfoEntity order, String externalOrderNo) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setExternalOrderNo(externalOrderNo);
        updateOrder.setOrderStatus(OrderStatusEnum.TRADE_SUCCESS.getCode());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        updateOrder.setDeliveryTime(LocalDateTime.now());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);

        log.info("支付宝红包订单发货状态更新成功，订单号：{}，发货状态：{}，外部订单号：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERING.getDescription(), externalOrderNo);
    }

    /**
     * 保存发货信息到t_order_delivery表
     */
    private void saveDeliveryInfo(OrderInfoEntity orderInfo,AlipayVoucherSendResponse alipayVoucherSendResponse) {
        log.info("[数据保存]开始保存支付宝红包发货信息，订单号：{}", orderInfo.getOrderNo());

        // 订单项记录
        OrderItemEntity orderItem = orderInfo.getOrderItems().get(0);

        OrderDeliveryEntity deliveryEntity = new OrderDeliveryEntity();

        // 基本订单信息
        deliveryEntity.setOrderId(orderInfo.getId());
        deliveryEntity.setOrderNo(orderInfo.getOrderNo());
        deliveryEntity.setOrderItemId(orderItem.getId());
        deliveryEntity.setMiniSkuId(orderItem.getProductSkuId());

        // 发货信息
        deliveryEntity.setDeliveryType(3);
        deliveryEntity.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode().intValue());
        deliveryEntity.setDeliveryTime(LocalDateTime.now());

        // 券信息
        deliveryEntity.setCouponCode(alipayVoucherSendResponse.getOutVoucherId());

        // 收货信息（从订单信息复制）
        deliveryEntity.setReceiverName(orderInfo.getReceiverName());
        deliveryEntity.setReceiverMobile(orderInfo.getReceiverMobile());
        deliveryEntity.setReceiverAddress(orderInfo.getReceiverAddress());
        deliveryEntity.setRechargeAccount(orderInfo.getRechargeAccount());

        // 发货备注
       deliveryEntity.setDeliveryRemark("红包自动发放");

       try {
           orderDeliveryRepository.saveOrderDelivery(deliveryEntity);
           log.info("[数据保存]支付宝红包发货信息保存成功，订单号：{}", orderInfo.getOrderNo());
       } catch (Exception e) {
           log.error("[数据保存]支付宝红包发货信息保存失败，订单号：{}，错误信息：{}", orderInfo.getOrderNo(), e.getMessage(), e);
           throw e;
        }
    }

    /**
     * 更新订单状态为发货失败
     */
    private void updateOrderToRechargeFailed(OrderInfoEntity order) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(order.getId());
        updateOrder.setCustomerId(order.getCustomerId());
        updateOrder.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode().intValue());
        updateOrder.setAfterSaleStatus(AfterSaleStatusEnum.PENDING_AUDIT.getCode());
        updateOrder.setModTime(LocalDateTime.now());

        orderRepository.updateOrder(updateOrder);

        log.info("订单发货状态更新为发货失败，订单号：{}，发货状态：{}",
                order.getOrderNo(), DeliveryStatusEnum.DELIVERY_FAILED.getDescription());
    }

    /**
     * 发放失败时自动创建售后单
     *
     * @param orderInfo 订单信息
     */
    private void createAfterSaleForRechargeFailed(OrderInfoEntity orderInfo) {
        log.info("开始为支付宝红包发放失败订单创建售后单，订单号：{}", orderInfo.getOrderNo());
        // 构建售后申请请求
        AfterSaleApplyRequest applyRequest = new AfterSaleApplyRequest();
        applyRequest.setOrderNo(orderInfo.getOrderNo());
        applyRequest.setCustomerId(orderInfo.getCustomerId());
        applyRequest.setAfterSaleType(AfterSaleTypeEnum.FULL_REFUND.getCode()); // 全额退款
        applyRequest.setAfterSaleQuantity(orderInfo.getOrderItems().get(0).getQuantity());
        applyRequest.setApplyRefundAmount(orderInfo.getPaymentAmount());
        applyRequest.setAfterSaleReason("发放失败");
        applyRequest.setRefundDescription("系统检测到发放失败，自动申请售后退款");
        applyRequest.setCustomerRemark("发放失败自动申请");

        // 调用售后领域服务创建售后单
        AfterSaleEntity afterSale = afterSaleService.applyAfterSale(orderInfo, applyRequest);

        // 保存售后单
        afterSaleRepository.save(afterSale);

        // 创建售后日志
        AfterSaleLogEntity afterSaleLog = afterSaleLogService.createAfterSaleLog(
                afterSale,
                AfterSaleOperationTypeEnum.APPLY_AFTER_SALE.getCode(),
                "发放失败，系统自动申请售后",
                "系统",
                1L);
        afterSaleLogRepository.save(afterSaleLog);

        // 更新订单的售后状态
        syncUpdateOrderAfterSaleStatus(orderInfo,afterSale);


        log.info ("支付宝红包发放失败售后单创建成功，订单号：{}，售后单号：{}",
                orderInfo.getOrderNo(), afterSale.getAfterSaleNo());
    }


    private void syncUpdateOrderAfterSaleStatus(OrderInfoEntity mainOrder, AfterSaleEntity afterSale) {
        OrderInfoEntity updateOrder = new OrderInfoEntity();
        updateOrder.setId(mainOrder.getId());
        updateOrder.setCustomerId(mainOrder.getCustomerId());
        updateOrder.setAfterSaleStatus(afterSale.getAfterSaleStatus());
        updateOrder.setModTime(LocalDateTime.now());
        orderRepository.updateOrder(updateOrder);
    }
}