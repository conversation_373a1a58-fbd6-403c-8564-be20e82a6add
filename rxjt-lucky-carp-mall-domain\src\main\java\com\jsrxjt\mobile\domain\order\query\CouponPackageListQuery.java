package com.jsrxjt.mobile.domain.order.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class CouponPackageListQuery {

    /**
     * 客户ID
     */
    private Long customerId;

    private String queryName;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 扁平化类型
     */
    private List<Integer> flatProductTypeList;

    /**
     * 品牌列表
     */
    private List<Integer> brandList;

    /**
     * 品牌id
     */
    private List<Long> brandIdList;
}

