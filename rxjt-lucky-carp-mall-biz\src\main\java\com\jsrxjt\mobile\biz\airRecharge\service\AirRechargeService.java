package com.jsrxjt.mobile.biz.airRecharge.service;

import com.jsrxjt.common.core.vo.BaseResponse;
import com.jsrxjt.mobile.api.airRecharge.AirRechargeRequest;
import com.jsrxjt.mobile.api.customer.response.AirRechargeInfoResponse;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * @Description: 空中充值
 * @Author: ywt
 * @Date: 2025-08-26 15:05
 * @Version: 1.0
 */
public interface AirRechargeService {
    AirRechargeInfoResponse getAirRechargeInfo();
    void updateAirRechargeStatus(AirRechargeRequest request);
}
