<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsrxjt.mobile.infra.packages.persistent.mapper.PackageSubSkuMapper">

    <update id="updatePackageSubSkuStatus" parameterType="List">
        <foreach collection="list" item="item">
            UPDATE package_sub_sku
            SET amount = #{item.amount}
                <if test="item.subSkuStatus != null">
                    ,sub_sku_status = #{item.subSkuStatus}
                </if>
                <if test="item.centerStatus != null">
                    ,center_status = #{item.centerStatus}
                </if>
                <if test="item.inventory!= null">
                    ,inventory = #{item.inventory}
                </if>
                <if test="item.pnType != null">
                    ,pn_type = #{item.pnType}
                </if>
                <if test="item.accountType != null">
                    ,account_type = #{item.accountType}
                </if>
                <if test="item.rationSaleNum != null">
                    ,ration_sale_num = #{item.rationSaleNum}
                </if>
                <if test="item.price != null">
                    ,price = #{item.price}
                </if>
                <if test="item.costPrice != null">
                    ,cost_price = #{item.costPrice}
                </if>
            WHERE id = #{item.id};
        </foreach>
    </update>

    <update id="increaseSoldNum">
        UPDATE package_sub_sku
        SET sold_num = COALESCE(sold_num, 0) + #{quantity},
            mod_time = NOW()
        WHERE id = #{packageSubSkuId}
    </update>

</mapper>
