package com.jsrxjt.mobile.domain.order.repository;

import com.jsrxjt.mobile.api.common.PageDTO;
import com.jsrxjt.mobile.api.enums.OrderChannelEnum;
import com.jsrxjt.mobile.api.enums.OrderStatusEnum;
import com.jsrxjt.mobile.api.enums.PaymentStatusEnum;
import com.jsrxjt.mobile.domain.order.entity.OrderInfoEntity;
import com.jsrxjt.mobile.domain.order.entity.SubSkuOrderEntity;
import com.jsrxjt.mobile.domain.order.query.OrderListQuery;

/**
 * 订单相关模块
 */
public interface OrderRepository {
    /**
     * 保存订单信息
     * 
     * @param order
     */
    void saveOrder(OrderInfoEntity order);

    /**
     * 根据订单号查询订单信息
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    OrderInfoEntity findByOrderNo(String orderNo);

    /**
     * 更新订单信息
     * 
     * @param order 订单信息
     */
    void updateOrder(OrderInfoEntity order);

    /**
     * 根据外部订单号更新订单信息
     *
     * @param order 订单信息
     */
    void updateOrderByExternalOrderNo(OrderInfoEntity order);

    /**
     * 根据当前订单状态和支付状态，更新订单信息
     * @param order 订单信息
     * @param currentPaymentStatus 当前支付状态
     * @param currentOrderStatus 当前订单状态
     */
    void updateOrderCheckCurrentStatus(OrderInfoEntity order, PaymentStatusEnum currentPaymentStatus,
                                       OrderStatusEnum currentOrderStatus);

    /**
     * 分页查询订单列表
     * 
     * @param query 查询条件
     * @return 订单列表分页数据
     */
    PageDTO<OrderInfoEntity> findOrderListByPage(OrderListQuery query);
    /**
     * 更新子SKU订单信息
     *
     * @param  updateSubSku 待更新的子SKU订单属性
     */

    void updateSubSkuOrder(SubSkuOrderEntity updateSubSku);

    /**
     * 统计指定订单状态的订单数量
     * @param customerId 客户id
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    int countOrderByStatus(Long customerId,OrderStatusEnum orderStatus);

    /**
     * 根据客户id、充值时间范围、扁平化产品类型   分页查询订单列表
     * @param query 查询条件
     * @param flatProductType 扁平化产品类型
     * @return 订单列表
     */
    PageDTO<OrderInfoEntity> findByFlatProductTypeAndDateRange(OrderListQuery query, Integer flatProductType, Long productSpuId);

    /**
     * 获取指定客户最后一次充值的账户
     * @param customerId 客户id
     * @param orderChannel 订单来源渠道
     * @return 充值账号
     */
    String getLastRechargeAccount(Long customerId, OrderChannelEnum orderChannel);

    /**
     * 根据外部订单号和交易号查询订单信息
     * @param externalOrderNo
     * @param tradeNo
     * @return
     */
    OrderInfoEntity findOrderByExternalOrderNoAndTradeNo(String externalOrderNo, String tradeNo);

}
