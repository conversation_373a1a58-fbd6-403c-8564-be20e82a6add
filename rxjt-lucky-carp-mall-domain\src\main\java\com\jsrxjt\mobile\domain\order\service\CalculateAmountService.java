package com.jsrxjt.mobile.domain.order.service;

import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.domain.order.entity.ExceedCalculationResult;
import com.jsrxjt.mobile.domain.order.entity.OrderAmountResult;
import com.jsrxjt.mobile.domain.order.entity.PurchaseInfo;
import com.jsrxjt.mobile.domain.order.entity.ServiceFeeCalculationResult;

import java.math.BigDecimal;

/**
 * 计算金额服务接口
 * 
 * <AUTHOR> Fengping
 * @since 2025/5/27
 **/
public interface CalculateAmountService {
    /**
     * 计算产品加点手续费费
     * 
     * @param purchaseInfo 购买信息
     * @return 加点手续费计算结果，包含总手续费、1件商品的手续费，手续费百分比等信息
     */
    ServiceFeeCalculationResult calculateServiceFee(PurchaseInfo purchaseInfo);

    /**
     * 计算超额手续费
     * 
     * @param purchaseInfo 购买信息
     * @return 超额计算结果，包含超额金额、手续费、百分比等信息
     */
    ExceedCalculationResult calculateExceedFee(PurchaseInfo purchaseInfo);

    /**
     * 计算订单金额
     * 
     * @param purchaseInfo 购买信息
     * @return 订单金额计算结果
     */
    OrderAmountResult calculateOrderAmount(PurchaseInfo purchaseInfo);

    /**
     * 更新用户月度消费金额
     * 
     * @param customerId    用户ID
     * @param productItemId 产品项id
     * @param amount        金额
     * @param operation     操作类型
     */
    void updateMonthlyPurchaseAmount(Long customerId, ProductItemId productItemId, BigDecimal amount, String operation);

    /**
     * 获取用户当月产品已购买数量
     * 
     * @param customerId    用户ID
     * @param productItemId 产品项id
     * @return 当月已购买数量
     */
    Integer getMonthlyPurchaseQuantity(Long customerId, ProductItemId productItemId);

    /**
     * 更新用户当月产品购买数量
     * 
     * @param customerId    用户ID
     * @param productItemId 产品项id
     * @param quantity      数量
     * @param operation     操作类型 (ADD/SUBTRACT)
     */
    void updateMonthlyPurchaseQuantity(Long customerId, ProductItemId productItemId, Integer quantity,
            String operation);
}
