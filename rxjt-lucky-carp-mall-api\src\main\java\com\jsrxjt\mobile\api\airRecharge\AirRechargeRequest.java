package com.jsrxjt.mobile.api.airRecharge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 空中充值请求
 * @Author: ywt
 * @Date: 2025-08-26 17:10
 * @Version: 1.0
 */
@Data
@Schema(description = "空中充值请求参数")
public class AirRechargeRequest {
    @Schema(description = "空中充值的主键id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键id不能为空")
    private Integer id;
}
